# NEUROGLYPH Conceptual Language Specification v2.0

## 🎯 **VISIONE FONDAMENTALE**

NEUROGLYPH è il primo **linguaggio concettuale puro** della storia, dove:

- **Simbolo = Concetto atomico indivisibile**
- **Token = Unità semantica con ID univoco**  
- **Codice = Sequenza di concetti logici**
- **AST = Struttura semantica universale**
- **Parsing = Riconoscimento concettuale diretto**

## 🧠 **PRINCIPI CONCETTUALI**

### **1. ATOMICITÀ SEMANTICA**
```
∀ → CONCEPT_UNIVERSAL_QUANTIFICATION (ID: 1001)
⇒ → CONCEPT_MATERIAL_IMPLICATION (ID: 1002)  
∧ → CONCEPT_LOGICAL_CONJUNCTION (ID: 1003)
```

Ogni simbolo Unicode corrisponde a **esattamente un concetto** indivisibile.

### **2. PUREZZA LOGICA**
```
NEUROGLYPH: ∀x ∈ ℝ: x² ≥ 0
SEMANTICA: UniversalQuantification(
    variable=x,
    domain=RealNumbers,
    proposition=GreaterEqual(Square(x), Zero)
)
```

Il codice esprime **significato logico diretto**, non sintassi di linguaggi esistenti.

### **3. UNIVERSALITÀ CONCETTUALE**
```
NEUROGLYPH → ConceptualAST → Python
NEUROGLYPH → ConceptualAST → Rust  
NEUROGLYPH → ConceptualAST → Coq
NEUROGLYPH → ConceptualAST → Lean
```

L'AST concettuale è **indipendente da qualunque linguaggio target**.

## 📚 **REGISTRY CONCETTUALE COMPLETO**

### **CATEGORIE SEMANTICHE**

#### **🔢 QUANTIFICATORI (Quantifiers)**
| Simbolo | Concept ID | Semantica | Arity |
|---------|------------|-----------|-------|
| ∀ | 1001 | universal_quantification | 3 |
| ∃ | 1002 | existential_quantification | 3 |

#### **🔗 CONNETTIVI LOGICI (Logical Connectives)**
| Simbolo | Concept ID | Semantica | Arity |
|---------|------------|-----------|-------|
| ⇒ | 1101 | material_implication | 2 |
| ⇔ | 1102 | biconditional | 2 |
| ∧ | 1103 | logical_conjunction | 2 |
| ∨ | 1104 | logical_disjunction | 2 |
| ¬ | 1105 | logical_negation | 1 |
| ≡ | 1106 | logical_equivalence | 2 |

#### **⊢ INFERENZA (Inference)**
| Simbolo | Concept ID | Semantica | Arity |
|---------|------------|-----------|-------|
| ⊢ | 1201 | syntactic_entailment | 2 |
| ⊨ | 1202 | semantic_entailment | 2 |
| ⊭ | 1203 | does_not_entail | 2 |
| ⊤ | 1204 | logical_truth | 0 |
| ⊥ | 1205 | logical_falsehood | 0 |

#### **∈ TEORIA DEGLI INSIEMI (Set Theory)**
| Simbolo | Concept ID | Semantica | Arity |
|---------|------------|-----------|-------|
| ∈ | 1301 | set_membership | 2 |
| ∉ | 1302 | not_set_membership | 2 |
| ⊂ | 1303 | proper_subset | 2 |
| ⊆ | 1304 | subset_or_equal | 2 |
| ⊇ | 1305 | superset_or_equal | 2 |
| ∩ | 1306 | set_intersection | 2 |
| ∪ | 1307 | set_union | 2 |
| ∅ | 1308 | empty_set | 0 |

#### **∫ MATEMATICA (Mathematics)**
| Simbolo | Concept ID | Semantica | Arity |
|---------|------------|-----------|-------|
| ∫ | 1401 | definite_integral | 3 |
| ∑ | 1402 | summation | 3 |
| ∏ | 1403 | product | 3 |
| ∂ | 1404 | partial_derivative | 2 |
| ∇ | 1405 | gradient | 1 |
| √ | 1406 | square_root | 1 |
| ∞ | 1407 | infinity | 0 |

#### **🧠 META-CONCETTI (Meta-Concepts)**
| Simbolo | Concept ID | Semantica | Arity |
|---------|------------|-----------|-------|
| 🧠 | 1501 | reasoning_process | 1 |
| ⚡ | 1502 | function_concept | 1 |
| 🔄 | 1503 | iterative_process | 1 |
| 🏛 | 1504 | structural_concept | 1 |
| 📖 | 1505 | knowledge_concept | 1 |
| 🔮 | 1506 | prediction_concept | 1 |

## 🔤 **GRAMMAR SEMANTICA EBNF**

```ebnf
// NEUROGLYPH Conceptual Grammar v2.0
// Ogni regola corrisponde a un pattern concettuale

conceptual_expression: quantified_concept
                     | logical_concept
                     | mathematical_concept
                     | set_concept
                     | inference_concept
                     | meta_concept
                     | atomic_concept

// Quantificazione: ∀x ∈ A: P(x)
quantified_concept: UNIVERSAL_QUANTIFIER variable domain_restriction concept_body
                  | EXISTENTIAL_QUANTIFIER variable domain_restriction concept_body

domain_restriction: "∈" set_concept | ":" type_concept | ε

// Logica proposizionale: P ⇒ Q, P ∧ Q ∨ R
logical_concept: concept_operand IMPLICATION concept_operand
               | concept_operand BICONDITIONAL concept_operand
               | concept_operand CONJUNCTION concept_operand
               | concept_operand DISJUNCTION concept_operand
               | NEGATION concept_operand
               | "(" logical_concept ")"

// Inferenza: P ⊢ Q, {P, P ⇒ Q} ⊨ Q
inference_concept: premise_set SYNTACTIC_ENTAILMENT conclusion
                 | premise_set SEMANTIC_ENTAILMENT conclusion

premise_set: concept_operand | "{" concept_list "}"
concept_list: concept_operand ("," concept_operand)*

// Matematica: ∫₀¹ f(x) dx, ∑ᵢ₌₁ⁿ aᵢ
mathematical_concept: integral_concept
                    | summation_concept
                    | derivative_concept

integral_concept: INTEGRAL bounds function_expression differential
summation_concept: SUMMATION bounds expression
derivative_concept: PARTIAL_DERIVATIVE expression "/" PARTIAL_DERIVATIVE variable

// Set theory: x ∈ A, A ⊂ B, {1,2,3}
set_concept: concept_operand SET_MEMBERSHIP concept_operand
           | concept_operand SUBSET concept_operand
           | concept_operand SET_INTERSECTION concept_operand
           | concept_operand SET_UNION concept_operand
           | EMPTY_SET
           | set_literal

set_literal: "{" [concept_list] "}"

// Meta-concetti: 🧠 P ⇒ Q, ⚡ factorial
meta_concept: REASONING_SYMBOL conceptual_expression
            | FUNCTION_SYMBOL function_application
            | ITERATION_SYMBOL iterative_expression

// Atomi concettuali
atomic_concept: variable | literal | constant

// Tokens concettuali (mappati da registry)
UNIVERSAL_QUANTIFIER: "∀"
EXISTENTIAL_QUANTIFIER: "∃"
IMPLICATION: "⇒"
BICONDITIONAL: "⇔"
CONJUNCTION: "∧"
DISJUNCTION: "∨"
NEGATION: "¬"
SYNTACTIC_ENTAILMENT: "⊢"
SEMANTIC_ENTAILMENT: "⊨"
SET_MEMBERSHIP: "∈"
SUBSET: "⊂"
SET_INTERSECTION: "∩"
SET_UNION: "∪"
EMPTY_SET: "∅"
INTEGRAL: "∫"
SUMMATION: "∑"
PARTIAL_DERIVATIVE: "∂"
REASONING_SYMBOL: "🧠"
FUNCTION_SYMBOL: "⚡"
ITERATION_SYMBOL: "🔄"

// Letterali
variable: /[a-zA-Z][a-zA-Z0-9_]*/
literal: number | string
number: /[0-9]+(\.[0-9]+)?/
string: /"[^"]*"/
```

## 🏗️ **AST CONCETTUALE**

### **Struttura Base**
```python
@dataclass(frozen=True)
class ConceptualAST:
    """AST semantico puro - immutabile e universale."""
    root_concept: Concept
    semantic_relations: List[ConceptRelation]
    logical_structure: LogicalStructure
    concept_metadata: ConceptMetadata

@dataclass(frozen=True)
class Concept(ABC):
    """Concetto atomico base."""
    concept_id: int
    symbol: str
    semantic_type: str
    arity: int
    
    @abstractmethod
    def semantic_meaning(self) -> str:
        """Restituisce significato semantico del concetto."""
        pass
    
    @abstractmethod
    def logical_strength(self) -> float:
        """Restituisce forza logica del concetto (0.0-1.0)."""
        pass
```

### **Concetti Specifici**
```python
@dataclass(frozen=True)
class UniversalQuantification(Concept):
    """Quantificazione universale: ∀x ∈ A: P(x)"""
    variable: Variable
    domain: Domain
    body: Concept
    
    def semantic_meaning(self) -> str:
        return f"For all {self.variable} in {self.domain}: {self.body}"
    
    def logical_strength(self) -> float:
        return 1.0  # Quantificazione universale ha forza massima

@dataclass(frozen=True)
class MaterialImplication(Concept):
    """Implicazione materiale: P ⇒ Q"""
    premise: Concept
    conclusion: Concept
    
    def semantic_meaning(self) -> str:
        return f"If {self.premise} then {self.conclusion}"
    
    def logical_strength(self) -> float:
        return 0.8  # Implicazione ha forza alta ma non massima

@dataclass(frozen=True)
class SyntacticEntailment(Concept):
    """Conseguenza sintattica: P ⊢ Q"""
    premises: List[Concept]
    conclusion: Concept
    
    def semantic_meaning(self) -> str:
        premise_str = ", ".join(str(p) for p in self.premises)
        return f"From {premise_str} syntactically follows {self.conclusion}"
    
    def logical_strength(self) -> float:
        return 1.0  # Conseguenza sintattica è certa
```

## 🔄 **EQUIVALENZA SEMANTICA**

### **Criteri di Equivalenza**
Due AST concettuali sono semanticamente equivalenti se:

1. **Isomorfismo Strutturale**: Stessa struttura logica
2. **Equivalenza Concettuale**: Stessi concetti atomici
3. **Preservazione Semantica**: Stesso significato logico
4. **Invarianza Sintattica**: Indipendente da rappresentazione

### **Algoritmo di Confronto**
```python
def semantically_equivalent(ast1: ConceptualAST, ast2: ConceptualAST) -> bool:
    """Verifica equivalenza semantica tra AST concettuali."""
    
    # 1. Normalizzazione canonica
    norm1 = normalize_conceptual_ast(ast1)
    norm2 = normalize_conceptual_ast(ast2)
    
    # 2. Confronto strutturale
    if not structural_isomorphism(norm1, norm2):
        return False
    
    # 3. Confronto concettuale
    if not conceptual_equivalence(norm1, norm2):
        return False
    
    # 4. Verifica semantica
    return semantic_preservation(norm1, norm2)
```

## 🎯 **OBIETTIVI DI VALIDAZIONE**

| Metrica | Target | Descrizione |
|---------|--------|-------------|
| **Concept Coverage** | 100% | Tutti i simboli registry supportati |
| **Semantic Fidelity** | ≥95% | Roundtrip semantico preservato |
| **Atomic Tokenization** | 100% | Zero-splitting garantito |
| **Logical Consistency** | ≥98% | Coerenza logica AST |
| **Conceptual Purity** | 100% | Indipendenza da linguaggi target |

Questa specifica definisce NEUROGLYPH come **linguaggio concettuale puro**, base per l'implementazione del primo sistema di reasoning simbolico veramente semantico.
