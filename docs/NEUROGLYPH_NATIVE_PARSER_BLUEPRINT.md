# NEUROGLYPH Native Parser Blueprint - Strategia Suprema

## 🎯 **VISIONE STRATEGICA**

NEUROGLYPH → NEUROGLYPH rappresenta la **strategia suprema** per creare il primo LLM veramente simbolico:

- **🧠 Semantica Pura**: Interpreti logica, pattern, funzioni in modo agnostico rispetto a Python
- **♻️ Roundtrip Perfetto**: Nessuna perdita semantica, zero fallback a stringhe  
- **🛠️ Portabilità**: Il sistema può generare Python, Rust, C++, ecc. dall'AST simbolico
- **🤖 LLM Training**: L'LLM impara a pensare in simboli, non in codice verboso
- **🧪 Validazione**: Confronti AST usando solo regole simboliche

## 🏗️ **ARCHITETTURA MODULARE**

```
neuroglyph/
├─ parser/                       # tutto il parsing simbolico
│  ├─ grammar.lark               # grammatica EBNF per Lark
│  ├─ lexer.py                   # definizione token
│  └─ parser.py                  # invoca Lark e costruisce NGAST
│
├─ ast/                          # definizione dell'AST simbolico
│  ├─ nodes.py                   # classi @dataclass per i nodi
│  ├─ transformers.py            # normalizzazione/canonicalizzazione
│  └─ visitor.py                 # traversali generici
│
├─ unparser/                     # da AST a codice NEUROGLYPH
│  ├─ serializer.py              # generazione stringa da NGAST
│  └─ canonical_style.py         # regole di formattazione
│
├─ transpilers/                  # opzionale: NGAST → linguaggi target
│  └─ to_python.py               # mappa NGAST → Python AST
│
├─ tokenizer/                    # tokenizer NEUROGLYPH nativo
│  └─ tokenizer.py               # BPE/unigram, zero-splitting guaranteed
│
├─ tests/                        # test di validazione
│  ├─ test_roundtrip.py          # AST roundtrip verification
│  ├─ test_canonical.py          # normalizzazione e formattazione
│  └─ test_transpiler.py         # validazione transpiler
│
└─ scripts/                      # tooling CLI
   ├─ phase_4_1_ast_roundtrip_verification.py  
   ├─ phase_4_2_tokenizer_validation.py  
   └─ phase_4_3_transpiler_validation.py  
```

## 🧱 **COMPONENTI DEL SISTEMA**

### 1. **Parser Simbolico**
- **Input**: stringa NEUROGLYPH (es. `∀x. x ∈ A ⇒ x > 0`)
- **Output**: NGAST (Abstract Symbolic Tree)
- **Tecnologia**: lark-parser con grammatica EBNF

### 2. **AST Canonico (NGAST)**
Ogni nodo rappresenta un costrutto simbolico:

```json
{
  "type": "Implication",
  "left": {
    "type": "Quantifier",
    "quantifier": "∀",
    "variable": "x",
    "domain": "A"
  },
  "right": {
    "type": "Comparison",
    "op": ">",
    "left": "x",
    "right": 0
  }
}
```

### 3. **Encoder/Decoder NEUROGLYPH**
- **NGAST → neuroglyph_code**: simboli leggibili
- **neuroglyph_code → NGAST**: parser inverso
- **Roundtrip perfetto**: zero perdita semantica

### 4. **Transpiler (Opzionale)**
- **NGAST → Python**: per integrazione esistente
- **NGAST → Rust/C++**: per espansione futura
- **Mapping selettivo**: solo costrutti semanticamente equivalenti

## 🚀 **FASI DI SVILUPPO DETTAGLIATE**

### **🔧 Fase 1 – Definizione della Grammatica (24h)**

**Obiettivo**: Creare grammatica EBNF completa per tutti i simboli NEUROGLYPH

**Deliverable**:
- `grammar.lark` con regole complete
- Lista token catalogati per categoria (logical, math, code, structure, meta)
- Test parsing minimo su esempi base

**Esempio Grammatica**:
```ebnf
start: expr
expr: quant_expr | binary_expr | unary_expr | atom
quant_expr: QUANTIFIER VAR "." expr  
binary_expr: expr OP expr
atom: VAR | NUMBER | SYMBOL
```

**Metriche Target**:
- Parseability 100% su 100 esempi test
- Coverage grammar ≥ 100% simboli registry

### **🏗️ Fase 2 – Parser & Costruzione NGAST (48h)**

**Obiettivo**: Implementare parser completo con costruzione AST

**Componenti**:
- **Lexer** (`lexer.py`): regole regex per token
- **Parser** (`parser.py`): usa Lark per generare NGAST
- **AST Nodes** (`ast/nodes.py`): dataclasses per nodi

**Esempio Nodo**:
```python
@dataclass
class Quantifier:
    quant: str  # '∀' o '∃'
    var: str
    body: ASTNode
```

**Metriche Target**:
- Parseability 100% su 1000 esempi
- Coverage tutti i tipi di nodi
- Zero errori di costruzione AST

### **🔄 Fase 3 – Normalizzazione & Canonicalizzazione (24h)**

**Obiettivo**: Garantire equivalenza semantica AST

**Componenti**:
- **Transformer** (`ast/transformers.py`): normalizzazione
- **Visitor** (`ast/visitor.py`): confronto semantico
- **Canonical Style** (`unparser/canonical_style.py`): formattazione

**Regole Normalizzazione**:
- Ordinamento argomenti commutativi (A ∧ B → B ∧ A)
- Unificazione literal formats (0.5 vs .5)
- Quote style costante

**Metriche Target**:
- AST equivalence ≥ 95% su permutazioni/formattazioni

### **🔁 Fase 4 – Roundtrip Verification (24h)**

**Obiettivo**: Validazione completa Parse → AST → Unparse

**Flusso**:
1. Parse → AST
2. AST → Unparse  
3. Re-parse del codice generato
4. Confronto AST canonici

**Script**: `phase_4_1_ast_roundtrip_verification.py`

**Metriche Target**:
- Roundtrip pass rate ≥ 95% (ideale 100%)
- Report dettagliato drift patterns

### **🔡 Fase 5 – Tokenizer Integration (24h)**

**Obiettivo**: Tokenizer nativo con zero-splitting guarantee

**Componenti**:
- BPE/Unigram per simboli unici
- Validazione su tutti i simboli registry
- Test boundaries e vocab size

**Script**: `phase_4_2_tokenizer_validation.py`

**Metriche Target**:
- Zero-splitting 100%
- Vocab size ottimizzato (≤10× simboli registry)

### **🔀 Fase 6 – Transpiler NGAST → Python (Opzionale, 48h)**

**Obiettivo**: Integrazione con sistema esistente

**Componenti**:
- Mappature NGAST → Python AST
- Serializzazione con ast.unparse()
- Roundtrip: NG → Py AST → NGAST

**Script**: `phase_4_3_transpiler_validation.py`

**Metriche Target**:
- Fidelity ≥ 95% sui casi supportati
- Coverage constructs ≥ 90%

### **🧪 Fase 7 – Test di Integrazione & CI (24h)**

**Obiettivo**: Pipeline completa e automazione

**Componenti**:
- Comando unificato `neuroglyph-verify`
- Pipeline CI (GitHub Actions)
- Badge pass/fail per AST/Tokenization
- Documentazione API completa

**Metriche Target**:
- CI Pass Rate 100%
- Documentazione coverage 100%

## 📊 **METRICHE DI SUCCESSO COMPLESSIVA**

| Fase | Metrica Target | Criticità |
|------|----------------|-----------|
| 1. Parseability | 100% | 🔴 Critica |
| 2. AST Construction | Coverage ≥ 100% | 🔴 Critica |
| 3. Canonical AST Equivalence | ≥ 95% | 🟡 Alta |
| 4. Roundtrip Verification | ≥ 95% (ideale 100%) | 🔴 Critica |
| 5. Tokenization Integrity | 100% zero-splitting | 🔴 Critica |
| 6. Transpiler Fidelity | ≥ 95% sui supportati | 🟢 Media |
| 7. CI Pass Rate | 100% | 🟡 Alta |

## 🔭 **ROADMAP IMMEDIATA**

### **Sprint 1 (3 giorni)**
1. **Giorno 1**: Definire EBNF completo in `grammar.lark`
2. **Giorno 2-3**: Implementare parser+AST con Lark e dataclasses
3. **Giorno 3**: Scrivere test di roundtrip e Canonical Visitor

### **Sprint 2 (2 giorni)**  
4. **Giorno 4**: Tokenizer integration e zero-splitting validation
5. **Giorno 5**: CI pipeline e documentazione

### **Sprint 3 (Opzionale, 2 giorni)**
6. **Giorno 6-7**: Transpiler NGAST → Python per backward compatibility

**Dopo 5 giorni avremo**: NGParser e NGAST affidabili al 95%+, pronti per pipeline LLM.

## 🏆 **VANTAGGI STRATEGICI**

### **Vs Approccio Python-Based**
- ✅ **Semantica Completa**: Tutti i 9,236 simboli supportati
- ✅ **Zero Perdita**: Roundtrip perfetto senza compromessi
- ✅ **Portabilità**: Base per transpiler multi-linguaggio
- ✅ **Unicità**: Primo LLM con reasoning simbolico nativo

### **Impatto sul Progetto NEUROGLYPH**
- 🚀 **Foundation Solida**: Base per tutte le fasi successive
- 🧠 **True Symbolic AI**: LLM che pensa in simboli, non token
- 🔬 **Validazione Scientifica**: Metriche oggettive e riproducibili
- 🌍 **Scalabilità**: Architettura modulare per espansioni future

Con questo blueprint, ogni componente è **chiaro**, **misurabile**, **modulare** e **scalabile**: esattamente ciò che serve per un LLM che "pensa davvero" in simboli.

---

# 🚨 **CRITICAL PIVOT: NEUROGLYPH LINGUAGGIO CONCETTUALE PURO**

## **DIAGNOSI BRUTALE**
L'implementazione attuale **TRADISCE la visione NEUROGLYPH originale**:
- ❌ Tokenizer Lark tradizionale (non atomico)
- ❌ Grammatica sintattica (non semantica)
- ❌ AST Python-oriented (non concettuale)
- ❌ Parsing procedurale (non semantico)

**COMPLIANCE SCORE: 35/100** - INACCETTABILE

## **REFACTOR COMPLETO: ROADMAP 17 GIORNI**

### **🎯 OBIETTIVO SUPREMO**
```
Simbolo = Concetto atomico indivisibile
Token = Unità semantica pura (ID univoco)
Codice = Linguaggio concettuale universale
AST = Struttura logica pura (non Python)
Parsing = Riconoscimento semantico diretto
```

### **📋 FASI DI REFACTOR**

| Fase | Durata | Deliverable | Criticità |
|------|--------|-------------|-----------|
| **1. Spec Concettuale** | 2 giorni | Documento semantico completo | 🔴 Critica |
| **2. Tokenizer Atomico** | 3 giorni | Mappatura 1:1 simbolo→tokenID | 🔴 Critica |
| **3. Parser Concettuale** | 4 giorni | Grammar semantica + AST puro | 🔴 Critica |
| **4. AST Semantico** | 3 giorni | Nodi concettuali immutabili | 🟡 Alta |
| **5. Integrazione Dataset** | 3 giorni | Rigenerazione con parser puro | 🟡 Alta |
| **6. Evaluation Adapter** | 2 giorni | Eliminazione layer Python | 🟢 Media |

### **🔧 COMPONENTI CHIAVE**

#### **1. Registry Concettuale Completo**
```json
{
  "concepts": {
    "∀": {
      "id": 1001,
      "unicode": "∀",
      "concept": "universal_quantification",
      "semantic_type": "quantifier",
      "arity": 2,
      "meaning": "for all elements in domain",
      "logical_strength": 1.0
    },
    "⇒": {
      "id": 1002,
      "unicode": "⇒",
      "concept": "material_implication",
      "semantic_type": "logical_connective",
      "arity": 2,
      "meaning": "if premise then conclusion",
      "logical_strength": 0.8
    }
  }
}
```

#### **2. Tokenizer Concettuale Atomico**
```python
class NGConceptualTokenizer:
    def __init__(self, concept_registry):
        self.symbol_to_concept = {c.unicode: c for c in concept_registry}

    def tokenize(self, neuroglyph_code: str) -> List[ConceptToken]:
        tokens = []
        i = 0
        while i < len(neuroglyph_code):
            # Riconoscimento simbolo atomico
            symbol = self._extract_atomic_symbol(neuroglyph_code, i)
            if symbol in self.symbol_to_concept:
                concept = self.symbol_to_concept[symbol]
                tokens.append(ConceptToken(
                    symbol=symbol,
                    concept_id=concept.id,
                    semantic_type=concept.semantic_type,
                    meaning=concept.meaning
                ))
            i += len(symbol)
        return tokens
```

#### **3. Grammar Semantica Pura**
```ebnf
// NEUROGLYPH Conceptual Grammar v2.0
conceptual_expression: quantified_concept
                     | logical_concept
                     | mathematical_concept
                     | inference_concept

quantified_concept: UNIVERSAL_QUANTIFIER variable domain_restriction concept_body
                  | EXISTENTIAL_QUANTIFIER variable domain_restriction concept_body

logical_concept: concept_operand IMPLICATION concept_operand
               | concept_operand CONJUNCTION concept_operand
               | NEGATION concept_operand

inference_concept: premise_set PROVES conclusion
                 | premise_set ENTAILS conclusion

// Tokens concettuali atomici
UNIVERSAL_QUANTIFIER: "∀"
EXISTENTIAL_QUANTIFIER: "∃"
IMPLICATION: "⇒"
CONJUNCTION: "∧"
PROVES: "⊢"
ENTAILS: "⊨"
```

#### **4. AST Concettuale Puro**
```python
@dataclass(frozen=True)
class ConceptualAST:
    """AST semantico puro - immutabile e universale."""
    root_concept: Concept
    semantic_relations: List[ConceptRelation]
    logical_structure: LogicalStructure
    meta_properties: Dict[str, Any]

@dataclass(frozen=True)
class UniversalQuantification(Concept):
    """Concetto di quantificazione universale ∀."""
    variable: Variable
    domain: Domain
    body: Concept
    logical_strength: float = 1.0

    def semantic_meaning(self) -> str:
        return f"For all {self.variable} in {self.domain}: {self.body}"

@dataclass(frozen=True)
class MaterialImplication(Concept):
    """Concetto di implicazione materiale ⇒."""
    premise: Concept
    conclusion: Concept
    logical_strength: float = 0.8

    def semantic_meaning(self) -> str:
        return f"If {self.premise} then {self.conclusion}"
```

#### **5. Parser Concettuale**
```python
class NGConceptualParser:
    """Parser semantico puro per NEUROGLYPH."""

    def __init__(self, concept_registry, semantic_grammar):
        self.concepts = concept_registry
        self.grammar = semantic_grammar
        self.concept_patterns = self._build_concept_patterns()

    def parse(self, concept_tokens: List[ConceptToken]) -> ConceptualAST:
        """Parse semantico diretto da token a concetti."""
        for pattern in self.concept_patterns:
            if pattern.matches_semantically(concept_tokens):
                return pattern.build_conceptual_ast(concept_tokens)

        raise SemanticParseError("No semantic pattern matches token sequence")

    def _build_concept_patterns(self) -> List[ConceptPattern]:
        """Costruisce pattern semantici da grammar."""
        patterns = []

        # Pattern quantificazione universale: ∀ variable domain body
        patterns.append(ConceptPattern(
            semantic_type="universal_quantification",
            token_sequence=[UNIVERSAL_QUANTIFIER, VARIABLE, DOMAIN, CONCEPT],
            builder=lambda tokens: UniversalQuantification(
                variable=tokens[1].concept,
                domain=tokens[2].concept,
                body=tokens[3].concept
            )
        ))

        # Pattern implicazione: premise ⇒ conclusion
        patterns.append(ConceptPattern(
            semantic_type="material_implication",
            token_sequence=[CONCEPT, IMPLICATION, CONCEPT],
            builder=lambda tokens: MaterialImplication(
                premise=tokens[0].concept,
                conclusion=tokens[2].concept
            )
        ))

        return patterns
```

### **🧪 VALIDAZIONE SEMANTICA**

#### **Round-trip Concettuale**
```python
def test_semantic_roundtrip(neuroglyph_code: str) -> bool:
    """Test roundtrip semantico puro."""
    # 1. Tokenize → Concetti
    tokens = tokenizer.tokenize(neuroglyph_code)

    # 2. Parse → AST Concettuale
    ast = parser.parse(tokens)

    # 3. Serialize → NEUROGLYPH
    reconstructed = serializer.serialize(ast)

    # 4. Re-parse → AST Concettuale
    reconstructed_ast = parser.parse(tokenizer.tokenize(reconstructed))

    # 5. Equivalenza semantica (non sintattica!)
    return ast.semantically_equivalent(reconstructed_ast)
```

#### **Metriche Semantiche**
```python
@dataclass
class SemanticValidationMetrics:
    concept_coverage: float  # % simboli registry coperti
    semantic_fidelity: float  # % equivalenza semantica roundtrip
    atomic_tokenization: float  # % zero-splitting garantito
    logical_consistency: float  # % coerenza logica AST
    conceptual_purity: float  # % indipendenza da Python
```

### **📊 CRITERI DI SUCCESSO**

| Metrica | Target | Criticità |
|---------|--------|-----------|
| **Concept Coverage** | 100% | 🔴 Critica |
| **Semantic Fidelity** | ≥95% | 🔴 Critica |
| **Atomic Tokenization** | 100% | 🔴 Critica |
| **Logical Consistency** | ≥98% | 🟡 Alta |
| **Conceptual Purity** | 100% | 🟡 Alta |

### **🚀 PIANO IMMEDIATO**

**GIORNO 1-2: Spec Concettuale**
- Documento completo semantica NEUROGLYPH
- Registry concettuale con tutti i simboli
- Grammar semantica EBNF pura

**GIORNO 3-5: Tokenizer Atomico**
- Implementazione tokenizer concettuale
- Test zero-splitting su tutti i simboli
- Validazione 1:1 simbolo→concetto

**GIORNO 6-9: Parser Concettuale**
- Parser semantico con pattern matching
- AST concettuale immutabile
- Test parsing su 1000+ casi

**GIORNO 10-12: AST Semantico**
- Classi concetto pure e immutabili
- Visitor semantico per traversal
- Round-trip semantico garantito

**GIORNO 13-15: Integrazione**
- Rigenerazione dataset con parser puro
- Adattamento evaluation harness
- Eliminazione dipendenze Python

**GIORNO 16-17: Validazione**
- Test completi su tutti i criteri
- Documentazione e guida contributori
- Report finale compliance

### **🎯 RISULTATO FINALE**

Al termine del refactor avremo:

✅ **NEUROGLYPH Linguaggio Concettuale Puro**
- Ogni simbolo = concetto atomico indivisibile
- Parser semantico che riconosce significati
- AST universale indipendente da linguaggi
- Tokenizer con zero-splitting garantito
- Validazione semantica rigorosa

✅ **Ecosystem Coerente**
- Dataset generato da parser concettuale
- Evaluation basata su semantica pura
- LLM training su concetti atomici
- Benchmark su equivalenza semantica

Questo è il **vero NEUROGLYPH**: il primo linguaggio di programmazione concettuale della storia.

---

## 🔬 **ANALISI TECNICA DETTAGLIATA**

### **📊 Registry Simbolico Attuale**

Dal registry esistente abbiamo identificato:

<augment_code_snippet path="data/registry/neuroglyph_symbols.json" mode="EXCERPT">
```json
{
  "metadata": {
    "total_symbols": 68,
    "total_examples_analyzed": 20000
  },
  "categories": {
    "logical": ["¬", "⇒", "⇔", "∀", "∃", "∧", "∨", "∴", "≡", "⊢", "⊥"],
    "mathematical": ["±", "²", "³", "∂", "∇", "∏", "∑", "∞", "∫", "≈", "≠", "≤", "≥"],
    "set_theory": ["∅", "∈", "∉", "∩", "∪", "⊂", "⊆", "⊇"],
    "arrows": ["→", "↔", "↕", "⤴"],
    "greek": ["λ", "ℝ"],
    "emoji": ["🏛", "📖", "🔄", "🔢", "🔮", "🧠"]
  }
}
```
</augment_code_snippet>

**Simboli Critici per Parsing**:
- **Logici**: `∀x`, `∃y`, `P ⇒ Q`, `P ∧ Q`, `P ∨ Q`, `¬P`
- **Matematici**: `∫₀¹ f(x) dx`, `∑ᵢ₌₁ⁿ aᵢ`, `∂f/∂x`
- **Set Theory**: `x ∈ A`, `A ⊂ B`, `A ∩ B`, `∅`
- **Meta-simboli**: `🧠` (reasoning), `⊢` (inference), `⚡` (function)

### **🔧 Grammatica EBNF Dettagliata**

```ebnf
// NEUROGLYPH Grammar v1.0
start: expression

// Espressioni principali
expression: quantified_expr
          | logical_expr
          | mathematical_expr
          | set_expr
          | meta_expr
          | atom

// Quantificatori: ∀x ∈ A: P(x), ∃y: Q(y)
quantified_expr: QUANTIFIER variable [domain_spec] ":" expression
domain_spec: "∈" set_expression

// Logica proposizionale: P ⇒ Q, P ∧ Q ∨ R
logical_expr: expression LOGICAL_OP expression
            | "¬" expression
            | "(" expression ")"

// Matematica: ∫₀¹ f(x) dx, ∑ᵢ₌₁ⁿ aᵢ
mathematical_expr: integral_expr | sum_expr | derivative_expr
integral_expr: "∫" [bounds] function_expr differential
sum_expr: "∑" [bounds] expression
derivative_expr: "∂" expression "/" "∂" variable

// Set theory: x ∈ A, A ⊂ B, A ∩ B
set_expr: expression SET_OP expression

// Meta-espressioni: 🧠 P ⊢ Q, ⚡ func_name
meta_expr: META_SYMBOL expression

// Atomi
atom: variable | number | symbol | string

// Token definitions
QUANTIFIER: "∀" | "∃"
LOGICAL_OP: "⇒" | "⇔" | "∧" | "∨" | "≡" | "⊢"
SET_OP: "∈" | "∉" | "⊂" | "⊆" | "⊇" | "∩" | "∪"
META_SYMBOL: "🧠" | "⚡" | "🔄" | "🏛" | "📖" | "🔮"
MATH_OP: "∫" | "∑" | "∏" | "∂" | "∇" | "√" | "∞"

variable: /[a-zA-Z][a-zA-Z0-9_]*/
number: /[0-9]+(\.[0-9]+)?/
symbol: /[⟨⟩⟦⟧←→↔↕⇄⇆⤴]/
```

### **🏗️ Struttura NGAST (Nodi AST)**

```python
from dataclasses import dataclass
from typing import List, Optional, Union, Any
from abc import ABC, abstractmethod

@dataclass
class NGASTNode(ABC):
    """Nodo base dell'AST simbolico NEUROGLYPH."""
    line: int = 0
    column: int = 0

    @abstractmethod
    def accept(self, visitor: 'NGASTVisitor') -> Any:
        pass

@dataclass
class QuantifiedExpression(NGASTNode):
    """∀x ∈ A: P(x) o ∃y: Q(y)"""
    quantifier: str  # "∀" o "∃"
    variable: str
    domain: Optional['SetExpression'] = None
    body: NGASTNode = None

    def accept(self, visitor):
        return visitor.visit_quantified(self)

@dataclass
class LogicalExpression(NGASTNode):
    """P ⇒ Q, P ∧ Q, ¬P"""
    operator: str  # "⇒", "∧", "∨", "¬", "≡"
    left: Optional[NGASTNode] = None
    right: Optional[NGASTNode] = None

    def accept(self, visitor):
        return visitor.visit_logical(self)

@dataclass
class MathematicalExpression(NGASTNode):
    """∫₀¹ f(x) dx, ∑ᵢ₌₁ⁿ aᵢ, ∂f/∂x"""
    operator: str  # "∫", "∑", "∂"
    bounds: Optional[List[NGASTNode]] = None
    expression: Optional[NGASTNode] = None
    variable: Optional[str] = None

    def accept(self, visitor):
        return visitor.visit_mathematical(self)

@dataclass
class SetExpression(NGASTNode):
    """x ∈ A, A ⊂ B, A ∩ B"""
    operator: str  # "∈", "⊂", "∩", "∪"
    left: NGASTNode = None
    right: NGASTNode = None

    def accept(self, visitor):
        return visitor.visit_set(self)

@dataclass
class MetaExpression(NGASTNode):
    """🧠 reasoning, ⚡ function, 🔄 loop"""
    meta_symbol: str  # "🧠", "⚡", "🔄"
    content: NGASTNode = None

    def accept(self, visitor):
        return visitor.visit_meta(self)

@dataclass
class Variable(NGASTNode):
    """Variabile: x, y, func_name"""
    name: str

    def accept(self, visitor):
        return visitor.visit_variable(self)

@dataclass
class Literal(NGASTNode):
    """Letterale: 42, 3.14, "hello" """
    value: Union[int, float, str]
    type: str  # "int", "float", "string"

    def accept(self, visitor):
        return visitor.visit_literal(self)
```

### **🔄 Parser Implementation con Lark**

```python
from lark import Lark, Transformer
from typing import Dict, List

class NGASTTransformer(Transformer):
    """Trasforma parse tree Lark in NGAST."""

    def quantified_expr(self, items):
        quantifier, variable, domain, body = items
        return QuantifiedExpression(
            quantifier=str(quantifier),
            variable=str(variable),
            domain=domain if domain else None,
            body=body
        )

    def logical_expr(self, items):
        if len(items) == 2:  # Unary: ¬P
            op, expr = items
            return LogicalExpression(operator=str(op), right=expr)
        else:  # Binary: P ⇒ Q
            left, op, right = items
            return LogicalExpression(operator=str(op), left=left, right=right)

    def mathematical_expr(self, items):
        # Gestisce ∫, ∑, ∂ con bounds e variabili
        operator = str(items[0])
        return MathematicalExpression(
            operator=operator,
            bounds=items[1:-1] if len(items) > 2 else None,
            expression=items[-1]
        )

    def set_expr(self, items):
        left, op, right = items
        return SetExpression(operator=str(op), left=left, right=right)

    def meta_expr(self, items):
        symbol, content = items
        return MetaExpression(meta_symbol=str(symbol), content=content)

    def variable(self, items):
        return Variable(name=str(items[0]))

    def number(self, items):
        value = str(items[0])
        if '.' in value:
            return Literal(value=float(value), type="float")
        else:
            return Literal(value=int(value), type="int")

class NGParser:
    """Parser simbolico NEUROGLYPH nativo."""

    def __init__(self, grammar_path: str = "neuroglyph/parser/grammar.lark"):
        with open(grammar_path, 'r') as f:
            grammar = f.read()

        self.parser = Lark(grammar, parser='earley', transformer=NGASTTransformer())
        self.symbol_registry = self._load_symbol_registry()

    def parse(self, neuroglyph_code: str) -> NGASTNode:
        """
        Parse NEUROGLYPH code in AST simbolico.

        Args:
            neuroglyph_code: Codice simbolico NEUROGLYPH

        Returns:
            Root node dell'AST simbolico
        """
        try:
            ast_root = self.parser.parse(neuroglyph_code)
            return ast_root
        except Exception as e:
            raise NGParseError(f"Errore parsing: {e}")

    def _load_symbol_registry(self) -> Dict:
        """Carica registry simboli per validazione."""
        # Integrazione con registry esistente
        from neuroglyph.utils.registry_loader import load_registry_fast
        return load_registry_fast()
```

### **🔁 Unparser (AST → NEUROGLYPH)**

```python
class NGASTVisitor(ABC):
    """Visitor pattern per traversal AST."""

    @abstractmethod
    def visit_quantified(self, node: QuantifiedExpression) -> str: pass

    @abstractmethod
    def visit_logical(self, node: LogicalExpression) -> str: pass

    @abstractmethod
    def visit_mathematical(self, node: MathematicalExpression) -> str: pass

    @abstractmethod
    def visit_set(self, node: SetExpression) -> str: pass

    @abstractmethod
    def visit_meta(self, node: MetaExpression) -> str: pass

    @abstractmethod
    def visit_variable(self, node: Variable) -> str: pass

    @abstractmethod
    def visit_literal(self, node: Literal) -> str: pass

class NGUnparser(NGASTVisitor):
    """Converte NGAST in codice NEUROGLYPH."""

    def unparse(self, ast_root: NGASTNode) -> str:
        """Genera codice NEUROGLYPH da AST."""
        return ast_root.accept(self)

    def visit_quantified(self, node: QuantifiedExpression) -> str:
        domain_str = f" ∈ {node.domain.accept(self)}" if node.domain else ""
        body_str = node.body.accept(self)
        return f"{node.quantifier}{node.variable}{domain_str}: {body_str}"

    def visit_logical(self, node: LogicalExpression) -> str:
        if node.left is None:  # Unary: ¬P
            return f"{node.operator}{node.right.accept(self)}"
        else:  # Binary: P ⇒ Q
            left_str = node.left.accept(self)
            right_str = node.right.accept(self)
            return f"{left_str} {node.operator} {right_str}"

    def visit_mathematical(self, node: MathematicalExpression) -> str:
        if node.operator == "∫":
            bounds_str = "".join(b.accept(self) for b in node.bounds) if node.bounds else ""
            expr_str = node.expression.accept(self)
            return f"∫{bounds_str} {expr_str}"
        elif node.operator == "∑":
            bounds_str = "".join(b.accept(self) for b in node.bounds) if node.bounds else ""
            expr_str = node.expression.accept(self)
            return f"∑{bounds_str} {expr_str}"
        elif node.operator == "∂":
            expr_str = node.expression.accept(self)
            return f"∂{expr_str}/∂{node.variable}"

    def visit_set(self, node: SetExpression) -> str:
        left_str = node.left.accept(self)
        right_str = node.right.accept(self)
        return f"{left_str} {node.operator} {right_str}"

    def visit_meta(self, node: MetaExpression) -> str:
        content_str = node.content.accept(self)
        return f"{node.meta_symbol} {content_str}"

    def visit_variable(self, node: Variable) -> str:
        return node.name

    def visit_literal(self, node: Literal) -> str:
        return str(node.value)
```

### **🧪 Test di Roundtrip**

```python
class NGRoundTripTester:
    """Tester per validazione roundtrip NEUROGLYPH → NGAST → NEUROGLYPH."""

    def __init__(self):
        self.parser = NGParser()
        self.unparser = NGUnparser()
        self.canonicalizer = NGCanonicalizer()

    def test_roundtrip(self, neuroglyph_code: str) -> RoundTripResult:
        """
        Test completo di roundtrip.

        Args:
            neuroglyph_code: Codice simbolico originale

        Returns:
            Risultato del test con metriche
        """
        try:
            # Step 1: Parse → AST
            original_ast = self.parser.parse(neuroglyph_code)

            # Step 2: AST → Code
            reconstructed_code = self.unparser.unparse(original_ast)

            # Step 3: Re-parse
            reconstructed_ast = self.parser.parse(reconstructed_code)

            # Step 4: Confronto AST canonici
            canonical_original = self.canonicalizer.canonicalize(original_ast)
            canonical_reconstructed = self.canonicalizer.canonicalize(reconstructed_ast)

            ast_equivalent = self._compare_ast(canonical_original, canonical_reconstructed)

            return RoundTripResult(
                original_code=neuroglyph_code,
                reconstructed_code=reconstructed_code,
                ast_equivalent=ast_equivalent,
                success=ast_equivalent,
                fidelity_score=1.0 if ast_equivalent else 0.0
            )

        except Exception as e:
            return RoundTripResult(
                original_code=neuroglyph_code,
                reconstructed_code="",
                ast_equivalent=False,
                success=False,
                fidelity_score=0.0,
                error=str(e)
            )

    def _compare_ast(self, ast1: NGASTNode, ast2: NGASTNode) -> bool:
        """Confronto semantico profondo tra AST."""
        # Implementazione ricorsiva di confronto
        if type(ast1) != type(ast2):
            return False

        if isinstance(ast1, QuantifiedExpression):
            return (ast1.quantifier == ast2.quantifier and
                    ast1.variable == ast2.variable and
                    self._compare_ast(ast1.body, ast2.body))

        # ... altri tipi di nodi

        return True
```

### **📊 Metriche di Validazione**

```python
@dataclass
class ValidationMetrics:
    """Metriche complete di validazione NEUROGLYPH."""

    # Parsing
    parseability_rate: float = 0.0
    parse_errors: List[str] = field(default_factory=list)

    # AST Construction
    ast_construction_rate: float = 0.0
    ast_coverage: Dict[str, int] = field(default_factory=dict)

    # Roundtrip
    roundtrip_success_rate: float = 0.0
    ast_equivalence_rate: float = 0.0
    fidelity_scores: List[float] = field(default_factory=list)

    # Tokenization
    zero_splitting_rate: float = 0.0
    vocab_efficiency: float = 0.0

    # Overall
    overall_pass_rate: float = 0.0
    critical_failures: List[str] = field(default_factory=list)

    def calculate_overall_score(self) -> float:
        """Calcola score complessivo pesato."""
        weights = {
            'parseability': 0.25,
            'roundtrip': 0.30,
            'ast_equivalence': 0.25,
            'tokenization': 0.20
        }

        score = (
            self.parseability_rate * weights['parseability'] +
            self.roundtrip_success_rate * weights['roundtrip'] +
            self.ast_equivalence_rate * weights['ast_equivalence'] +
            self.zero_splitting_rate * weights['tokenization']
        )

        return score
```

Questa architettura dettagliata fornisce una base solida per implementare il parser simbolico nativo NEUROGLYPH con tutte le componenti necessarie per validazione rigorosa e roundtrip perfetto.
