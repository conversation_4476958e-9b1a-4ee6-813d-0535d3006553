# NEUROGLYPH CI/CD - ULTRA-RIGOROUS Dependencies
# <PERSON>ttimizza<PERSON> per GitHub Actions mantenendo MASSIMA RIGOROSITÀ

# Core Symbolic Processing (ESSENZIALI per ULTRA-RIGOROSITÀ)
numpy>=1.24.0,<2.0.0
scipy>=1.11.0,<2.0.0
sympy>=1.12.0
networkx>=3.1.0

# Core ML/AI (Versioni compatibili Python 3.10-3.11)
torch>=2.0.0,<2.5.0
transformers>=4.35.0,<5.0.0
tokenizers>=0.15.0,<1.0.0
datasets>=2.14.0,<3.0.0

# Memory e Storage (CRITICI per Registry Validation)
lmdb>=1.4.1,<2.0.0
# sqlite3 è built-in Python - RIMOSSO

# Data Processing (ESSENZIALI per Validation)
pandas>=2.0.0,<3.0.0
jsonlines>=4.0.0
pydantic>=2.4.0,<3.0.0
typing-extensions>=4.8.0

# Testing (ULTRA-RIGOROUS Testing Framework)
pytest>=7.4.0,<8.0.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
hypothesis>=6.88.0

# Cryptographic Security (per Tripla Barriera)
cryptography>=41.0.0

# Utilities (ESSENZIALI per CI)
tqdm>=4.66.0
rich>=13.6.0
click>=8.1.0
python-dotenv>=1.0.0
PyYAML>=6.0.1

# Development Tools (Quality Assurance)
black>=23.9.0
isort>=5.12.0
flake8>=6.1.0

# ESCLUSI per CI (troppo pesanti):
# - unsloth (richiede CUDA, troppo pesante per CI)
# - bitsandbytes (GPU-specific)
# - faiss-cpu (troppo pesante per validation essenziale)
# - redis (non necessario per validation)
# - accelerate (non necessario per validation)
# - peft, trl (training-specific)
# - matplotlib, seaborn, plotly (visualization non necessaria)
# - ray, wandb (advanced features non necessarie)

# NOTA: Questo file mantiene TUTTI i componenti ESSENZIALI
# per la TRIPLA BARRIERA DI SICUREZZA SIMBOLICA e i 
# 5 PRINCIPI IMMUTABILI senza compromessi sulla RIGOROSITÀ
