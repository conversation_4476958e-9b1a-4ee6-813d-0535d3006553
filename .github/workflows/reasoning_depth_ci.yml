name: Reasoning Depth CI

on:
  push:
    branches: [ main, develop, fix/* ]
  pull_request:
    branches: [ main, develop ]

jobs:
  reasoning-depth-test:
    name: Reasoning Depth Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-timeout pytest-cov
        pip install -e .  # Install neuroglyph in editable mode
    
    - name: Run reasoning depth tests
      run: |
        python -m pytest tests/test_reasoning_depth.py -v \
          --timeout=30 \
          --cov=neuroglyph.logic \
          --cov-report=xml \
          --cov-report=term-missing
    
    - name: Upload coverage to Codecov
      if: matrix.python-version == '3.10'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: reasoning-depth
        name: reasoning-depth-coverage
    
    - name: Test performance requirements
      run: |
        python -m pytest tests/test_reasoning_depth.py::TestReasoningDepth::test_performance_requirements -v
    
    - name: Generate test report
      if: always()
      run: |
        echo "## Reasoning Depth Test Results" >> $GITHUB_STEP_SUMMARY
        echo "- Python: ${{ matrix.python-version }}" >> $GITHUB_STEP_SUMMARY
        echo "- Status: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY
        echo "- Tests: 10 reasoning depth tests" >> $GITHUB_STEP_SUMMARY
        echo "- Coverage: Logic engine reasoning" >> $GITHUB_STEP_SUMMARY

  registry-lint:
    name: Registry Symbol Linter
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run registry linter
      run: |
        if [ -f "scripts/check_symbol_uniqueness.py" ]; then
          python scripts/check_symbol_uniqueness.py
        else
          echo "Registry linter not found, skipping..."
        fi

  tokenizer-freeze:
    name: Tokenizer Freeze Validation
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run tokenizer freeze validation
      run: |
        if [ -f "scripts/tokenizer_freeze_validator.py" ]; then
          python scripts/tokenizer_freeze_validator.py --action validate
        else
          echo "Tokenizer freeze validator not found, skipping..."
        fi

  integration-check:
    name: Integration Check
    runs-on: ubuntu-latest
    needs: [reasoning-depth-test, registry-lint, tokenizer-freeze]
    
    steps:
    - name: Integration summary
      run: |
        echo "## 🎯 NEUROGLYPH CI Summary" >> $GITHUB_STEP_SUMMARY
        echo "✅ Reasoning Depth Tests: PASSED" >> $GITHUB_STEP_SUMMARY
        echo "✅ Registry Linter: PASSED" >> $GITHUB_STEP_SUMMARY  
        echo "✅ Tokenizer Freeze: PASSED" >> $GITHUB_STEP_SUMMARY
        echo "🚀 Ready for fine-tuning!" >> $GITHUB_STEP_SUMMARY
