#!/usr/bin/env python3
"""
Debug script per testare il parser NEUROGLYPH.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from neuroglyph.parser.parser import <PERSON><PERSON>ars<PERSON>

def test_simple_cases():
    """Test casi semplici per debug."""
    parser = NGParser()
    
    test_cases = [
        # Casi che dovrebbero funzionare
        "P",           # Variabile semplice
        "P ⇒ Q",       # Implicazione
        "¬P",          # Negazione
        "x ∈ A",       # Appartenenza
        "∅",           # Insieme vuoto
        "🧠 P",        # Meta simbolo
        "f(x)",        # Funzione

        # Casi che potrebbero fallire
        "∀x: P(x)",    # Quantificatore (non supportato)
        "∃y: Q(y)",    # Quantificatore (non supportato)
        "∫ f(x)",      # Integrale
        "√x",          # Radice
        "P ∧ Q ∨ R",   # Espressione complessa
        "{1, 2, 3}",   # Set letterale (non supportato)
        "P ⊢ Q",       # Inferenza (simbolo non in grammatica)
    ]
    
    for case in test_cases:
        print(f"\n🧪 Testing: '{case}'")
        try:
            ast = parser.parse(case)
            print(f"✅ Success: {type(ast).__name__}")
            print(f"   AST: {ast}")
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_simple_cases()
