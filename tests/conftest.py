"""
Configurazione pytest per test NEUROGLYPH.

Fixture condivise e configurazione per tutti i test.
"""

import pytest
import sys
import os

# Aggiungi il path del progetto per import
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def pytest_configure(config):
    """Configurazione pytest."""
    config.addinivalue_line(
        "markers", "timeout: mark test to run with timeout"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modifica automatica degli item di test."""
    for item in items:
        # Aggiungi marker slow ai test di performance
        if "performance" in item.name.lower():
            item.add_marker(pytest.mark.slow)
