#!/usr/bin/env python3
"""
Debug test per mathematical reasoning chain.
"""

import sys
import os

# Aggiungi path per import
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from neuroglyph.logic.logic_engine import FormalLogicEngine
    from neuroglyph.logic.formula import *
    LOGIC_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Logic engine non disponibile: {e}")
    LOGIC_AVAILABLE = False


def debug_mathematical_reasoning():
    """Debug del mathematical reasoning chain."""
    if not LOGIC_AVAILABLE:
        print("❌ Logic engine non disponibile")
        return
    
    print("🔍 DEBUG Mathematical Reasoning Chain")
    print("=" * 50)
    
    # Crea engine con debug abilitato
    engine = FormalLogicEngine(max_depth=20, max_steps=100)
    
    # Scenario: proprietà matematiche con catena di deduzioni
    x = Variable("x")
    four = Constant("4")
    
    premises = [
        # Se x è pari, allora x è divisibile per 2
        Implication(P("Even", x), P("DivisibleBy2", x)),
        # Se x è divisibile per 2, allora x è un numero intero
        Implication(P("DivisibleBy2", x), P("Integer", x)),
        # Se x è un numero intero, allora x è un numero razionale
        Implication(P("Integer", x), P("Rational", x)),
        # Se x è razionale, allora x è un numero reale
        Implication(P("Rational", x), P("Real", x)),
        # 4 è pari
        P("Even", four)
    ]
    goal = P("Real", four)
    
    print("📋 PREMISES:")
    for i, premise in enumerate(premises):
        print(f"   {i+1}. {premise}")
    
    print(f"\n🎯 GOAL: {goal}")
    
    print("\n🔍 STEP-BY-STEP ANALYSIS:")
    
    # Test unificazione manuale
    print("\n1. Test unificazione P(Even, x) con P(Even, 4):")
    unifier = engine.unifier
    
    even_x = P("Even", x)
    even_4 = P("Even", four)
    
    sub = unifier.unify_formulas(even_x, even_4)
    print(f"   {even_x} ≈ {even_4} → {sub}")
    
    if sub:
        # Applica sostituzione alla prima implicazione
        impl1 = premises[0]  # Even(x) → DivisibleBy2(x)
        impl1_sub = impl1.substitute(sub.mappings)
        print(f"   Dopo sostituzione: {impl1_sub}")
    
    print("\n2. Tentativo deduzione completa:")
    result = engine.deduce(premises, goal)
    
    print(f"   Success: {result.success}")
    print(f"   Steps: {result.steps_count}")
    print(f"   Depth: {result.depth}")
    print(f"   Error: {result.error_message}")
    
    if result.proof_tree:
        print(f"\n📊 PROOF TREE:")
        for i, step in enumerate(result.proof_tree.steps):
            print(f"   Step {i+1}: {step.formula} ({step.rule.value})")
    
    # Test semplificato: solo 2 step
    print("\n3. Test semplificato (2 step):")
    simple_premises = [
        Implication(P("Even", x), P("DivisibleBy2", x)),
        P("Even", four)
    ]
    simple_goal = P("DivisibleBy2", four)
    
    simple_result = engine.deduce(simple_premises, simple_goal)
    print(f"   Success: {simple_result.success}")
    print(f"   Steps: {simple_result.steps_count}")
    
    if simple_result.proof_tree:
        print(f"   Proof steps:")
        for i, step in enumerate(simple_result.proof_tree.steps):
            print(f"      {i+1}. {step.formula} ({step.rule.value})")


if __name__ == "__main__":
    debug_mathematical_reasoning()
