#!/usr/bin/env python3
"""
Test per verificare il fix della depth calculation nel FormalLogicEngine.
"""

import pytest
import sys
import os

# Aggiungi path per import
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from neuroglyph.logic.logic_engine import FormalLogicEngine
    from neuroglyph.logic.formula import *
    LOGIC_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Logic engine non disponibile: {e}")
    LOGIC_AVAILABLE = False


class TestDepthFix:
    """Test per verificare che depth calculation sia corretta."""
    
    @pytest.fixture
    def logic_engine(self):
        """Fixture per logic engine."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        return FormalLogicEngine(max_depth=20, max_steps=100)
    
    def test_3_step_chain_depth(self, logic_engine):
        """Test catena 3 step: P0 → P1 → P2 → P3."""
        premises = [
            Implication(P("P0"), P("P1")),  # P0 → P1
            Implication(P("P1"), P("P2")),  # P1 → P2  
            Implication(P("P2"), P("P3")),  # P2 → P3
            P("P0")  # Premessa iniziale
        ]
        goal = P("P3")
        
        result = logic_engine.deduce(premises, goal)
        
        print(f"🧪 3-Step Chain Test:")
        print(f"   - Success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Depth: {result.depth}")
        
        assert result.success, "3-step chain fallito"
        # Depth dovrebbe essere >= 7 (4 premesse + 3 deduzioni)
        assert result.depth >= 7, f"Depth {result.depth} < 7"
        
    def test_5_step_chain_depth(self, logic_engine):
        """Test catena 5 step: P0 → P1 → P2 → P3 → P4 → P5."""
        premises = [
            Implication(P("P0"), P("P1")),  # P0 → P1
            Implication(P("P1"), P("P2")),  # P1 → P2
            Implication(P("P2"), P("P3")),  # P2 → P3
            Implication(P("P3"), P("P4")),  # P3 → P4
            Implication(P("P4"), P("P5")),  # P4 → P5
            P("P0")  # Premessa iniziale
        ]
        goal = P("P5")
        
        result = logic_engine.deduce(premises, goal)
        
        print(f"🧪 5-Step Chain Test:")
        print(f"   - Success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Depth: {result.depth}")
        
        assert result.success, "5-step chain fallito"
        # Depth dovrebbe essere >= 11 (6 premesse + 5 deduzioni)
        assert result.depth >= 11, f"Depth {result.depth} < 11"
        
    def test_12_step_chain_depth(self, logic_engine):
        """Test catena 12 step: P0 → P1 → ... → P12."""
        premises = []
        for i in range(12):
            premises.append(Implication(P(f"P{i}"), P(f"P{i+1}")))
        premises.append(P("P0"))  # Premessa iniziale
        
        goal = P("P12")
        
        result = logic_engine.deduce(premises, goal)
        
        print(f"🧪 12-Step Chain Test:")
        print(f"   - Success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Depth: {result.depth}")
        
        assert result.success, "12-step chain fallito"
        # Depth dovrebbe essere >= 25 (13 premesse + 12 deduzioni)
        assert result.depth >= 25, f"Depth {result.depth} < 25"


if __name__ == "__main__":
    if LOGIC_AVAILABLE:
        print("🧪 Testing depth fix...")
        
        engine = FormalLogicEngine(max_depth=30, max_steps=200)
        test = TestDepthFix()
        
        # Test 3-step
        try:
            test.test_3_step_chain_depth(engine)
            print("✅ 3-step test PASSED")
        except Exception as e:
            print(f"❌ 3-step test FAILED: {e}")
        
        # Test 5-step
        try:
            test.test_5_step_chain_depth(engine)
            print("✅ 5-step test PASSED")
        except Exception as e:
            print(f"❌ 5-step test FAILED: {e}")
            
        # Test 12-step
        try:
            test.test_12_step_chain_depth(engine)
            print("✅ 12-step test PASSED")
        except Exception as e:
            print(f"❌ 12-step test FAILED: {e}")
    else:
        print("❌ Logic engine non disponibile")
