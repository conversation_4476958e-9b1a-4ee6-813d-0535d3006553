#!/usr/bin/env python3
"""
Test unitario per verificare depth calculation corretta nel reasoning engine.

Questo test verifica:
1. Formula steps == depth == 2*n + 1 per catene lineari
2. Proprietà invarianti del reasoning (determinismo, performance)
3. Casi edge: branching, variabili multiple, test negativi
4. Robustezza per CI/CD con timeout e asserzioni precise

Uso: pytest tests/test_reasoning_depth.py -v
"""

import pytest
import time
from typing import List

# Import diretto - assume che neuroglyph sia installabile (pip install -e .)
try:
    from neuroglyph.logic.logic_engine import FormalLogicEngine
    from neuroglyph.logic.formula import (
        P, Implication, Conjunction, Variable, Constant, Predicate
    )
    LOGIC_AVAILABLE = True
except ImportError as e:
    LOGIC_AVAILABLE = False
    pytest.skip(f"Logic engine non disponibile: {e}", allow_module_level=True)


# Fixture globale per evitare ricreazione engine
@pytest.fixture(scope="module")
def logic_engine():
    """Fixture per logic engine condivisa."""
    return FormalLogicEngine(max_depth=30, max_steps=200)


class TestReasoningDepth:
    """Test per verificare depth calculation e proprietà invarianti del reasoning."""
    
    @pytest.mark.parametrize("chain_length", [3, 5, 8, 12])
    def test_linear_chain_depth_formula(self, logic_engine, chain_length):
        """
        Test formula: steps == depth == 2*n + 1 per catene lineari.

        Questa formula vale per catene puramente lineari senza memoizzazione.
        Se l'algoritmo cambia, potrebbe essere necessario aggiornare le aspettative.
        """
        # Crea catena P0 → P1 → P2 → ... → P{chain_length}
        premises = [Implication(P(f"P{i}"), P(f"P{i+1}")) for i in range(chain_length)]
        premises.append(P("P0"))  # Premessa iniziale

        goal = P(f"P{chain_length}")
        result = logic_engine.deduce(premises, goal)

        # Formula teorica: n implicazioni + n deduzioni + 1 goal = 2n+1
        expected_steps = 2 * chain_length + 1

        assert result.success, f"Deduzione fallita per catena {chain_length}-step"
        assert result.steps_count == expected_steps, (
            f"Steps count mismatch: got {result.steps_count}, expected {expected_steps}"
        )
        assert result.depth == expected_steps, (
            f"Depth mismatch: got {result.depth}, expected {expected_steps}"
        )
        # Proprietà invariante: depth == steps per reasoning lineare
        assert result.depth == result.steps_count, (
            f"Depth ({result.depth}) != Steps ({result.steps_count})"
        )
    
    def test_branching_reasoning(self, logic_engine):
        """
        Test rami che biforcano: A→B, A→C, B∧C→D.

        Struttura attesa:
        1-4: premises, 5: B (A→B), 6: C (A→C), 7: B∧C (conjunction), 8: D (final)
        """
        premises = [
            Implication(P("A"), P("B")),           # A → B
            Implication(P("A"), P("C")),           # A → C
            Implication(Conjunction(P("B"), P("C")), P("D")),  # B ∧ C → D
            P("A")                                 # A è vero
        ]
        goal = P("D")

        result = logic_engine.deduce(premises, goal)

        assert result.success, "Branching reasoning fallito"
        # Proprietà invariante: depth == steps per reasoning deterministico
        assert result.steps_count == result.depth, (
            f"Depth ({result.depth}) != Steps ({result.steps_count}) in branching"
        )
        # Valore atteso basato su struttura algoritmo attuale
        expected_steps = 8  # 4 premises + 2 modus ponens + 1 conjunction + 1 final
        assert result.steps_count == expected_steps, (
            f"Branching steps: got {result.steps_count}, expected {expected_steps}"
        )
    
    def test_multiple_variables(self, logic_engine):
        """Test variabili multiple: ∀x,y."""
        x = Variable("x")
        y = Variable("y")
        
        premises = [
            Implication(P("Related", x, y), P("Connected", x, y)),
            Implication(P("Connected", x, y), P("Linked", x, y)),
            P("Related", Constant("A"), Constant("B"))
        ]
        goal = P("Linked", Constant("A"), Constant("B"))
        
        result = logic_engine.deduce(premises, goal)
        
        assert result.success, "Multiple variables reasoning fallito"
        assert result.steps_count == 7, f"Steps: {result.steps_count} != 7"
        assert result.depth == 7, f"Depth: {result.depth} != 7"
    
    def test_mathematical_reasoning(self, logic_engine):
        """Test mathematical property chain."""
        x = Variable("x")
        four = Constant("4")
        
        premises = [
            Implication(P("Even", x), P("DivisibleBy2", x)),
            Implication(P("DivisibleBy2", x), P("Integer", x)),
            Implication(P("Integer", x), P("Rational", x)),
            Implication(P("Rational", x), P("Real", x)),
            P("Even", four)
        ]
        goal = P("Real", four)
        
        result = logic_engine.deduce(premises, goal)
        
        assert result.success, "Mathematical reasoning fallito"
        assert result.steps_count == 13, f"Steps: {result.steps_count} != 13"
        assert result.depth == 13, f"Depth: {result.depth} != 13"
    
    @pytest.mark.timeout(0.25)  # Timeout automatico invece di cronometro manuale
    def test_performance_requirements(self, logic_engine):
        """
        Test che performance sia sotto i requisiti (<250ms).

        Usa pytest timeout per evitare rumore da carico variabile su CI runners.
        """
        # Test catena 12-step (caso più pesante)
        premises = [Implication(P(f"P{i}"), P(f"P{i+1}")) for i in range(12)]
        premises.append(P("P0"))
        goal = P("P12")

        result = logic_engine.deduce(premises, goal)

        assert result.success, "Performance test fallito"
        assert result.steps_count == 25, f"Steps: {result.steps_count} != 25"
        # Se arriviamo qui, il timeout non è scattato = performance OK

    def test_negative_case_impossible_deduction(self, logic_engine):
        """
        Test negativo: deduzione che NON dovrebbe riuscire.

        Verifica che il motore non "indovini" oltre i limiti logici.
        """
        premises = [
            Implication(P("A"), P("B")),  # A → B
            P("C")                        # C è vero (non collegato ad A o B)
        ]
        goal = P("B")  # Non possiamo dedurre B senza A

        result = logic_engine.deduce(premises, goal)

        assert not result.success, "Deduzione impossibile dovrebbe fallire"
        assert result.steps_count == 0, f"Steps dovrebbe essere 0, got {result.steps_count}"
        assert result.error_message is not None, "Dovrebbe esserci un messaggio di errore"

    def test_determinism_property(self, logic_engine):
        """
        Test determinismo: stesso input → stesso output.

        Verifica che il reasoning sia deterministico e riproducibile.
        """
        premises = [
            Implication(P("X"), P("Y")),
            Implication(P("Y"), P("Z")),
            P("X")
        ]
        goal = P("Z")

        # Esegui la stessa deduzione 3 volte
        results = [logic_engine.deduce(premises, goal) for _ in range(3)]

        # Tutti devono avere stesso risultato
        for i, result in enumerate(results):
            assert result.success, f"Run {i+1} fallito"
            assert result.steps_count == results[0].steps_count, (
                f"Run {i+1}: steps {result.steps_count} != {results[0].steps_count}"
            )
            assert result.depth == results[0].depth, (
                f"Run {i+1}: depth {result.depth} != {results[0].depth}"
            )


# Rimuoviamo il blocco if __name__ == "__main__" per lasciare che pytest gestisca tutto

