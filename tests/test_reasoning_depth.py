#!/usr/bin/env python3
"""
Test unitario per verificare depth calculation corretta.
Formula: steps == depth == 2*n + 1 per catene lineari.
"""

import pytest
import sys
import os

# Aggiungi path per import
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from neuroglyph.logic.logic_engine import FormalLogicEngine
    from neuroglyph.logic.formula import *
    LOGIC_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Logic engine non disponibile: {e}")
    LOGIC_AVAILABLE = False


class TestReasoningDepth:
    """Test per verificare depth calculation corretta."""
    
    @pytest.fixture
    def logic_engine(self):
        """Fixture per logic engine."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        return FormalLogicEngine(max_depth=30, max_steps=200)
    
    @pytest.mark.parametrize("chain_length", [3, 5, 8, 12])
    def test_linear_chain_depth_formula(self, logic_engine, chain_length):
        """Test formula: steps == depth == 2*n + 1."""
        # Crea catena P0 → P1 → P2 → ... → P{chain_length}
        premises = []
        for i in range(chain_length):
            premises.append(Implication(P(f"P{i}"), P(f"P{i+1}")))
        premises.append(P("P0"))  # Premessa iniziale
        
        goal = P(f"P{chain_length}")
        
        result = logic_engine.deduce(premises, goal)
        
        expected_steps = 2 * chain_length + 1
        
        assert result.success, f"Chain length {chain_length} fallita"
        assert result.steps_count == expected_steps, f"Steps: {result.steps_count} != {expected_steps}"
        assert result.depth == expected_steps, f"Depth: {result.depth} != {expected_steps}"
        assert result.depth == result.steps_count, "Depth deve essere uguale a steps"
    
    def test_branching_reasoning(self, logic_engine):
        """Test rami che biforcano: A→B, A→C, B∧C→D."""
        premises = [
            Implication(P("A"), P("B")),           # A → B
            Implication(P("A"), P("C")),           # A → C  
            Implication(Conjunction(P("B"), P("C")), P("D")),  # B ∧ C → D
            P("A")                                 # A è vero
        ]
        goal = P("D")
        
        result = logic_engine.deduce(premises, goal)
        
        assert result.success, "Branching reasoning fallito"
        assert result.steps_count == 8, f"Steps: {result.steps_count} != 8"
        assert result.depth == 8, f"Depth: {result.depth} != 8"
    
    def test_multiple_variables(self, logic_engine):
        """Test variabili multiple: ∀x,y."""
        x = Variable("x")
        y = Variable("y")
        
        premises = [
            Implication(P("Related", x, y), P("Connected", x, y)),
            Implication(P("Connected", x, y), P("Linked", x, y)),
            P("Related", Constant("A"), Constant("B"))
        ]
        goal = P("Linked", Constant("A"), Constant("B"))
        
        result = logic_engine.deduce(premises, goal)
        
        assert result.success, "Multiple variables reasoning fallito"
        assert result.steps_count == 7, f"Steps: {result.steps_count} != 7"
        assert result.depth == 7, f"Depth: {result.depth} != 7"
    
    def test_mathematical_reasoning(self, logic_engine):
        """Test mathematical property chain."""
        x = Variable("x")
        four = Constant("4")
        
        premises = [
            Implication(P("Even", x), P("DivisibleBy2", x)),
            Implication(P("DivisibleBy2", x), P("Integer", x)),
            Implication(P("Integer", x), P("Rational", x)),
            Implication(P("Rational", x), P("Real", x)),
            P("Even", four)
        ]
        goal = P("Real", four)
        
        result = logic_engine.deduce(premises, goal)
        
        assert result.success, "Mathematical reasoning fallito"
        assert result.steps_count == 13, f"Steps: {result.steps_count} != 13"
        assert result.depth == 13, f"Depth: {result.depth} != 13"
    
    def test_performance_requirements(self, logic_engine):
        """Test che performance sia sotto i requisiti (<250ms)."""
        import time
        
        # Test catena 12-step (caso più pesante)
        premises = []
        for i in range(12):
            premises.append(Implication(P(f"P{i}"), P(f"P{i+1}")))
        premises.append(P("P0"))
        goal = P("P12")
        
        start_time = time.perf_counter()
        result = logic_engine.deduce(premises, goal)
        duration = time.perf_counter() - start_time
        
        assert result.success, "Performance test fallito"
        assert duration < 0.250, f"Duration {duration:.3f}s > 250ms"
        assert result.steps_count == 25, f"Steps: {result.steps_count} != 25"


if __name__ == "__main__":
    if LOGIC_AVAILABLE:
        print("🧪 Testing reasoning depth calculation...")
        
        engine = FormalLogicEngine(max_depth=30, max_steps=200)
        test = TestReasoningDepth()
        
        # Test parametrizzati
        for length in [3, 5, 8, 12]:
            try:
                test.test_linear_chain_depth_formula(engine, length)
                print(f"✅ {length}-step chain: PASSED")
            except Exception as e:
                print(f"❌ {length}-step chain: FAILED - {e}")
        
        # Test branching
        try:
            test.test_branching_reasoning(engine)
            print("✅ Branching reasoning: PASSED")
        except Exception as e:
            print(f"❌ Branching reasoning: FAILED - {e}")
        
        # Test multiple variables
        try:
            test.test_multiple_variables(engine)
            print("✅ Multiple variables: PASSED")
        except Exception as e:
            print(f"❌ Multiple variables: FAILED - {e}")
        
        # Test mathematical
        try:
            test.test_mathematical_reasoning(engine)
            print("✅ Mathematical reasoning: PASSED")
        except Exception as e:
            print(f"❌ Mathematical reasoning: FAILED - {e}")
        
        # Test performance
        try:
            test.test_performance_requirements(engine)
            print("✅ Performance requirements: PASSED")
        except Exception as e:
            print(f"❌ Performance requirements: FAILED - {e}")
            
        print("\n🎯 DEPTH CALCULATION TESTS COMPLETED")
    else:
        print("❌ Logic engine non disponibile")
