{"metadata": {"name": "NEUROGLYPH Certified Dataset v2.0 (Expanded)", "description": "Expanded epistemologically pure dataset for NG GODMODE v2 fine-tuning", "parser": "Formal grammar-based", "certification_date": "2025-06-09", "total_patterns": 250, "validated_patterns": 250, "failed_patterns": 0, "validation_rate": 1.0, "min_fidelity_required": 0.95, "audit_lock_verified": true, "expansion_info": {"original_patterns": 25, "generated_patterns": 225, "expansion_ratio": 10.0}}, "patterns": [{"input": "∀x: P(x)", "output": "∀x: P(x)", "fidelity": 1.0, "ast_type": "UniversalQuantification"}, {"input": "∃x: P(x)", "output": "∃x: P(x)", "fidelity": 1.0, "ast_type": "ExistentialQuantification"}, {"input": "∀x ∈ ℝ: P(x)", "output": "∀x ∈ ℝ: P(x)", "fidelity": 1.0, "ast_type": "UniversalQuantification"}, {"input": "∃x ∈ ℕ: P(x)", "output": "∃x ∈ ℕ: P(x)", "fidelity": 1.0, "ast_type": "ExistentialQuantification"}, {"input": "∀x ∃y: R(x,y)", "output": "∀x ∃y: R(x,y)", "fidelity": 1.0, "ast_type": "UniversalQuantification"}, {"input": "P ⇒ Q", "output": "P ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication"}, {"input": "P ∧ Q", "output": "P ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction"}, {"input": "P ∨ Q", "output": "P ∨ Q", "fidelity": 1.0, "ast_type": "LogicalDisjunction"}, {"input": "¬P", "output": "¬P", "fidelity": 1.0, "ast_type": "LogicalNegation"}, {"input": "¬(P ∨ Q)", "output": "¬(P ∨ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation"}, {"input": "P ∧ Q ⇒ R", "output": "P ∧ Q ⇒ R", "fidelity": 1.0, "ast_type": "MaterialImplication"}, {"input": "A ∪ B", "output": "A ∪ B", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "A ∩ B", "output": "A ∩ B", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "x ∈ A", "output": "x ∈ A", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "A ⊆ B", "output": "A ⊆ B", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "x + y", "output": "x + y", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "f(x)", "output": "f(x)", "fidelity": 1.0, "ast_type": "FunctionCall"}, {"input": "x = y", "output": "x = y", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "∫ f(x) dx", "output": "∫ f(x) dx", "fidelity": 1.0, "ast_type": "Integral"}, {"input": "∑ xᵢ", "output": "∑ xᵢ", "fidelity": 1.0, "ast_type": "Summation"}, {"input": "∏ xᵢ", "output": "∏ xᵢ", "fidelity": 1.0, "ast_type": "Product"}, {"input": "∂f/∂x", "output": "∂f/∂x", "fidelity": 1.0, "ast_type": "PartialDerivative"}, {"input": "(P ∨ Q)", "output": "(P ∨ Q)", "fidelity": 1.0, "ast_type": "GroupedExpression"}, {"input": "f(x,y)", "output": "f(x,y)", "fidelity": 1.0, "ast_type": "FunctionCall"}, {"input": "x ≠ y", "output": "x ≠ y", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "¬(G ∧ Q)", "output": "¬(G ∧ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "H ∨ F", "output": "H ∨ F", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "∀c: T(c)", "output": "∀c: T(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∃m ∀a: R(m,a)", "output": "∃m ∀a: R(m,a)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∃c: T(c)", "output": "∃c: T(c)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∀k: T(a,k)", "output": "∀a ∀k: T(a,k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∈ 𝔹: Q(a)", "output": "∀a ∈ 𝔹: Q(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬(S ∨ P)", "output": "¬(S ∨ P)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∈ ℚ: R(a)", "output": "∀a ∈ ℚ: R(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬(Q ∧ G)", "output": "¬(Q ∧ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "G ⇔ S", "output": "G ⇔ S", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀a: G(a)", "output": "∀a: G(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "H ∨ G", "output": "H ∨ G", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "∃n: H(n)", "output": "∃n: H(n)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "F ⇒ S", "output": "F ⇒ S", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∀k: G(k)", "output": "∀k: G(k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀y ∀z: R(y,z)", "output": "∀y ∀z: R(y,z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "(P ∧ R) ⇒ P", "output": "(P ∧ R) ⇒ P", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∃y: Q(y)", "output": "∃y: Q(y)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀c ∈ 𝔹: T(c)", "output": "∀c ∈ 𝔹: T(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "H ∧ Q ∧ P", "output": "H ∧ Q ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "G ∨ T", "output": "G ∨ T", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "∃b ∈ ℚ: S(b)", "output": "∃b ∈ ℚ: S(b)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀z ∃b: S(z,b)", "output": "∀z ∃b: S(z,b)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "S ∧ Q ∧ F", "output": "S ∧ Q ∧ F", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "G(n) ⇒ S(n)", "output": "G(n) ⇒ S(n)", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∀b ∀a: R(b,a)", "output": "∀b ∀a: R(b,a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "T ∨ G", "output": "T ∨ G", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "S ∧ Q", "output": "S ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "(T ⇒ H) ∧ (H ⇒ T)", "output": "(T ⇒ H) ∧ (H ⇒ T)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "F ∧ S", "output": "F ∧ S", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "S ⇔ Q", "output": "S ⇔ Q", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀z: T(z)", "output": "∀z: T(z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∃x: T(x)", "output": "∃x: T(x)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬(H ∨ T)", "output": "¬(H ∨ T)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "T ∨ H", "output": "T ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "(R ∧ S) ⇒ Q", "output": "(R ∧ S) ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "S(b) ⇒ R(b)", "output": "S(b) ⇒ R(b)", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∃b: T(b)", "output": "∃b: T(b)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬T", "output": "¬T", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "∀y ∀n: T(y,n)", "output": "∀y ∀n: T(y,n)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬(S ∨ R)", "output": "¬(S ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "F ∧ G", "output": "F ∧ G", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "(F ∧ R) ⇒ H", "output": "(F ∧ R) ⇒ H", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "¬(R ∨ S)", "output": "¬(R ∨ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "(G ⇒ P) ∧ (P ⇒ G)", "output": "(G ⇒ P) ∧ (P ⇒ G)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∃k: T(a,k)", "output": "∀a ∃k: T(a,k)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∃n ∀x: S(n,x)", "output": "∃n ∀x: S(n,x)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬T", "output": "¬T", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "¬(G ∨ R)", "output": "¬(G ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "T ∨ R", "output": "T ∨ R", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∈ ℤ: T(a)", "output": "∀a ∈ ℤ: T(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬F", "output": "¬F", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "(T ⇒ S) ∧ (S ⇒ T)", "output": "(T ⇒ S) ∧ (S ⇒ T)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀x ∃c: T(x,c)", "output": "∀x ∃c: T(x,c)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "(F ∧ T) ⇒ H", "output": "(F ∧ T) ⇒ H", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "F ⇒ P", "output": "F ⇒ P", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "¬P", "output": "¬P", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "P ⇒ F", "output": "P ⇒ F", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∃a ∃b: S(a,b)", "output": "∃a ∃b: S(a,b)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "Q ∨ H", "output": "Q ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "R ∨ S", "output": "R ∨ S", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "(T ⇒ Q) ∧ (Q ⇒ T)", "output": "(T ⇒ Q) ∧ (Q ⇒ T)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀b ∃x: T(b,x)", "output": "∀b ∃x: T(b,x)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "(P ∧ Q) ⇒ T", "output": "(P ∧ Q) ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "Q ∨ F", "output": "Q ∨ F", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "G ⇔ R", "output": "G ⇔ R", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "F ⇒ G", "output": "F ⇒ G", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "(P ⇒ S) ∧ (S ⇒ P)", "output": "(P ⇒ S) ∧ (S ⇒ P)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "(F ∧ T) ⇒ T", "output": "(F ∧ T) ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "¬(F ∨ R)", "output": "¬(F ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "(S ⇒ P) ∧ (P ⇒ S)", "output": "(S ⇒ P) ∧ (P ⇒ S)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "F ⇒ Q", "output": "F ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∀k: Q(k)", "output": "∀k: Q(k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "H ∧ S ∧ Q", "output": "H ∧ S ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "H ∧ Q ∧ F", "output": "H ∧ Q ∧ F", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "P ⇒ H", "output": "P ⇒ H", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "¬(P ∨ G)", "output": "¬(P ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "X ⊃ B", "output": "X ⊃ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Z ∩ X", "output": "Z ∩ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃y ∈ D: y ∉ Z", "output": "∃y ∈ D: y ∉ Z", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "Y ∪ B", "output": "Y ∪ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Z \\ Y", "output": "Z \\ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Z ∩ D", "output": "Z ∩ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "m ∉ Y", "output": "m ∉ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∀a ∈ A: a ∈ C", "output": "∀a ∈ A: a ∈ C", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "set_theory", "generated": true}, {"input": "(A ∩ D) ⊆ Y", "output": "(A ∩ D) ⊆ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "X ∩ C", "output": "X ∩ C", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "n ∉ Z", "output": "n ∉ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∀z ∈ X: z ∈ A", "output": "∀z ∈ X: z ∈ A", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "set_theory", "generated": true}, {"input": "Z ∩ (B ∪ D)", "output": "Z ∩ (B ∪ D)", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "X ∩ B", "output": "X ∩ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "z ∉ X", "output": "z ∉ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "D ⊂ B", "output": "D ⊂ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C ⊇ B", "output": "C ⊇ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "a ∉ B", "output": "a ∉ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C ⊇ B", "output": "C ⊇ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃n ∈ A: n ∉ D", "output": "∃n ∈ A: n ∉ D", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "b ∉ Z", "output": "b ∉ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "X ∪ D", "output": "X ∪ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "x ∈ B", "output": "x ∈ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ∩ C", "output": "Y ∩ C", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ∩ (B ∪ B)", "output": "Y ∩ (B ∪ B)", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C \\ A", "output": "C \\ A", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃c ∈ D: c ∉ A", "output": "∃c ∈ D: c ∉ A", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "Y ⊇ D", "output": "Y ⊇ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃x ∈ B: x ∉ C", "output": "∃x ∈ B: x ∉ C", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "(Y ∩ Z) ⊆ B", "output": "(Y ∩ Z) ⊆ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ∩ Z", "output": "Y ∩ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "(B ∩ Y) ⊆ Y", "output": "(B ∩ Y) ⊆ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C \\ X", "output": "C \\ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "A ⊃ C", "output": "A ⊃ C", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ⊆ B", "output": "Y ⊆ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ⊇ Z", "output": "Y ⊇ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "X ∪ D", "output": "X ∪ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "B ∩ A", "output": "B ∩ A", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y \\ Z", "output": "Y \\ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "A ⊂ Z", "output": "A ⊂ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "A ∩ X", "output": "A ∩ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∀m ∈ Z: m ∈ A", "output": "∀m ∈ Z: m ∈ A", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "set_theory", "generated": true}, {"input": "(X ∪ B) ∩ A", "output": "(X ∪ B) ∩ A", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "(B ∪ C) ∩ D", "output": "(B ∪ C) ∩ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃c ∈ A: c ∉ X", "output": "∃c ∈ A: c ∉ X", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "Z ∪ B", "output": "Z ∪ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "c ∈ X", "output": "c ∈ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "D \\ B", "output": "D \\ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "D ⊇ X", "output": "D ⊇ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "x ∈ B", "output": "x ∈ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Z ∩ (Y ∪ A)", "output": "Z ∩ (Y ∪ A)", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C ⊆ B", "output": "C ⊆ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C ⊆ Y", "output": "C ⊆ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃x ∈ X: x ∉ Y", "output": "∃x ∈ X: x ∉ Y", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "Z \\ Y", "output": "Z \\ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "B ∩ (Y ∪ C)", "output": "B ∩ (Y ∪ C)", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "a ≤ z", "output": "a ≤ z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "f(x)", "output": "f(x)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "x ≥ y", "output": "x ≥ y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "z = b", "output": "z = b", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(y)", "output": "ψ(y)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "m ≤ y", "output": "m ≤ y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∃b ∈ ℂ: h(b) = x", "output": "∃b ∈ ℂ: h(b) = x", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "a < n", "output": "a < n", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "b ≤ m", "output": "b ≤ m", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "x² + c² = a²", "output": "x² + c² = a²", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "a = x", "output": "a = x", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "φ(z)", "output": "φ(z)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "f(x,n)", "output": "f(x,n)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "b · z = n", "output": "b · z = n", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "χ(y,x)", "output": "χ(y,x)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "χ(y,b)", "output": "χ(y,b)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "∃y ∈ ℤ: h(y) = x", "output": "∃y ∈ ℤ: h(y) = x", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "n ≥ c", "output": "n ≥ c", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "m ≤ x", "output": "m ≤ x", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "m < b", "output": "m < b", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "x ≤ a", "output": "x ≤ a", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "φ(z,y)", "output": "φ(z,y)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "a ≥ y", "output": "a ≥ y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∃c ∈ ℂ: χ(c) = m", "output": "∃c ∈ ℂ: χ(c) = m", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "c + x = z", "output": "c + x = z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∀n ∈ 𝔹: g(n) ∈ ℤ", "output": "∀n ∈ 𝔹: g(n) ∈ ℤ", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(z)", "output": "ψ(z)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "z ≥ b", "output": "z ≥ b", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "n = a", "output": "n = a", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∃z ∈ ℚ: φ(z) = b", "output": "∃z ∈ ℚ: φ(z) = b", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "h(y) = m", "output": "h(y) = m", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "z = k", "output": "z = k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "b ≠ a", "output": "b ≠ a", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "y ≠ k", "output": "y ≠ k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "z ≠ x", "output": "z ≠ x", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "k · m = a", "output": "k · m = a", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "b = x", "output": "b = x", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∀k ∈ ℂ: φ(k) ∈ ℝ", "output": "∀k ∈ ℂ: φ(k) ∈ ℝ", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "g(a,c)", "output": "g(a,c)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "k ≠ c", "output": "k ≠ c", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∃a ∈ ℤ: h(a) = y", "output": "∃a ∈ ℤ: h(a) = y", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "χ(z)", "output": "χ(z)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "c ≥ y", "output": "c ≥ y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "z > b", "output": "z > b", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "a² + z² = c²", "output": "a² + z² = c²", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(a)", "output": "ψ(a)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "x = y", "output": "x = y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "c ≥ m", "output": "c ≥ m", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "k ≥ n", "output": "k ≥ n", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(m)", "output": "ψ(m)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "∀m ∈ ℝ: h(m) ∈ ℝ", "output": "∀m ∈ ℝ: h(m) ∈ ℝ", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "χ(m) = k", "output": "χ(m) = k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "f(a,z)", "output": "f(a,z)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "b ≠ m", "output": "b ≠ m", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(m) = k", "output": "ψ(m) = k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "x ≥ k", "output": "x ≥ k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "¬(∃a: Q(a)) ⇔ (∀a: ¬Q(a))", "output": "¬(∃a: Q(a)) ⇔ (∀a: ¬Q(a))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "∀b ∈ ℕ: ∃a ∈ 𝔹: φ(b) = a", "output": "∀b ∈ ℕ: ∃a ∈ 𝔹: φ(b) = a", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀c ∈ ℚ: ∃x ∈ ℂ: ψ(c) = x", "output": "∀c ∈ ℚ: ∃x ∈ ℂ: ψ(c) = x", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀n ∈ ℂ: ∃z ∈ 𝔹: ψ(n) = z", "output": "∀n ∈ ℂ: ∃z ∈ 𝔹: ψ(n) = z", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(m ∈ A) ∧ (χ(m) ∈ B)", "output": "(m ∈ A) ∧ (χ(m) ∈ B)", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀c ∈ ℝ: ∃x ∈ ℤ: g(c) = x", "output": "∀c ∈ ℝ: ∃x ∈ ℤ: g(c) = x", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(A ∩ D) ⊆ (A ∪ Y)", "output": "(A ∩ D) ⊆ (A ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀a ∈ ℕ: ∃m ∈ ℕ: g(a) = m", "output": "∀a ∈ ℕ: ∃m ∈ ℕ: g(a) = m", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀k ∈ ℕ: ∃c ∈ ℕ: g(k) = c", "output": "∀k ∈ ℕ: ∃c ∈ ℕ: g(k) = c", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(D ∩ Z) ⊆ (C ∪ C)", "output": "(D ∩ Z) ⊆ (C ∪ C)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀k ∈ ℕ: ∃n ∈ 𝔹: g(k) = n", "output": "∀k ∈ ℕ: ∃n ∈ 𝔹: g(k) = n", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(n ∈ C) ∧ (φ(n) ∈ B)", "output": "(n ∈ C) ∧ (φ(n) ∈ B)", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(∀a: R(a)) ⇒ (∃c: G(c))", "output": "(∀a: R(a)) ⇒ (∃c: G(c))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(Z ∪ X) ∩ (A ∪ B)", "output": "(Z ∪ X) ∩ (A ∪ B)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(G ∧ S) ∨ (F ∧ Q)", "output": "(G ∧ S) ∨ (F ∧ Q)", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(X ∩ Z) ⊆ (A ∪ Y)", "output": "(X ∩ Z) ⊆ (A ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀a ∈ ℤ: ∃k ∈ ℚ: ψ(a) = k", "output": "∀a ∈ ℤ: ∃k ∈ ℚ: ψ(a) = k", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(∀x: H(x)) ⇒ (∃y: P(y))", "output": "(∀x: H(x)) ⇒ (∃y: P(y))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(B ∩ D) ⊆ (B ∪ A)", "output": "(B ∩ D) ⊆ (B ∪ A)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(m ∈ A) ∧ (h(m) ∈ X)", "output": "(m ∈ A) ∧ (h(m) ∈ X)", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(b ∈ B) ∧ (f(b) ∈ C)", "output": "(b ∈ B) ∧ (f(b) ∈ C)", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀y ∈ ℕ: ∃b ∈ ℂ: h(y) = b", "output": "∀y ∈ ℕ: ∃b ∈ ℂ: h(y) = b", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(Z ∩ B) ⊆ (Y ∪ Y)", "output": "(Z ∩ B) ⊆ (Y ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀c ∈ ℤ: ∃k ∈ ℚ: φ(c) = k", "output": "∀c ∈ ℤ: ∃k ∈ ℚ: φ(c) = k", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(B ∩ A) ⊆ (D ∪ A)", "output": "(B ∩ A) ⊆ (D ∪ A)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(D ∪ X) ∩ (C ∪ Z)", "output": "(D ∪ X) ∩ (C ∪ Z)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(∀y: S(y)) ⇒ (∃x: Q(x))", "output": "(∀y: S(y)) ⇒ (∃x: Q(x))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "¬(∃x: G(x)) ⇔ (∀x: ¬G(x))", "output": "¬(∃x: G(x)) ⇔ (∀x: ¬G(x))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(X ∪ Z) ∩ (B ∪ Y)", "output": "(X ∪ Z) ∩ (B ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀a ∈ 𝔹: ∃z ∈ ℕ: g(a) = z", "output": "∀a ∈ 𝔹: ∃z ∈ ℕ: g(a) = z", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "¬(∃k: Q(k)) ⇔ (∀k: ¬Q(k))", "output": "¬(∃k: Q(k)) ⇔ (∀k: ¬Q(k))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(Y ∩ C) ⊆ (X ∪ D)", "output": "(Y ∩ C) ⊆ (X ∪ D)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(∀x: S(x)) ⇒ (∃a: T(a))", "output": "(∀x: S(x)) ⇒ (∃a: T(a))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(C ∪ B) ∩ (C ∪ D)", "output": "(C ∪ B) ∩ (C ∪ D)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(X ∪ Z) ∩ (Z ∪ Y)", "output": "(X ∪ Z) ∩ (Z ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}], "failed_patterns": []}