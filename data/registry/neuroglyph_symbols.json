{"metadata": {"version": "1.0.0", "created_from_dataset": "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl", "total_symbols": 68, "total_examples_analyzed": 20000, "description": "Complete NEUROGLYPH symbol registry extracted from dataset"}, "symbols": ["¬", "±", "²", "³", "è", "ò", "λ", "ℝ", "→", "↔", "↕", "⇄", "⇆", "⇒", "⇔", "∀", "∂", "∃", "∅", "∆", "∇", "∈", "∉", "∏", "∑", "∘", "√", "∞", "∧", "∨", "∩", "∪", "∫", "∮", "∯", "∰", "∱", "∲", "∳", "∴", "≈", "≠", "≡", "≤", "≥", "⊂", "⊆", "⊇", "⊈", "⊢", "⊤", "⊥", "⊨", "⊭", "⚡", "⟦", "⟧", "⟨", "⟩", "⟷", "⤴", "️", "🏛", "📖", "🔄", "🔢", "🔮", "🧠"], "categories": {"logical": ["¬", "⇒", "⇔", "∀", "∃", "∧", "∨", "∴", "≡", "⊢", "⊥"], "mathematical": ["±", "²", "³", "∂", "∇", "∏", "∑", "∞", "∫", "≈", "≠", "≤", "≥"], "set_theory": ["∅", "∈", "∉", "∩", "∪", "⊂", "⊆", "⊇"], "arrows": ["→", "↔", "↕", "⤴"], "greek": ["λ", "ℝ"], "emoji": ["🏛", "📖", "🔄", "🔢", "🔮", "🧠"], "punctuation": ["è", "ò", "️"], "other": ["⇄", "⇆", "∆", "∘", "√", "∮", "∯", "∰", "∱", "∲", "∳", "⊈", "⊤", "⊨", "⊭", "⚡", "⟦", "⟧", "⟨", "⟩", "⟷"]}, "frequency": {"🧠": 10684, "∧": 46926, "⇒": 24235, "⊢": 56878, "∃": 14822, "∀": 13170, "≈": 300, "⚡": 7888, "⤴": 6960, "⟦": 7872, "⟧": 7872, "→": 9358, "🔄": 4624, "🏛": 3248, "️": 3248, "🔢": 5560, "∫": 6923, "∞": 435, "⊂": 3871, "⊆": 1640, "≠": 6450, "∅": 3972, "∩": 460, "∈": 4932, "∉": 576, "🔮": 1108, "ò": 1108, "📖": 328, "√": 340, "⇆": 240, "≤": 575, "⇔": 1960, "¬": 3033, "∨": 2563, "∴": 1520, "è": 75, "⊥": 2183, "∘": 1120, "⊭": 340, "λ": 540, "∑": 727, "≥": 572, "∳": 300, "∲": 300, "ℝ": 581, "∪": 230, "↕": 390, "²": 1099, "³": 1099, "⊈": 276, "⊤": 300, "∰": 300, "⟩": 340, "∯": 300, "∇": 300, "±": 300, "↔": 300, "≡": 440, "⟷": 300, "⊨": 340, "∂": 698, "⟨": 340, "∆": 300, "⊇": 692, "∏": 440, "⇄": 240, "∮": 300, "∱": 300}, "contexts": {"🧠": [{"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "multistep_80102", "context": "🧠 P ∧ Q ∧ (P ⇒ R) ⊢ ? ⊢ Q ∧ R ⊢ ∫ ⊢ ∧..."}, {"example_id": "logical_bf018c73", "context": "🧠 T ⇒ S ∧ S ⇒ R ∧ T ⊢ ? ⊢ S ∧ ⊢ R ∧ ∀ ∃ ⇒ ⊢..."}], "∧": [{"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "math_d5286316", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "⇒": [{"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "multistep_80102", "context": "🧠 P ∧ Q ∧ (P ⇒ R) ⊢ ? ⊢ Q ∧ R ⊢ ∫ ⊢ ∧..."}], "⊢": [{"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}], "∃": [{"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "logical_bf018c73", "context": "🧠 T ⇒ S ∧ S ⇒ R ∧ T ⊢ ? ⊢ S ∧ ⊢ R ∧ ∀ ∃ ⇒ ⊢..."}, {"example_id": "story_existential_condition_39", "context": "📖 Se Luca ∃ dati ∈ insieme A ⇒ successo ⊢ ? ⊢ ∃x ∈..."}], "∀": [{"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "logical_bf018c73", "context": "🧠 T ⇒ S ∧ S ⇒ R ∧ T ⊢ ? ⊢ S ∧ ⊢ R ∧ ∀ ∃ ⇒ ⊢..."}, {"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}], "≈": [{"example_id": "logical_15cc58db", "context": "🧠 Q ∧ Q ⇒ S ⊢ ? ⊢ S ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "logical_5b415060", "context": "🧠 S ⇒ Q ∧ Q ⇒ T ∧ S ⊢ ? ⊢ Q ∧ ⊢ T ⊢ ⇒ ∃ ∀ ∧ ≈..."}, {"example_id": "math_03287c06", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "⚡": [{"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}], "⤴": [{"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}], "⟦": [{"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_959e86de", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}], "⟧": [{"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_959e86de", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}], "→": [{"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_959e86de", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}], "🔄": [{"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_959e86de", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}, {"example_id": "code_3b054c2c", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}], "🏛": [{"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_959e86de", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}, {"example_id": "code_959e86de", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}], "️": [{"example_id": "code_e3d00eb3", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b ⟦ ⚡ ⤴ ..."}, {"example_id": "code_959e86de", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}, {"example_id": "code_959e86de", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}], "🔢": [{"example_id": "math_d5286316", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_191a9e18", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_89f97aeb", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "∫": [{"example_id": "math_d5286316", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "multistep_80102", "context": "🧠 P ∧ Q ∧ (P ⇒ R) ⊢ ? ⊢ Q ∧ R ⊢ ∫ ⊢ ∧..."}, {"example_id": "math_191a9e18", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "∞": [{"example_id": "math_d5286316", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_89f97aeb", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "story_infinite_division_115", "context": "📖 Luca ha ∞ libri e divide in ↕ modo ⊢ ? ⊢ ∀ parte..."}], "⊂": [{"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}, {"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}, {"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}], "⊆": [{"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}, {"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}, {"example_id": "complex_set_theory_1391", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}], "≠": [{"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}, {"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}, {"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}], "∅": [{"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}, {"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}, {"example_id": "complex_set_theory_1699", "context": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ? ⊢ A ⊂ C ∧ A ≠..."}], "∩": [{"example_id": "structure_∅_225", "context": "🧠 C ∩ Y = ∅ ∧ x ∈ C ⊢ ? ⊢ x ∉ Y ∅ ⊢ ∧..."}, {"example_id": "structure_∅_61", "context": "🧠 A ∩ X = ∅ ∧ x ∈ A ⊢ ? ⊢ x ∉ X ∅ ⊢ ∧..."}, {"example_id": "structure_∅_105", "context": "🧠 D ∩ A = ∅ ∧ x ∈ D ⊢ ? ⊢ x ∉ A ∅ ⊢ ∧..."}], "∈": [{"example_id": "structure_∅_225", "context": "🧠 C ∩ Y = ∅ ∧ x ∈ C ⊢ ? ⊢ x ∉ Y ∅ ⊢ ∧..."}, {"example_id": "story_existential_condition_39", "context": "📖 Se Luca ∃ dati ∈ insieme A ⇒ successo ⊢ ? ⊢ ∃x ∈..."}, {"example_id": "story_existential_condition_39", "context": "📖 Se Luca ∃ dati ∈ insieme A ⇒ successo ⊢ ? ⊢ ∃x ∈..."}], "∉": [{"example_id": "structure_∅_225", "context": "🧠 C ∩ Y = ∅ ∧ x ∈ C ⊢ ? ⊢ x ∉ Y ∅ ⊢ ∧..."}, {"example_id": "structure_∅_61", "context": "🧠 A ∩ X = ∅ ∧ x ∈ A ⊢ ? ⊢ x ∉ X ∅ ⊢ ∧..."}, {"example_id": "structure_∅_105", "context": "🧠 D ∩ A = ∅ ∧ x ∈ D ⊢ ? ⊢ x ∉ A ∅ ⊢ ∧..."}], "🔮": [{"example_id": "reverse_set_reverse_164", "context": "🔮 Data la conclusione: ⊢ A ⊂ C, quale formula può ..."}, {"example_id": "reverse_function_reverse_90", "context": "🔮 Data la conclusione: ⊢ f(x) = y, quale formula p..."}, {"example_id": "reverse_function_reverse_439", "context": "🔮 Data la conclusione: ⊢ f(x) = y, quale formula p..."}], "ò": [{"example_id": "reverse_set_reverse_164", "context": "🔮 Data la conclusione: ⊢ A ⊂ C, quale formula può ..."}, {"example_id": "reverse_function_reverse_90", "context": "🔮 Data la conclusione: ⊢ f(x) = y, quale formula p..."}, {"example_id": "reverse_function_reverse_439", "context": "🔮 Data la conclusione: ⊢ f(x) = y, quale formula p..."}], "📖": [{"example_id": "story_existential_condition_39", "context": "📖 Se Luca ∃ dati ∈ insieme A ⇒ successo ⊢ ? ⊢ ∃x ∈..."}, {"example_id": "story_function_application_105", "context": "📖 Maria applica f: A → B su punti ∧ f è iniettiva ..."}, {"example_id": "story_universal_uniqueness_0", "context": "📖 Sofia colleziona elementi dove ∀ elemento ≠ prec..."}], "√": [{"example_id": "logical_4b23b1a6", "context": "🧠 S ⇒ R ∧ R ⇒ P ∧ S ⊢ ? ⊢ R ∧ ⊢ P ⊢ ⇒ ∃ ∀ ∧ √..."}, {"example_id": "math_3dd813fb", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_6cfd61a6", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "⇆": [{"example_id": "code_959e86de", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}, {"example_id": "code_1a26484d", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}, {"example_id": "code_892d065f", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}], "≤": [{"example_id": "math_191a9e18", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "multistep_32724", "context": "🧠 P ∧ Q ∧ (P ⇒ R) ⊢ ? ⊢ Q ∧ R ∃ ≤ ⊢ ∧..."}, {"example_id": "multistep_40232", "context": "🧠 ∀x (P(x) ⇒ Q(x)) ∧ ∃x P(x) ∧ ∀x (Q(x) ⇒ R(x)) ⊢ ..."}], "⇔": [{"example_id": "complex_propositional_logic_1295", "context": "🧠 (P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S) ⊢ ? ⊢ (..."}, {"example_id": "complex_propositional_logic_1295", "context": "🧠 (P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S) ⊢ ? ⊢ (..."}, {"example_id": "logical_cd440cc5", "context": "🧠 P ⇒ Q ∧ Q ⇒ R ∧ P ⊢ ? ⊢ Q ∧ ⊢ R ∃ ∧ ⇒ ⊢ ∀ ⇔..."}], "¬": [{"example_id": "complex_propositional_logic_1295", "context": "🧠 (P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S) ⊢ ? ⊢ (..."}, {"example_id": "complex_propositional_logic_1295", "context": "🧠 (P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S) ⊢ ? ⊢ (..."}, {"example_id": "complex_propositional_logic_1295", "context": "🧠 (P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S) ⊢ ? ⊢ (..."}], "∨": [{"example_id": "complex_propositional_logic_1295", "context": "🧠 (P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S) ⊢ ? ⊢ (..."}, {"example_id": "complex_propositional_logic_1295", "context": "🧠 (P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S) ⊢ ? ⊢ (..."}, {"example_id": "multistep_99651", "context": "🧠 P ∧ Q ∧ (P ⇒ R) ⊢ ? ⊢ Q ∧ R ∨ ≥ ⊢ ∧..."}], "∴": [{"example_id": "complex_propositional_logic_1295", "context": "🧠 (P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S) ⊢ ? ⊢ (..."}, {"example_id": "complex_propositional_logic_1295", "context": "🧠 (P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S) ⊢ ? ⊢ (..."}, {"example_id": "structure_∴_206", "context": "🧠 P ⇒ Q ∧ Q ⇒ R ∴ ? ⊢ P ⇒ R ∴ ⊢ ∧..."}], "è": [{"example_id": "story_function_application_105", "context": "📖 Maria applica f: A → B su punti ∧ f è iniettiva ..."}, {"example_id": "story_function_application_211", "context": "📖 Luca applica f: A → B su dati ∧ f è iniettiva ⊢ ..."}, {"example_id": "story_function_application_100", "context": "📖 Andrea applica f: A → B su punti ∧ f è iniettiva..."}], "⊥": [{"example_id": "logical_b56a5b26", "context": "🧠 Q ⇒ R ∧ R ⇒ P ∧ Q ⊢ ? ⊢ R ∧ ⊢ P ∃ ∧ ⇒ ⊢ ∀ ⊥..."}, {"example_id": "logical_ed83746e", "context": "🧠 ∀x (P(x) ⇒ T(x)) ∧ ∃x P(x) ∧ ∀x (T(x) ⇒ R(x)) ⊢ ..."}, {"example_id": "complex_mathematical_logic_1665", "context": "🧠 ∀w ∃v ∈ ℝ: w ≠ v ⇒ f(w) ⊥ f(v) ⊢ ? ⊢ ∃x,y: f(x) ..."}], "∘": [{"example_id": "complex_function_theory_761", "context": "🧠 ∀f: A → B, ∃g: B → A | g ∘ f = id_A ⇒ f ∈ Inj(A,..."}, {"example_id": "complex_function_theory_761", "context": "🧠 ∀f: A → B, ∃g: B → A | g ∘ f = id_A ⇒ f ∈ Inj(A,..."}, {"example_id": "complex_function_theory_476", "context": "🧠 ∀f: A → B, ∃g: B → A | g ∘ f = id_A ⇒ f ∈ Inj(A,..."}], "⊭": [{"example_id": "logical_7ded9646", "context": "🧠 P ⇒ T ∧ T ⇒ R ∧ P ⊢ ? ⊢ T ∧ ⊢ R ∃ ∧ ⇒ ⊢ ∀ ⊭..."}, {"example_id": "logical_fcb2c07c", "context": "🧠 T ∧ T ⇒ Q ⊢ ? ⊢ Q ∃ ∧ ⇒ ⊢ ∀ ⊭..."}, {"example_id": "logical_f6af12d6", "context": "🧠 R ⇒ Q ∧ Q ⇒ S ∧ R ⊢ ? ⊢ Q ∧ ⊢ S ∃ ∧ ⇒ ⊢ ∀ ⊭..."}], "λ": [{"example_id": "code_f411c2f6", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}, {"example_id": "code_0e47fa40", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b 🔄 ⚡ ⟧ ..."}, {"example_id": "code_9a3bddcb", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}], "∑": [{"example_id": "math_0128a789", "context": "🔢 x + 7 = 6 ⊢ x = ? ⊢ x = -1 ⊢ ∫ ∧ ∑..."}, {"example_id": "multistep_96734", "context": "🧠 ∀x (P(x) ⇒ Q(x)) ∧ ∃x P(x) ∧ ∀x (Q(x) ⇒ R(x)) ⊢ ..."}, {"example_id": "multistep_95904", "context": "🧠 ∀x (P(x) ⇒ Q(x)) ∧ ∃x P(x) ∧ ∀x (Q(x) ⇒ R(x)) ⊢ ..."}], "≥": [{"example_id": "multistep_99651", "context": "🧠 P ∧ Q ∧ (P ⇒ R) ⊢ ? ⊢ Q ∧ R ∨ ≥ ⊢ ∧..."}, {"example_id": "multistep_40232", "context": "🧠 ∀x (P(x) ⇒ Q(x)) ∧ ∃x P(x) ∧ ∀x (Q(x) ⇒ R(x)) ⊢ ..."}, {"example_id": "multistep_88585", "context": "🧠 P ∧ Q ∧ (P ⇒ R) ⊢ ? ⊢ Q ∧ R ∃ ≥ ⊢ ∧..."}], "∳": [{"example_id": "math_51ae924e", "context": "🔢 x + 3 = 9 ⊢ x = ? ⊢ x = 6 ⊢ ∫ ∧ ∳..."}, {"example_id": "math_d7104d73", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∳..."}, {"example_id": "math_24e188d4", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "∲": [{"example_id": "math_b4a9c3d7", "context": "🔢 x + 9 = 2 ⊢ x = ? ⊢ x = -7 ⊢ ∫ ∧ ∲..."}, {"example_id": "math_9b899995", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∲..."}, {"example_id": "math_3974674d", "context": "🔢 x + 7 = 1 ⊢ x = ? ⊢ x = -6 ⊢ ∫ ∧ ∲..."}], "ℝ": [{"example_id": "complex_mathematical_logic_1665", "context": "🧠 ∀w ∃v ∈ ℝ: w ≠ v ⇒ f(w) ⊥ f(v) ⊢ ? ⊢ ∃x,y: f(x) ..."}, {"example_id": "complex_mathematical_logic_489", "context": "🧠 ∀x ∃y ∈ ℝ: x ≠ y ⇒ f(x) ⊥ f(y) ⊢ ? ⊢ ∃x,y: f(x) ..."}, {"example_id": "complex_mathematical_logic_838", "context": "🧠 ∀v ∃x ∈ ℝ: v ≠ x ⇒ f(v) ⊥ f(x) ⊢ ? ⊢ ∃x,y: f(x) ..."}], "∪": [{"example_id": "structure_∪_27", "context": "🧠 D ∪ B ⊢ ? ⊢ D ≠ ∅ ∨ B ≠ ∅ ∪ ⊢ ∧..."}, {"example_id": "structure_∪_27", "context": "🧠 D ∪ B ⊢ ? ⊢ D ≠ ∅ ∨ B ≠ ∅ ∪ ⊢ ∧..."}, {"example_id": "structure_∪_83", "context": "🧠 Z ∪ A ⊢ ? ⊢ Z ≠ ∅ ∨ A ≠ ∅ ∪ ⊢ ∧..."}], "↕": [{"example_id": "story_infinite_division_115", "context": "📖 Luca ha ∞ libri e divide in ↕ modo ⊢ ? ⊢ ∀ parte..."}, {"example_id": "story_infinite_division_115", "context": "📖 Luca ha ∞ libri e divide in ↕ modo ⊢ ? ⊢ ∀ parte..."}, {"example_id": "story_infinite_division_287", "context": "📖 Sofia ha ∞ monete e divide in ↕ modo ⊢ ? ⊢ ∀ par..."}], "²": [{"example_id": "math_9b899995", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∲..."}, {"example_id": "math_644f02b6", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∰..."}, {"example_id": "math_ca6520ce", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∯..."}], "³": [{"example_id": "math_9b899995", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∲..."}, {"example_id": "math_644f02b6", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∰..."}, {"example_id": "math_ca6520ce", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∯..."}], "⊈": [{"example_id": "structure_⊈_13", "context": "🧠 C ⊈ Y ⊢ ? ⊢ C ≠ ∅ ∨ Y ≠ ∅ ⊈ ⊢ ∧..."}, {"example_id": "structure_⊈_13", "context": "🧠 C ⊈ Y ⊢ ? ⊢ C ≠ ∅ ∨ Y ≠ ∅ ⊈ ⊢ ∧..."}, {"example_id": "structure_⊈_130", "context": "🧠 X ⊈ Y ⊢ ? ⊢ X ≠ ∅ ∨ Y ≠ ∅ ⊈ ⊢ ∧..."}], "⊤": [{"example_id": "logical_5e0a976e", "context": "🧠 ∀x (S(x) ⇒ R(x)) ∧ ∃x S(x) ∧ ∀x (R(x) ⇒ P(x)) ⊢ ..."}, {"example_id": "logical_83391bc0", "context": "🧠 ∀x (P(x) ⇒ R(x)) ∧ ∃x P(x) ∧ ∀x (R(x) ⇒ S(x)) ⊢ ..."}, {"example_id": "logical_68ac766a", "context": "🧠 R ⇒ Q ∧ Q ⇒ P ∧ R ⊢ ? ⊢ Q ∧ ⊢ P ∀ ⊢ ∃ ∧ ⇒ ⊤..."}], "∰": [{"example_id": "math_644f02b6", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∰..."}, {"example_id": "math_3e76c920", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_6677f094", "context": "🔢 x + 10 = 8 ⊢ x = ? ⊢ x = -2 ⊢ ∫ ∧ ∰..."}], "⟩": [{"example_id": "code_5b129846", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b 🔄 ⚡ ⟧ ..."}, {"example_id": "code_3ce95c6f", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}, {"example_id": "code_51c18f83", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b 🔄 ⚡ ⟧ ..."}], "∯": [{"example_id": "math_ca6520ce", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∯..."}, {"example_id": "math_110c04b7", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_ab3e03f6", "context": "🔢 x + 1 = 9 ⊢ x = ? ⊢ x = 8 ⊢ ∫ ∧ ∯..."}], "∇": [{"example_id": "math_2b716b96", "context": "🔢 x + 6 = 3 ⊢ x = ? ⊢ x = -3 ⊢ ∫ ∧ ∇..."}, {"example_id": "math_c5a90a41", "context": "🔢 x + 4 = 6 ⊢ x = ? ⊢ x = 2 ⊢ ∫ ∧ ∇..."}, {"example_id": "math_0eeb9071", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "±": [{"example_id": "math_2bd5b634", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_e4983b1e", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_c429141e", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "↔": [{"example_id": "logical_3e648d08", "context": "🧠 Q ⇒ R ∧ R ⇒ P ∧ Q ⊢ ? ⊢ R ∧ ⊢ P ∀ ⊢ ∃ ∧ ⇒ ↔..."}, {"example_id": "logical_fd05b03c", "context": "🧠 P ∧ P ⇒ Q ⊢ ? ⊢ Q ∀ ⊢ ∃ ∧ ⇒ ↔..."}, {"example_id": "logical_1435fec9", "context": "🧠 R ∧ R ⇒ Q ⊢ ? ⊢ Q ∀ ⊢ ∃ ∧ ⇒ ↔..."}], "≡": [{"example_id": "logical_e6db9aeb", "context": "🧠 Q ∧ Q ⇒ R ⊢ ? ⊢ R ⊢ ⇒ ∃ ∀ ∧ ≡..."}, {"example_id": "logical_dcaeade4", "context": "🧠 P ∧ P ⇒ R ⊢ ? ⊢ R ∃ ∧ ⇒ ⊢ ∀ ≡..."}, {"example_id": "logical_6102be01", "context": "🧠 ∀x (P(x) ⇒ R(x)) ∧ ∃x P(x) ∧ ∀x (R(x) ⇒ Q(x)) ⊢ ..."}], "⟷": [{"example_id": "logical_0ac4bfe9", "context": "🧠 Q ∧ Q ⇒ R ⊢ ? ⊢ R ∀ ⊢ ∃ ∧ ⇒ ⟷..."}, {"example_id": "logical_226798ff", "context": "🧠 S ⇒ P ∧ P ⇒ T ∧ S ⊢ ? ⊢ P ∧ ⊢ T ∀ ⊢ ∃ ∧ ⇒ ⟷..."}, {"example_id": "logical_bd33978d", "context": "🧠 P ⇒ Q ∧ Q ⇒ R ∧ P ⊢ ? ⊢ Q ∧ ⊢ R ∀ ⊢ ∃ ∧ ⇒ ⟷..."}], "⊨": [{"example_id": "logical_5196ff8c", "context": "🧠 R ⇒ P ∧ P ⇒ Q ∧ R ⊢ ? ⊢ P ∧ ⊢ Q ∃ ∧ ⇒ ⊢ ∀ ⊨..."}, {"example_id": "logical_f0377428", "context": "🧠 Q ⇒ R ∧ R ⇒ P ∧ Q ⊢ ? ⊢ R ∧ ⊢ P ∃ ∧ ⇒ ⊢ ∀ ⊨..."}, {"example_id": "logical_7320fd28", "context": "🧠 Q ∧ Q ⇒ P ⊢ ? ⊢ P ∃ ∧ ⇒ ⊢ ∀ ⊨..."}], "∂": [{"example_id": "multistep_89036", "context": "🧠 ∀x (P(x) ⇒ Q(x)) ∧ ∃x P(x) ∧ ∀x (Q(x) ⇒ R(x)) ⊢ ..."}, {"example_id": "multistep_37201", "context": "🧠 ∀x (P(x) ⇒ Q(x)) ∧ ∃x P(x) ∧ ∀x (Q(x) ⇒ R(x)) ⊢ ..."}, {"example_id": "multistep_89947", "context": "🧠 ∀x (P(x) ⇒ Q(x)) ∧ ∃x P(x) ∧ ∀x (Q(x) ⇒ R(x)) ⊢ ..."}], "⟨": [{"example_id": "code_7e5e5e00", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}, {"example_id": "code_7e72b71f", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}, {"example_id": "code_891f47d0", "context": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b) 🏛️ ⟦Ca..."}], "∆": [{"example_id": "math_97fdf9e1", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_a3bc26ca", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_9ed6588a", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "⊇": [{"example_id": "structure_⊇_249", "context": "🧠 D ⊇ B ∧ B ≠ ∅ ⊢ ? ⊢ D ≠ ∅ ⊇ ⊢ ∧..."}, {"example_id": "structure_⊇_249", "context": "🧠 D ⊇ B ∧ B ≠ ∅ ⊢ ? ⊢ D ≠ ∅ ⊇ ⊢ ∧..."}, {"example_id": "structure_⊇_169", "context": "🧠 B ⊇ Z ∧ Z ≠ ∅ ⊢ ? ⊢ B ≠ ∅ ⊇ ⊢ ∧..."}], "∏": [{"example_id": "math_664a243a", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "logical_89678820", "context": "🧠 P ⇒ S ∧ S ⇒ R ∧ P ⊢ ? ⊢ S ∧ ⊢ R ⊢ ⇒ ∃ ∀ ∧ ∏..."}, {"example_id": "math_f075c09b", "context": "🔢 x + 10 = 6 ⊢ x = ? ⊢ x = -4 ⊢ ∫ ∧ ∏..."}], "⇄": [{"example_id": "code_5851cd82", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}, {"example_id": "code_06b4820d", "context": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum ⚡ ⟦lst⟧..."}, {"example_id": "code_9399ba14", "context": "⚡ def add(a, b): ⤴ a + b ⚡ ⟦a, b⟧ → ⤴ a + b 🔄 ⚡ ⟧ ..."}], "∮": [{"example_id": "math_a3174d97", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_fbf58509", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∮..."}, {"example_id": "math_c9de6e3d", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}], "∱": [{"example_id": "math_1cf2691d", "context": "🔢 x + 2 = 2 ⊢ x = ? ⊢ x = 0 ⊢ ∫ ∧ ∱..."}, {"example_id": "math_b4df68f7", "context": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ? ⊢ x = 3 ∧ y ..."}, {"example_id": "math_730e2023", "context": "🔢 ∫ x² dx ⊢ ? ⊢ x³/3 + C ⊢ ∫ ∧ ∱..."}]}}