{"metadata": {"total_concepts": 147, "version": "2.0", "description": "NEUROGLYPH Conceptual Registry"}, "concepts": [{"concept_id": 1001, "symbol": "∀", "unicode_codepoint": "U+2200", "concept_name": "universal_quantification", "semantic_type": "quantifier", "arity": 3, "meaning": "for all elements in domain", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1002, "symbol": "∃", "unicode_codepoint": "U+2203", "concept_name": "existential_quantification", "semantic_type": "quantifier", "arity": 3, "meaning": "there exists at least one", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1101, "symbol": "⇒", "unicode_codepoint": "U+21D2", "concept_name": "material_implication", "semantic_type": "logical_connective", "arity": 2, "meaning": "if premise then conclusion", "logical_strength": 0.8, "aliases": []}, {"concept_id": 1102, "symbol": "⇔", "unicode_codepoint": "U+21D4", "concept_name": "biconditional", "semantic_type": "logical_connective", "arity": 2, "meaning": "if and only if", "logical_strength": 0.9, "aliases": []}, {"concept_id": 1103, "symbol": "∧", "unicode_codepoint": "U+2227", "concept_name": "logical_conjunction", "semantic_type": "logical_connective", "arity": 2, "meaning": "logical and", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1104, "symbol": "∨", "unicode_codepoint": "U+2228", "concept_name": "logical_disjunction", "semantic_type": "logical_connective", "arity": 2, "meaning": "logical or", "logical_strength": 0.7, "aliases": []}, {"concept_id": 1105, "symbol": "¬", "unicode_codepoint": "U+00AC", "concept_name": "logical_negation", "semantic_type": "logical_connective", "arity": 1, "meaning": "logical not", "logical_strength": 1.0, "aliases": []}, {"concept_id": 9001, "symbol": "!", "unicode_codepoint": "U+0021", "concept_name": "uniqueness_operator", "semantic_type": "logical_connective", "arity": 1, "meaning": "uniqueness operator for existential quantification", "logical_strength": 1.0, "aliases": ["unique"]}, {"concept_id": 1106, "symbol": "≡", "unicode_codepoint": "U+2261", "concept_name": "logical_equivalence", "semantic_type": "logical_connective", "arity": 2, "meaning": "logically equivalent", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1201, "symbol": "⊢", "unicode_codepoint": "U+22A2", "concept_name": "syntactic_entailment", "semantic_type": "inference", "arity": 2, "meaning": "syntactically entails", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1202, "symbol": "⊨", "unicode_codepoint": "U+22A8", "concept_name": "semantic_entailment", "semantic_type": "inference", "arity": 2, "meaning": "semantically entails", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1203, "symbol": "⊭", "unicode_codepoint": "U+22AD", "concept_name": "does_not_entail", "semantic_type": "inference", "arity": 2, "meaning": "does not entail", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1204, "symbol": "⊤", "unicode_codepoint": "U+22A4", "concept_name": "logical_truth", "semantic_type": "inference", "arity": 0, "meaning": "logical truth", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1205, "symbol": "⊥", "unicode_codepoint": "U+22A5", "concept_name": "logical_falsehood", "semantic_type": "inference", "arity": 0, "meaning": "logical falsehood", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1301, "symbol": "∈", "unicode_codepoint": "U+2208", "concept_name": "set_membership", "semantic_type": "set_theory", "arity": 2, "meaning": "is element of", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1302, "symbol": "∉", "unicode_codepoint": "U+2209", "concept_name": "not_set_membership", "semantic_type": "set_theory", "arity": 2, "meaning": "is not element of", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1303, "symbol": "⊂", "unicode_codepoint": "U+2282", "concept_name": "proper_subset", "semantic_type": "set_theory", "arity": 2, "meaning": "is proper subset of", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1304, "symbol": "⊆", "unicode_codepoint": "U+2286", "concept_name": "subset_or_equal", "semantic_type": "set_theory", "arity": 2, "meaning": "is subset or equal to", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1305, "symbol": "⊇", "unicode_codepoint": "U+2287", "concept_name": "superset_or_equal", "semantic_type": "set_theory", "arity": 2, "meaning": "is superset or equal to", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1306, "symbol": "∩", "unicode_codepoint": "U+2229", "concept_name": "set_intersection", "semantic_type": "set_theory", "arity": 2, "meaning": "intersection of sets", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1307, "symbol": "∪", "unicode_codepoint": "U+222A", "concept_name": "set_union", "semantic_type": "set_theory", "arity": 2, "meaning": "union of sets", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1308, "symbol": "∅", "unicode_codepoint": "U+2205", "concept_name": "empty_set", "semantic_type": "set_theory", "arity": 0, "meaning": "empty set", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1401, "symbol": "∫", "unicode_codepoint": "U+222B", "concept_name": "definite_integral", "semantic_type": "mathematics", "arity": 3, "meaning": "definite integral", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1402, "symbol": "∑", "unicode_codepoint": "U+2211", "concept_name": "summation", "semantic_type": "mathematics", "arity": 3, "meaning": "summation over range", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1403, "symbol": "∏", "unicode_codepoint": "U+220F", "concept_name": "product", "semantic_type": "mathematics", "arity": 3, "meaning": "product over range", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1404, "symbol": "∂", "unicode_codepoint": "U+2202", "concept_name": "partial_derivative", "semantic_type": "mathematics", "arity": 2, "meaning": "partial derivative", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1405, "symbol": "∇", "unicode_codepoint": "U+2207", "concept_name": "gradient", "semantic_type": "mathematics", "arity": 1, "meaning": "gradient operator", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1406, "symbol": "√", "unicode_codepoint": "U+221A", "concept_name": "square_root", "semantic_type": "mathematics", "arity": 1, "meaning": "square root", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1407, "symbol": "∞", "unicode_codepoint": "U+221E", "concept_name": "infinity", "semantic_type": "mathematics", "arity": 0, "meaning": "infinity", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1501, "symbol": "🧠", "unicode_codepoint": "U+1F9E0", "concept_name": "reasoning_process", "semantic_type": "meta_concept", "arity": 1, "meaning": "reasoning or thinking process", "logical_strength": 0.9, "aliases": []}, {"concept_id": 1502, "symbol": "⚡", "unicode_codepoint": "U+26A1", "concept_name": "function_concept", "semantic_type": "meta_concept", "arity": 1, "meaning": "function or energy concept", "logical_strength": 0.8, "aliases": []}, {"concept_id": 1503, "symbol": "🔄", "unicode_codepoint": "U+1F504", "concept_name": "iterative_process", "semantic_type": "meta_concept", "arity": 1, "meaning": "iteration or cycle", "logical_strength": 0.8, "aliases": []}, {"concept_id": 1504, "symbol": "🏛", "unicode_codepoint": "U+1F3DB", "concept_name": "structural_concept", "semantic_type": "meta_concept", "arity": 1, "meaning": "structural or architectural concept", "logical_strength": 0.7, "aliases": []}, {"concept_id": 1505, "symbol": "📖", "unicode_codepoint": "U+1F4D6", "concept_name": "knowledge_concept", "semantic_type": "meta_concept", "arity": 1, "meaning": "knowledge or learning", "logical_strength": 0.8, "aliases": []}, {"concept_id": 1506, "symbol": "🔮", "unicode_codepoint": "U+1F52E", "concept_name": "prediction_concept", "semantic_type": "meta_concept", "arity": 1, "meaning": "prediction or foresight", "logical_strength": 0.7, "aliases": []}, {"concept_id": 2000, "symbol": "=", "unicode_codepoint": "U+003D", "concept_name": "equality", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical equality relation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2001, "symbol": "±", "unicode_codepoint": "U+00B1", "concept_name": "unicode_00b1", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '±' (U+00B1)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2002, "symbol": "≤", "unicode_codepoint": "U+2264", "concept_name": "less_equal", "semantic_type": "mathematics", "arity": 2, "meaning": "less than or equal relation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2003, "symbol": "∆", "unicode_codepoint": "U+2206", "concept_name": "unicode_2206", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∆' (U+2206)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2004, "symbol": "️", "unicode_codepoint": "U+FE0F", "concept_name": "unicode_fe0f", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '️' (U+FE0F)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2005, "symbol": "/", "unicode_codepoint": "U+002F", "concept_name": "division", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical division operation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2006, "symbol": "²", "unicode_codepoint": "U+00B2", "concept_name": "digit_²", "semantic_type": "literal", "arity": 1, "meaning": "numeric digit '²'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2007, "symbol": "?", "unicode_codepoint": "U+003F", "concept_name": "question", "semantic_type": "meta_concept", "arity": 2, "meaning": "question or unknown placeholder", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2008, "symbol": "🏛️", "unicode_codepoint": "multi-char", "concept_name": "multichar_7949", "semantic_type": "meta_concept", "arity": 2, "meaning": "multi-character symbol '🏛️'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2009, "symbol": "🔢", "unicode_codepoint": "U+1F522", "concept_name": "math_context", "semantic_type": "meta_concept", "arity": 2, "meaning": "mathematical context indicator", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2010, "symbol": "∴", "unicode_codepoint": "U+2234", "concept_name": "therefore", "semantic_type": "logical_connective", "arity": 2, "meaning": "logical therefore conclusion", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2011, "symbol": "∘", "unicode_codepoint": "U+2218", "concept_name": "composition", "semantic_type": "mathematics", "arity": 2, "meaning": "function composition operator", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2012, "symbol": "⟦", "unicode_codepoint": "U+27E6", "concept_name": "bracket_open", "semantic_type": "meta_concept", "arity": 2, "meaning": "semantic bracket opening", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2013, "symbol": "|", "unicode_codepoint": "U+007C", "concept_name": "pipe", "semantic_type": "logical_connective", "arity": 2, "meaning": "logical pipe or separator", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2014, "symbol": "⟧", "unicode_codepoint": "U+27E7", "concept_name": "bracket_close", "semantic_type": "meta_concept", "arity": 2, "meaning": "semantic bracket closing", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2015, "symbol": "+", "unicode_codepoint": "U+002B", "concept_name": "addition", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical addition operation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2016, "symbol": "≈", "unicode_codepoint": "U+2248", "concept_name": "unicode_2248", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '≈' (U+2248)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2017, "symbol": "↔", "unicode_codepoint": "U+2194", "concept_name": "unicode_2194", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '↔' (U+2194)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2018, "symbol": "∮", "unicode_codepoint": "U+222E", "concept_name": "unicode_222e", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∮' (U+222E)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2019, "symbol": "∳", "unicode_codepoint": "U+2233", "concept_name": "counterclockwise_integral", "semantic_type": "mathematics", "arity": 2, "meaning": "counterclockwise contour integral", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2020, "symbol": "ℝ", "unicode_codepoint": "U+211D", "concept_name": "variable_ℝ", "semantic_type": "variable", "arity": 1, "meaning": "variable or identifier 'ℝ'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2021, "symbol": "⇄", "unicode_codepoint": "U+21C4", "concept_name": "unicode_21c4", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '⇄' (U+21C4)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2022, "symbol": "∱", "unicode_codepoint": "U+2231", "concept_name": "unicode_2231", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∱' (U+2231)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2023, "symbol": "⟩", "unicode_codepoint": "U+27E9", "concept_name": "unicode_27e9", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '⟩' (U+27E9)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2024, "symbol": "-", "unicode_codepoint": "U+002D", "concept_name": "subtraction", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical subtraction operation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2025, "symbol": "è", "unicode_codepoint": "U+00E8", "concept_name": "variable_è", "semantic_type": "variable", "arity": 1, "meaning": "variable or identifier 'è'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2026, "symbol": "≥", "unicode_codepoint": "U+2265", "concept_name": "greater_equal", "semantic_type": "mathematics", "arity": 2, "meaning": "greater than or equal relation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2027, "symbol": "↕", "unicode_codepoint": "U+2195", "concept_name": "unicode_2195", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '↕' (U+2195)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2028, "symbol": "⟨", "unicode_codepoint": "U+27E8", "concept_name": "unicode_27e8", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '⟨' (U+27E8)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2029, "symbol": "ò", "unicode_codepoint": "U+00F2", "concept_name": "variable_ò", "semantic_type": "variable", "arity": 1, "meaning": "variable or identifier 'ò'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2030, "symbol": "⇆", "unicode_codepoint": "U+21C6", "concept_name": "bidirectional", "semantic_type": "meta_concept", "arity": 2, "meaning": "bidirectional arrow", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2031, "symbol": "³", "unicode_codepoint": "U+00B3", "concept_name": "digit_³", "semantic_type": "literal", "arity": 1, "meaning": "numeric digit '³'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2032, "symbol": "≠", "unicode_codepoint": "U+2260", "concept_name": "inequality", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical inequality relation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2033, "symbol": "⊈", "unicode_codepoint": "U+2288", "concept_name": "not_subset", "semantic_type": "set_theory", "arity": 2, "meaning": "not subset relation", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2034, "symbol": "∯", "unicode_codepoint": "U+222F", "concept_name": "unicode_222f", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∯' (U+222F)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2035, "symbol": "⟷", "unicode_codepoint": "U+27F7", "concept_name": "unicode_27f7", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '⟷' (U+27F7)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2036, "symbol": "⤴", "unicode_codepoint": "U+2934", "concept_name": "return", "semantic_type": "meta_concept", "arity": 2, "meaning": "function return or output", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2037, "symbol": "λ", "unicode_codepoint": "U+03BB", "concept_name": "lambda", "semantic_type": "mathematics", "arity": 2, "meaning": "lambda function", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2038, "symbol": "∲", "unicode_codepoint": "U+2232", "concept_name": "clockwise_integral", "semantic_type": "mathematics", "arity": 2, "meaning": "clockwise contour integral", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2039, "symbol": "→", "unicode_codepoint": "U+2192", "concept_name": "arrow", "semantic_type": "meta_concept", "arity": 2, "meaning": "functional arrow or mapping", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2040, "symbol": "∰", "unicode_codepoint": "U+2230", "concept_name": "unicode_2230", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∰' (U+2230)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 3000, "symbol": "∄", "unicode_codepoint": "U+2204", "concept_name": "not_exists", "semantic_type": "quantifier", "arity": 3, "meaning": "there does not exist", "logical_strength": 1.0, "aliases": []}, {"concept_id": 3001, "symbol": "⊕", "unicode_codepoint": "U+2295", "concept_name": "exclusive_or", "semantic_type": "logical_connective", "arity": 2, "meaning": "exclusive or (XOR)", "logical_strength": 0.9, "aliases": []}, {"concept_id": 3002, "symbol": "⊬", "unicode_codepoint": "U+22AC", "concept_name": "does_not_prove", "semantic_type": "inference", "arity": 2, "meaning": "does not prove", "logical_strength": 1.0, "aliases": []}, {"concept_id": 3003, "symbol": "⊦", "unicode_codepoint": "U+22A6", "concept_name": "assertion", "semantic_type": "inference", "arity": 2, "meaning": "assertion or theorem", "logical_strength": 1.0, "aliases": []}, {"concept_id": 3004, "symbol": "⊧", "unicode_codepoint": "U+22A7", "concept_name": "models", "semantic_type": "inference", "arity": 2, "meaning": "models or satisfies", "logical_strength": 1.0, "aliases": []}, {"concept_id": 3005, "symbol": "≢", "unicode_codepoint": "U+2262", "concept_name": "not_equivalent", "semantic_type": "logical_connective", "arity": 2, "meaning": "not logically equivalent", "logical_strength": 0.9, "aliases": []}, {"concept_id": 3006, "symbol": "⊬", "unicode_codepoint": "U+22AC", "concept_name": "not_syntactic_consequence", "semantic_type": "logical_connective", "arity": 2, "meaning": "not syntactic consequence", "logical_strength": 0.9, "aliases": []}, {"concept_id": 3007, "symbol": "⊸", "unicode_codepoint": "U+22B8", "concept_name": "linear_implication", "semantic_type": "logical_connective", "arity": 2, "meaning": "linear logic implication", "logical_strength": 0.9, "aliases": []}, {"concept_id": 3008, "symbol": "⊗", "unicode_codepoint": "U+2297", "concept_name": "tensor_product", "semantic_type": "logical_connective", "arity": 2, "meaning": "tensor product", "logical_strength": 0.9, "aliases": []}, {"concept_id": 4000, "symbol": "∋", "unicode_codepoint": "U+220B", "concept_name": "contains_member", "semantic_type": "set_theory", "arity": 2, "meaning": "contains as member", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4001, "symbol": "∌", "unicode_codepoint": "U+220C", "concept_name": "does_not_contain", "semantic_type": "set_theory", "arity": 2, "meaning": "does not contain as member", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4002, "symbol": "⊃", "unicode_codepoint": "U+2283", "concept_name": "proper_superset", "semantic_type": "set_theory", "arity": 2, "meaning": "is proper superset of", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4003, "symbol": "⊄", "unicode_codepoint": "U+2284", "concept_name": "not_subset", "semantic_type": "set_theory", "arity": 2, "meaning": "is not subset of", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4004, "symbol": "⊅", "unicode_codepoint": "U+2285", "concept_name": "not_superset", "semantic_type": "set_theory", "arity": 2, "meaning": "is not superset of", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4005, "symbol": "⊉", "unicode_codepoint": "U+2289", "concept_name": "not_superset_equal", "semantic_type": "set_theory", "arity": 2, "meaning": "is not superset or equal to", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4006, "symbol": "∖", "unicode_codepoint": "U+2216", "concept_name": "set_difference", "semantic_type": "set_theory", "arity": 2, "meaning": "set difference", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4007, "symbol": "⊙", "unicode_codepoint": "U+2299", "concept_name": "set_composition", "semantic_type": "set_theory", "arity": 2, "meaning": "set composition", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4008, "symbol": "𝒫", "unicode_codepoint": "U+1D4AB", "concept_name": "power_set", "semantic_type": "set_theory", "arity": 2, "meaning": "power set", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4009, "symbol": "℘", "unicode_codepoint": "U+2118", "concept_name": "power_set_alt", "semantic_type": "set_theory", "arity": 2, "meaning": "power set alternative", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4010, "symbol": "𝔘", "unicode_codepoint": "U+1D518", "concept_name": "universal_set", "semantic_type": "set_theory", "arity": 2, "meaning": "universal set", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4011, "symbol": "ℵ", "unicode_codepoint": "U+2135", "concept_name": "aleph", "semantic_type": "mathematics", "arity": 1, "meaning": "aleph number (infinite cardinality)", "logical_strength": 0.9, "aliases": []}, {"concept_id": 4012, "symbol": "ℶ", "unicode_codepoint": "U+2136", "concept_name": "beth", "semantic_type": "mathematics", "arity": 1, "meaning": "beth number", "logical_strength": 0.9, "aliases": []}, {"concept_id": 4013, "symbol": "ℷ", "unicode_codepoint": "U+2137", "concept_name": "gimel", "semantic_type": "mathematics", "arity": 1, "meaning": "gimel number", "logical_strength": 0.9, "aliases": []}, {"concept_id": 4014, "symbol": "∼", "unicode_codepoint": "U+223C", "concept_name": "equivalence", "semantic_type": "set_theory", "arity": 2, "meaning": "is equivalent to", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4015, "symbol": "≅", "unicode_codepoint": "U+2245", "concept_name": "isomorphic", "semantic_type": "set_theory", "arity": 2, "meaning": "is isomorphic to", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4016, "symbol": "≃", "unicode_codepoint": "U+2243", "concept_name": "asymptotically_equal", "semantic_type": "set_theory", "arity": 2, "meaning": "asymptotically equal", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4017, "symbol": "≺", "unicode_codepoint": "U+227A", "concept_name": "precedes", "semantic_type": "set_theory", "arity": 2, "meaning": "precedes", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4018, "symbol": "≻", "unicode_codepoint": "U+227B", "concept_name": "succeeds", "semantic_type": "set_theory", "arity": 2, "meaning": "succeeds", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4019, "symbol": "≼", "unicode_codepoint": "U+227C", "concept_name": "precedes_equivalent", "semantic_type": "set_theory", "arity": 2, "meaning": "precedes or equivalent", "logical_strength": 1.0, "aliases": []}, {"concept_id": 4020, "symbol": "≽", "unicode_codepoint": "U+227D", "concept_name": "succeeds_equivalent", "semantic_type": "set_theory", "arity": 2, "meaning": "succeeds or equivalent", "logical_strength": 1.0, "aliases": []}, {"concept_id": 5000, "symbol": "⅋", "unicode_codepoint": "U+214B", "concept_name": "par", "semantic_type": "logical_connective", "arity": 2, "meaning": "multiplicative disjunction", "logical_strength": 0.9, "aliases": []}, {"concept_id": 5001, "symbol": "⇸", "unicode_codepoint": "U+21F8", "concept_name": "partial_function", "semantic_type": "logical_connective", "arity": 2, "meaning": "partial function arrow", "logical_strength": 0.9, "aliases": []}, {"concept_id": 5002, "symbol": "⇀", "unicode_codepoint": "U+21C0", "concept_name": "<PERSON><PERSON><PERSON><PERSON>", "semantic_type": "logical_connective", "arity": 2, "meaning": "partial function", "logical_strength": 0.9, "aliases": []}, {"concept_id": 5003, "symbol": "⊖", "unicode_codepoint": "U+2296", "concept_name": "fuzzy_difference", "semantic_type": "logical_connective", "arity": 2, "meaning": "fuzzy difference", "logical_strength": 0.9, "aliases": []}, {"concept_id": 5004, "symbol": "⊘", "unicode_codepoint": "U+2298", "concept_name": "fuzzy_division", "semantic_type": "logical_connective", "arity": 2, "meaning": "fuzzy division", "logical_strength": 0.9, "aliases": []}, {"concept_id": 5005, "symbol": "∙", "unicode_codepoint": "U+2219", "concept_name": "paraconsistent_conjunction", "semantic_type": "logical_connective", "arity": 2, "meaning": "paraconsistent conjunction", "logical_strength": 0.9, "aliases": []}, {"concept_id": 5006, "symbol": "∗", "unicode_codepoint": "U+2217", "concept_name": "paraconsistent_disjunction", "semantic_type": "logical_connective", "arity": 2, "meaning": "paraconsistent disjunction", "logical_strength": 0.9, "aliases": []}, {"concept_id": 5007, "symbol": "⅋", "unicode_codepoint": "U+214B", "concept_name": "multiplicative_or", "semantic_type": "logical_connective", "arity": 2, "meaning": "multiplicative or", "logical_strength": 0.9, "aliases": []}, {"concept_id": 6000, "symbol": "⊎", "unicode_codepoint": "U+228E", "concept_name": "disjoint_union", "semantic_type": "set_theory", "arity": 2, "meaning": "disjoint union of sets", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6001, "symbol": "⊔", "unicode_codepoint": "U+2294", "concept_name": "join", "semantic_type": "set_theory", "arity": 2, "meaning": "join operation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6002, "symbol": "⊓", "unicode_codepoint": "U+2293", "concept_name": "meet", "semantic_type": "set_theory", "arity": 2, "meaning": "meet operation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6003, "symbol": "⊏", "unicode_codepoint": "U+228F", "concept_name": "square_subset", "semantic_type": "set_theory", "arity": 2, "meaning": "square subset", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6004, "symbol": "⊐", "unicode_codepoint": "U+2290", "concept_name": "square_superset", "semantic_type": "set_theory", "arity": 2, "meaning": "square superset", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6005, "symbol": "⊑", "unicode_codepoint": "U+2291", "concept_name": "square_subset_equal", "semantic_type": "set_theory", "arity": 2, "meaning": "square subset or equal", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6006, "symbol": "⊒", "unicode_codepoint": "U+2292", "concept_name": "square_superset_equal", "semantic_type": "set_theory", "arity": 2, "meaning": "square superset or equal", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6007, "symbol": "⊖", "unicode_codepoint": "U+2296", "concept_name": "boolean_difference", "semantic_type": "set_theory", "arity": 2, "meaning": "boolean difference", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6008, "symbol": "⊎", "unicode_codepoint": "U+228E", "concept_name": "multiset_union", "semantic_type": "set_theory", "arity": 2, "meaning": "multiset union", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6009, "symbol": "⊓", "unicode_codepoint": "U+2293", "concept_name": "multiset_intersection", "semantic_type": "set_theory", "arity": 2, "meaning": "multiset intersection", "logical_strength": 1.0, "aliases": []}, {"concept_id": 6010, "symbol": "⊖", "unicode_codepoint": "U+2296", "concept_name": "multiset_difference", "semantic_type": "set_theory", "arity": 2, "meaning": "multiset difference", "logical_strength": 1.0, "aliases": []}, {"concept_id": 7000, "symbol": "≉", "unicode_codepoint": "U+2249", "concept_name": "not_approximately_equal", "semantic_type": "mathematics", "arity": 2, "meaning": "not approximately equal", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7001, "symbol": "≇", "unicode_codepoint": "U+2247", "concept_name": "not_isomorphic", "semantic_type": "mathematics", "arity": 2, "meaning": "not isomorphic to", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7002, "symbol": "∣", "unicode_codepoint": "U+2223", "concept_name": "divides", "semantic_type": "mathematics", "arity": 2, "meaning": "divides", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7003, "symbol": "∤", "unicode_codepoint": "U+2224", "concept_name": "does_not_divide", "semantic_type": "mathematics", "arity": 2, "meaning": "does not divide", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7004, "symbol": "∥", "unicode_codepoint": "U+2225", "concept_name": "parallel", "semantic_type": "mathematics", "arity": 2, "meaning": "parallel to", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7005, "symbol": "∦", "unicode_codepoint": "U+2226", "concept_name": "not_parallel", "semantic_type": "mathematics", "arity": 2, "meaning": "not parallel to", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7006, "symbol": "≍", "unicode_codepoint": "U+224D", "concept_name": "equivalent_asymptotic", "semantic_type": "mathematics", "arity": 2, "meaning": "equivalent asymptotic", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7007, "symbol": "≎", "unicode_codepoint": "U+224E", "concept_name": "geometrically_equivalent", "semantic_type": "mathematics", "arity": 2, "meaning": "geometrically equivalent", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7008, "symbol": "≏", "unicode_codepoint": "U+224F", "concept_name": "difference_between", "semantic_type": "mathematics", "arity": 2, "meaning": "difference between", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7009, "symbol": "∝", "unicode_codepoint": "U+221D", "concept_name": "proportional", "semantic_type": "mathematics", "arity": 2, "meaning": "proportional to", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7010, "symbol": "∷", "unicode_codepoint": "U+2237", "concept_name": "proportion", "semantic_type": "mathematics", "arity": 2, "meaning": "proportion", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7011, "symbol": "∶", "unicode_codepoint": "U+2236", "concept_name": "ratio", "semantic_type": "mathematics", "arity": 2, "meaning": "ratio", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7012, "symbol": "∵", "unicode_codepoint": "U+2235", "concept_name": "because", "semantic_type": "mathematics", "arity": 2, "meaning": "because", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7013, "symbol": "≁", "unicode_codepoint": "U+2241", "concept_name": "not_similar", "semantic_type": "mathematics", "arity": 2, "meaning": "not similar to", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7014, "symbol": "∽", "unicode_codepoint": "U+223D", "concept_name": "reversed_similar", "semantic_type": "mathematics", "arity": 2, "meaning": "reversed similar to", "logical_strength": 0.9, "aliases": []}, {"concept_id": 7015, "symbol": "≀", "unicode_codepoint": "U+2240", "concept_name": "wreath_product", "semantic_type": "mathematics", "arity": 2, "meaning": "wreath product", "logical_strength": 0.9, "aliases": []}, {"concept_id": 8000, "symbol": "←", "unicode_codepoint": "U+2190", "concept_name": "elimination", "semantic_type": "inference", "arity": 2, "meaning": "elimination rule", "logical_strength": 1.0, "aliases": []}, {"concept_id": 8001, "symbol": "↑", "unicode_codepoint": "U+2191", "concept_name": "discharge", "semantic_type": "inference", "arity": 2, "meaning": "discharge rule", "logical_strength": 1.0, "aliases": []}, {"concept_id": 8002, "symbol": "↓", "unicode_codepoint": "U+2193", "concept_name": "assumption", "semantic_type": "inference", "arity": 2, "meaning": "assumption rule", "logical_strength": 1.0, "aliases": []}, {"concept_id": 8003, "symbol": "⊣", "unicode_codepoint": "U+22A3", "concept_name": "reverse_sequent", "semantic_type": "inference", "arity": 2, "meaning": "reverse sequent", "logical_strength": 1.0, "aliases": []}, {"concept_id": 8004, "symbol": "∶", "unicode_codepoint": "U+2236", "concept_name": "type_judgment", "semantic_type": "inference", "arity": 2, "meaning": "type judgment", "logical_strength": 1.0, "aliases": []}, {"concept_id": 8005, "symbol": "⊞", "unicode_codepoint": "U+229E", "concept_name": "box_plus", "semantic_type": "inference", "arity": 2, "meaning": "box plus", "logical_strength": 1.0, "aliases": []}, {"concept_id": 8006, "symbol": "⊟", "unicode_codepoint": "U+229F", "concept_name": "box_minus", "semantic_type": "inference", "arity": 2, "meaning": "box minus", "logical_strength": 1.0, "aliases": []}]}