{"test_type": "quick_test", "timestamp": "2025-06-07 17:26:38", "total_examples": 24, "elapsed_time_seconds": 0.006644010543823242, "metrics": {"parseability_rate": 1.0, "ast_equivalence_rate": 0.0, "roundtrip_success_rate": 0.0, "avg_fidelity_score": 0.0, "avg_roundtrip_time_ms": 0.26647574986782274}, "quality_assessment": {"overall_pass": false, "passed_criteria": [{"criterion": "max_avg_time_ms", "threshold": 100.0, "actual": 0.26647574986782274, "status": "PASS"}], "failed_criteria": [{"criterion": "parseability_rate", "threshold": 0.95, "actual": 0.5416666666666666, "status": "FAIL"}, {"criterion": "ast_equivalence_rate", "threshold": 0.95, "actual": 0.0, "status": "FAIL"}, {"criterion": "roundtrip_success_rate", "threshold": 0.9, "actual": 0.0, "status": "FAIL"}, {"criterion": "avg_fidelity_score", "threshold": 0.95, "actual": 0.0, "status": "FAIL"}], "critical_failures": ["parseability_rate", "ast_equivalence_rate"]}, "detailed_metrics": "RoundTripMetrics(total_tests=24, successful_roundtrips=0, ast_equivalent_count=0, avg_fidelity_score=0.0, avg_roundtrip_time_ms=0.26647574986782274, success_rate=0.0, ast_equivalence_rate=0.0, failed_examples=['P ⇒ Q', 'P ∧ Q ∨ R', '¬P', 'P ⇔ Q', '∀x: P(x)', '∃y: Q(y)', '∀x ∈ ℝ: x² ≥ 0', '∫₀¹ x² dx', '∑ᵢ₌₁ⁿ i²', '∂f/∂x', '√x', 'x ∈ A', 'A ⊂ B', 'A ∩ B', '{1, 2, 3}', '∅', '🧠 P ⇒ Q', '⚡ factorial(n)', '🔄 while x > 0', 'P ∧ (P ⇒ Q) ⊢ Q', '{P, P ⇒ Q} ⊨ Q', 'f(x)', 'max(a, b, c)', 'sin(π/2)'], error_patterns={'str': 24})"}