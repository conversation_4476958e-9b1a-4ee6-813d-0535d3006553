{"timestamp": "2025-06-07 21:13:30 UTC", "dataset_path": "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl", "total_examples": 500, "parsing_failures": 0, "roundtrip_failures": 1000, "parsing_success_rate": 1.0, "roundtrip_success_rate": -1.0, "pattern_distribution": {"complex_math": 33, "universal_quantifier": 34, "existential_quantifier": 44, "nested_quantifiers": 25}, "complexity_distribution": {"high": 111, "very_high": 25}, "uncategorized_count": 914, "uncategorized_failures": [{"index": 0, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ x³/3 + C"}, {"index": 1, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ?"}, {"index": 1, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ x = 3 ∧ y = 1"}, {"index": 2, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ?"}, {"index": 2, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ x = 3 ∧ y = 1"}, {"index": 3, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ f ∈ Inj(A,B)"}, {"index": 4, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ?"}, {"index": 4, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ x = 3 ∧ y = 1"}, {"index": 5, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 S ⇒ Q ∧ Q ⇒ P ∧ S ⊢ ?"}, {"index": 5, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ Q ∧ ⊢ P"}, {"index": 6, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 S ∧ S ⇒ R ⊢ ?"}, {"index": 6, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ R"}, {"index": 7, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum"}, {"index": 7, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⚡ ⟦lst⟧ → 🔄 ⟦sum = 0⟧ → ⟦sum += item⟧ → ⤴ sum"}, {"index": 8, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🔮 Data la conclusione: ⊢ f(x) = y, quale formula può derivare questa inferenza?"}, {"index": 8, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ Possibile premessa: f: X → Y ∧ x ∈ X ∧ f injective ∧ f(x) defined"}, {"index": 9, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ f ∈ Inj(A,B)"}, {"index": 10, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 ∀x (R(x) ⇒ Q(x)) ∧ ∃x R(x) ∧ ∀x (Q(x) ⇒ P(x)) ⊢ ?"}, {"index": 10, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ ∃x Q(x) ∧ ⊢ ∃x P(x)"}, {"index": 11, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🔢 x + 9 = 9 ⊢ x = ?"}, {"index": 11, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ x = 0"}, {"index": 12, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 P ∧ Q ∧ (P ⇒ R) ⊢ ?"}, {"index": 12, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ Q ∧ R"}, {"index": 13, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum"}, {"index": 13, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⚡ ⟦lst⟧ → 🔄 ⟦sum = 0⟧ → ⟦sum += item⟧ → ⤴ sum"}, {"index": 14, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🔢 x + 5 = 6 ⊢ x = ?"}, {"index": 14, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ x = 1"}, {"index": 15, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "⚡ def add(a, b): ⤴ a + b"}, {"index": 15, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⚡ ⟦a, b⟧ → ⤴ a + b"}, {"index": 16, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ x³/3 + C"}, {"index": 17, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 x ∉ A ∧ A ≠ ∅ ⊢ ?"}, {"index": 18, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 Q ⇒ S ∧ S ⇒ R ∧ Q ⊢ ?"}, {"index": 18, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ S ∧ ⊢ R"}, {"index": 19, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🔮 Data la conclusione: ⊢ P ∧ Q, quale formula può derivare questa inferenza?"}, {"index": 19, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ Possibile premessa: (P ∨ R) ∧ (Q ∨ S) ∧ ¬R ∧ ¬S"}, {"index": 20, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum"}, {"index": 20, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⚡ ⟦lst⟧ → 🔄 ⟦sum = 0⟧ → ⟦sum += item⟧ → ⤴ sum"}, {"index": 21, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 ∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅ ⊢ ?"}, {"index": 21, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ A ⊂ C ∧ A ≠ ∅"}, {"index": 22, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 ∀x (P(x) ⇒ Q(x)) ∧ ∃x P(x) ∧ ∀x (Q(x) ⇒ R(x)) ⊢ ?"}, {"index": 22, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ ∃x Q(x) ∧ ∃x R(x)"}, {"index": 23, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 P ⇒ Q ∧ Q ⇒ R ∧ P ⊢ ?"}, {"index": 23, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ Q ∧ ⊢ R"}, {"index": 24, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 B ∪ A ⊢ ?"}, {"index": 24, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ B ≠ ∅ ∨ A ≠ ∅"}, {"index": 25, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 D ⊇ C ∧ C ≠ ∅ ⊢ ?"}, {"index": 25, "text_type": "response", "failure_type": "roundtrip_failure", "text": "⊢ D ≠ ∅"}, {"index": 26, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b)"}, {"index": 26, "text_type": "response", "failure_type": "roundtrip_failure", "text": "🏛️ ⟦Calculator⟧ → ⚡ add ⟦a,b⟧ → ⤴ a+b ∧ ⚡ multiply ⟦a,b⟧ → ⤴ a*b"}, {"index": 27, "text_type": "prompt", "failure_type": "roundtrip_failure", "text": "🧠 P ∧ Q ∧ (P ⇒ R) ⊢ ?"}], "top_failure_patterns": {"existential_quantifier": 44, "universal_quantifier": 34, "complex_math": 33, "nested_quantifiers": 25}, "pattern_details": {"list_comprehension": {"regex": "\\[.*for.*in.*\\]", "description": "List comprehensions [expr for x in iterable]", "examples": ["[x*2 for x in range(3)]", "[x for x in lst if x > 0]"], "complexity": "medium", "ast_nodes": ["ListComp", "comprehension"]}, "dict_comprehension": {"regex": "\\{.*:.*for.*in.*\\}", "description": "Dict comprehensions {k:v for x in iterable}", "examples": ["{x: x*2 for x in range(3)}"], "complexity": "medium", "ast_nodes": ["DictComp"]}, "set_comprehension": {"regex": "\\{.*for.*in.*\\}", "description": "Set comprehensions {expr for x in iterable}", "examples": ["{x*2 for x in range(3)}"], "complexity": "medium", "ast_nodes": ["SetComp"]}, "nested_comprehension": {"regex": "\\[.*\\[.*for.*\\].*for.*\\]", "description": "Nested comprehensions", "examples": ["[[y for y in x] for x in matrix]"], "complexity": "high", "ast_nodes": ["ListComp", "nested"]}, "simple_decorator": {"regex": "@\\w+\\s*\\ndef", "description": "Simple decorators @decorator", "examples": ["@property\ndef func():"], "complexity": "low", "ast_nodes": ["FunctionDef", "decorator_list"]}, "parametric_decorator": {"regex": "@\\w+\\([^)]*\\)\\s*\\ndef", "description": "Parametric decorators @decorator(args)", "examples": ["@retry(times=3)\ndef func():"], "complexity": "medium", "ast_nodes": ["FunctionDef", "Call"]}, "multiple_decorators": {"regex": "@\\w+.*\\n@\\w+.*\\ndef", "description": "Multiple decorators", "examples": ["@staticmethod\n@property\ndef func():"], "complexity": "medium", "ast_nodes": ["FunctionDef", "multiple_decorators"]}, "function_annotation": {"regex": "def\\s+\\w+\\([^)]*:\\s*\\w+[^)]*\\)\\s*->\\s*\\w+:", "description": "Function type annotations", "examples": ["def func(x: int) -> str:"], "complexity": "medium", "ast_nodes": ["FunctionDef", "arg", "returns"]}, "variable_annotation": {"regex": "\\w+\\s*:\\s*\\w+\\s*=", "description": "Variable type annotations", "examples": ["x: int = 5"], "complexity": "low", "ast_nodes": ["<PERSON><PERSON><PERSON>"]}, "complex_annotation": {"regex": ":\\s*(List|Dict|Tuple|Optional|Union)\\[", "description": "Complex type annotations", "examples": ["x: List[Dict[str, int]]"], "complexity": "high", "ast_nodes": ["Subscript", "Name"]}, "default_parameters": {"regex": "def\\s+\\w+\\([^)]*=.*\\):", "description": "Default parameter values", "examples": ["def func(a=1, b=True):"], "complexity": "low", "ast_nodes": ["FunctionDef", "arg", "defaults"]}, "complex_defaults": {"regex": "def\\s+\\w+\\([^)]*=\\s*[\\[\\{].*[\\]\\}].*\\):", "description": "Complex default values (lists, dicts)", "examples": ["def func(lst=[1,2,3]):"], "complexity": "medium", "ast_nodes": ["FunctionDef", "List", "Dict"]}, "set_literal": {"regex": "\\{[^:}]*\\}", "description": "Set literals {1, 2, 3}", "examples": ["{1, 2, 3}", "{x, y, z}"], "complexity": "low", "ast_nodes": ["Set"]}, "f_string": {"regex": "f['\\\"].*\\{.*\\}.*['\\\"]", "description": "F-string literals", "examples": ["f'Hello {name}'", "f'{x:.2f}'"], "complexity": "medium", "ast_nodes": ["JoinedStr", "FormattedValue"]}, "raw_string": {"regex": "r['\\\"].*['\\\"]", "description": "Raw string literals", "examples": ["r'\\d+\\w*'"], "complexity": "low", "ast_nodes": ["Constant"]}, "universal_quantifier": {"regex": "∀\\w+.*:", "description": "Universal quantifiers ∀x:", "examples": ["∀x ∈ ℝ: P(x)", "∀x: P(x) ⇒ Q(x)"], "complexity": "high", "ast_nodes": ["UniversalQuantification"]}, "existential_quantifier": {"regex": "∃\\w+.*:", "description": "Existential quantifiers ∃x:", "examples": ["∃x ∈ ℕ: P(x)", "∃!x: P(x)"], "complexity": "high", "ast_nodes": ["ExistentialQuantification"]}, "nested_quantifiers": {"regex": "[∀∃]\\w+.*[∀∃]\\w+.*:", "description": "Nested quantifiers", "examples": ["∀x ∃y: R(x,y)", "∃x ∀y: P(x) ⇒ Q(y)"], "complexity": "very_high", "ast_nodes": ["nested_quantification"]}, "complex_math": {"regex": "[∫∑∏∂∇].*", "description": "Complex mathematical expressions", "examples": ["∫₀¹ f(x)dx", "∑ᵢ₌₁ⁿ xᵢ"], "complexity": "high", "ast_nodes": ["MathematicalExpression"]}, "subscript_superscript": {"regex": "[a-zA-Z][₀-₉⁰-⁹]+", "description": "Subscripts and superscripts", "examples": ["x₁", "a²", "H₂O"], "complexity": "medium", "ast_nodes": ["Subscript", "Superscript"]}, "lambda_expression": {"regex": "lambda\\s+.*:", "description": "Lambda expressions", "examples": ["lambda x: x*2", "lambda x, y: x+y"], "complexity": "medium", "ast_nodes": ["Lambda"]}, "generator_expression": {"regex": "\\(.*for.*in.*\\)", "description": "Generator expressions", "examples": ["(x*2 for x in range(10))"], "complexity": "medium", "ast_nodes": ["GeneratorExp"]}}, "elapsed_time_seconds": 0.5719351768493652}