{"timestamp": "2025-06-07 18:15:20 UTC", "neuroglyph_version": "2.0-conceptual", "environment_hash": "bee9ce571a14a9fb", "dataset_hash": "not_available", "validation_levels": [{"level": 1, "name": "Reproducibility", "status": "PASS", "score": 1.0, "details": {"runs_executed": 3, "results_identical": true, "success_rates": [1.0, 1.0, 1.0], "variance": 0.0}, "execution_time_seconds": 0.2554771900177002, "error": null}, {"level": 2, "name": "Environment Integrity", "status": "PASS", "score": 1.0, "details": {"environment_hash": "bee9ce571a14a9fb9afb5164df6acdcaed19572cb8a7b78dd5642672f859643d", "python_version": "3.12.4", "critical_libraries": {"torch": "2.5.0", "transformers": "4.45.2", "datasets": "not_installed", "numpy": "2.2.5"}, "total_packages": 454, "requirements_locked": "data/certification/requirements.lock.txt"}, "execution_time_seconds": 3.8052241802215576, "error": null}, {"level": 3, "name": "External Benchmarks", "status": "SKIP", "score": 0.0, "details": {"reason": "Skipped for speed"}, "execution_time_seconds": 0.0, "error": null}, {"level": 4, "name": "Manual Review", "status": "PASS", "score": 1.0, "details": {"sample_size": 8, "approved_count": 8, "approval_rate": 1.0, "review_cases": [{"original": "∀x: P(x)", "ast_type": "UniversalQuantification", "semantic_meaning": "For all x: variable 'P'", "logical_strength": 1.0, "roundtrip": "∀x: P", "roundtrip_match": false, "manual_approval": true}, {"original": "P ⇒ Q", "ast_type": "MaterialImplication", "semantic_meaning": "If variable 'P' then variable 'Q'", "logical_strength": 0.8, "roundtrip": "P ⇒ Q", "roundtrip_match": true, "manual_approval": true}, {"original": "🧠 reasoning", "ast_type": "Variable", "semantic_meaning": "variable 'reasoning'", "logical_strength": 0.5, "roundtrip": "reasoning", "roundtrip_match": false, "manual_approval": true}, {"original": "P ∧ Q ∨ R", "ast_type": "LogicalConjunction", "semantic_meaning": "variable 'P' and variable 'Q'", "logical_strength": 1.0, "roundtrip": "P ∧ Q", "roundtrip_match": false, "manual_approval": true}, {"original": "∃y: Q(y) ⇒ R(y)", "ast_type": "ExistentialQuantification", "semantic_meaning": "There exists y: variable 'Q'", "logical_strength": 0.7, "roundtrip": "∃y: Q", "roundtrip_match": false, "manual_approval": true}, {"original": "func_name(x, y)", "ast_type": "Variable", "semantic_meaning": "variable 'func_name'", "logical_strength": 0.5, "roundtrip": "func_name", "roundtrip_match": false, "manual_approval": true}, {"original": "x ∈ A ∩ B", "ast_type": "Variable", "semantic_meaning": "variable 'x'", "logical_strength": 0.5, "roundtrip": "x", "roundtrip_match": false, "manual_approval": true}, {"original": "¬(P ∨ Q)", "ast_type": "LogicalDisjunction", "semantic_meaning": "variable 'P' or variable 'Q'", "logical_strength": 0.6, "roundtrip": "P ∨ Q", "roundtrip_match": false, "manual_approval": true}], "note": "Automated analysis - manual human review recommended"}, "execution_time_seconds": 0.019154071807861328, "error": null}, {"level": 5, "name": "Fuzzy Roundtrip", "status": "PASS", "score": 1.0, "details": {"semantic_fidelity": 1.0, "roundtrip_accuracy": 1.0, "combined_score": 1.0, "total_test_cases": 19, "successful_cases": 19}, "execution_time_seconds": 0.0934140682220459, "error": null}, {"level": 6, "name": "Distribution Analysis", "status": "PASS", "score": 1.0, "details": {"category_statistics": {"variable": {"count": 2, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "literal": {"count": 2, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "implication": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "conjunction": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "disjunction": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "negation": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "universal": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "existential": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "complex_logic": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "quantified_implication": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "negated_disjunction": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "set_membership": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "empty_set": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "inference": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "meta_concept": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "function_call": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}, "long_variable": {"count": 1, "min_fidelity": 1.0, "max_fidelity": 1.0, "avg_fidelity": 1.0, "all_above_threshold": true}}, "uniform_coverage": true, "average_minimum_fidelity": 1.0, "total_categories": 17, "distribution_score": 1.0}, "execution_time_seconds": 0.00023412704467773438, "error": null}], "overall_status": "FAILED", "overall_score": 0.8333333333333334, "certification_hash": "100a2eff2d033964c4f78d7133d86652fc3196b46070c45f8c19e54b6fbf3635", "reproducibility_guarantee": true, "external_validity": false}