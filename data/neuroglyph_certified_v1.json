{"metadata": {"name": "NEUROGLYPH Certified Dataset v1.0", "description": "Epistemologically pure dataset for NG GODMODE v1 fine-tuning", "parser": "Formal grammar-based", "certification_date": "2024-12-19", "total_patterns": 25, "validated_patterns": 25, "failed_patterns": 0, "validation_rate": 1.0, "min_fidelity_required": 0.95, "audit_lock_verified": true}, "patterns": [{"input": "∀x: P(x)", "output": "∀x: P(x)", "fidelity": 1.0, "ast_type": "UniversalQuantification"}, {"input": "∃x: P(x)", "output": "∃x: P(x)", "fidelity": 1.0, "ast_type": "ExistentialQuantification"}, {"input": "∀x ∈ ℝ: P(x)", "output": "∀x ∈ ℝ: P(x)", "fidelity": 1.0, "ast_type": "UniversalQuantification"}, {"input": "∃x ∈ ℕ: P(x)", "output": "∃x ∈ ℕ: P(x)", "fidelity": 1.0, "ast_type": "ExistentialQuantification"}, {"input": "∀x ∃y: R(x,y)", "output": "∀x ∃y: R(x,y)", "fidelity": 1.0, "ast_type": "UniversalQuantification"}, {"input": "P ⇒ Q", "output": "P ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication"}, {"input": "P ∧ Q", "output": "P ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction"}, {"input": "P ∨ Q", "output": "P ∨ Q", "fidelity": 1.0, "ast_type": "LogicalDisjunction"}, {"input": "¬P", "output": "¬P", "fidelity": 1.0, "ast_type": "LogicalNegation"}, {"input": "¬(P ∨ Q)", "output": "¬(P ∨ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation"}, {"input": "P ∧ Q ⇒ R", "output": "P ∧ Q ⇒ R", "fidelity": 1.0, "ast_type": "MaterialImplication"}, {"input": "A ∪ B", "output": "A ∪ B", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "A ∩ B", "output": "A ∩ B", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "x ∈ A", "output": "x ∈ A", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "A ⊆ B", "output": "A ⊆ B", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "x + y", "output": "x + y", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "f(x)", "output": "f(x)", "fidelity": 1.0, "ast_type": "FunctionCall"}, {"input": "x = y", "output": "x = y", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "∫ f(x) dx", "output": "∫ f(x) dx", "fidelity": 1.0, "ast_type": "Integral"}, {"input": "∑ xᵢ", "output": "∑ xᵢ", "fidelity": 1.0, "ast_type": "Summation"}, {"input": "∏ xᵢ", "output": "∏ xᵢ", "fidelity": 1.0, "ast_type": "Product"}, {"input": "∂f/∂x", "output": "∂f/∂x", "fidelity": 1.0, "ast_type": "PartialDerivative"}, {"input": "(P ∨ Q)", "output": "(P ∨ Q)", "fidelity": 1.0, "ast_type": "GroupedExpression"}, {"input": "f(x,y)", "output": "f(x,y)", "fidelity": 1.0, "ast_type": "FunctionCall"}, {"input": "x ≠ y", "output": "x ≠ y", "fidelity": 1.0, "ast_type": "BinaryOperation"}], "failed_patterns": []}