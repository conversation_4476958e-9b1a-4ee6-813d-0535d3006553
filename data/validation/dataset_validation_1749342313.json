{"dataset_path": "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl", "total_examples": 100, "metrics": {"registry_compliance_rate": 1.0, "tokenization_success_rate": 1.0, "parsing_success_rate": 1.0, "roundtrip_fidelity_rate": 0.0, "symbol_coverage_rate": 0.6933333333333334, "overall_quality_score": 0.7346666666666666}, "quality_thresholds": {"registry_compliance": 1.0, "tokenization_integrity": 0.98, "parsing_success_rate": 0.95, "roundtrip_fidelity": 0.95, "symbol_coverage": 0.9}, "quality_assessment": {"overall_pass": true, "failed_criteria": ["registry_compliance", "tokenization_integrity", "roundtrip_fidelity", "symbol_coverage"]}, "statistics": {"category_distribution": {"set_theory": 6, "mathematical_reasoning": 25, "complex_reasoning": 13, "narrative_reasoning": 2, "meta_reasoning": 5, "logical_reasoning": 32, "symbolic_compression": 1, "code_generation": 10, "reverse_reasoning": 6}, "difficulty_distribution": {"medium": 42, "hard": 37, "easy": 21}, "symbol_usage": {"⊇": 2, "⊢": 90, "∧": 85, "∫": 27, "√": 1, "≤": 4, "∀": 43, "→": 7, "∃": 41, "∘": 5, "⇒": 46, "∈": 9, "⇔": 9, "¬": 9, "∨": 8, "∴": 5, "≠": 9, "∂": 2, "≈": 2, "⊈": 1, "⊭": 3, "⊥": 3, "🔄": 10, "⚡": 10, "⟧": 10, "⤴": 10, "🏛️": 10, "⟦": 10, "⇄": 2, "≡": 4, "⊆": 3, "⊨": 3, "∞": 2, "⟨": 2, "∑": 3, "⟩": 1, "∇": 1, "∱": 2, "∲": 1, "∆": 1, "±": 1, "⊤": 2, "∏": 4, "⊂": 3, "∅": 3, "∰": 1, "⇆": 2, "↔": 1, "⟷": 4, "∮": 1, "≥": 2, "∳": 1}, "error_count": 0, "top_symbols": {"⊢": 90, "∧": 85, "⇒": 46, "∀": 43, "∃": 41, "∫": 27, "🔄": 10, "⚡": 10, "⟧": 10, "⤴": 10, "🏛️": 10, "⟦": 10, "∈": 9, "⇔": 9, "¬": 9, "≠": 9, "∨": 8, "→": 7, "∘": 5, "∴": 5}}, "component_stats": {"tokenizer": {"total_tokens": 8076, "concept_tokens": 3384, "unknown_symbols": 3, "zero_splitting_rate": 0.41901931649331353}, "parser": {"total_parses": 400, "successful_parses": 400, "pattern_matches": {"logical_conjunction": 86, "empty_set": 10, "variable": 270, "universal_quantification": 10, "logical_negation": 10, "material_implication": 14}, "semantic_errors": 0, "success_rate": 1.0}}, "elapsed_time_seconds": 0.6074459552764893}