{"metadata": {"total_concepts": 84, "version": "2.0", "description": "NEUROGLYPH Conceptual Registry"}, "concepts": [{"concept_id": 1001, "symbol": "∀", "unicode_codepoint": "U+2200", "concept_name": "universal_quantification", "semantic_type": "quantifier", "arity": 3, "meaning": "for all elements in domain", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1002, "symbol": "∃", "unicode_codepoint": "U+2203", "concept_name": "existential_quantification", "semantic_type": "quantifier", "arity": 3, "meaning": "there exists at least one", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1101, "symbol": "⇒", "unicode_codepoint": "U+21D2", "concept_name": "material_implication", "semantic_type": "logical_connective", "arity": 2, "meaning": "if premise then conclusion", "logical_strength": 0.8, "aliases": []}, {"concept_id": 1102, "symbol": "⇔", "unicode_codepoint": "U+21D4", "concept_name": "biconditional", "semantic_type": "logical_connective", "arity": 2, "meaning": "if and only if", "logical_strength": 0.9, "aliases": []}, {"concept_id": 1103, "symbol": "∧", "unicode_codepoint": "U+2227", "concept_name": "logical_conjunction", "semantic_type": "logical_connective", "arity": 2, "meaning": "logical and", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1104, "symbol": "∨", "unicode_codepoint": "U+2228", "concept_name": "logical_disjunction", "semantic_type": "logical_connective", "arity": 2, "meaning": "logical or", "logical_strength": 0.7, "aliases": []}, {"concept_id": 1105, "symbol": "¬", "unicode_codepoint": "U+00AC", "concept_name": "logical_negation", "semantic_type": "logical_connective", "arity": 1, "meaning": "logical not", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1106, "symbol": "≡", "unicode_codepoint": "U+2261", "concept_name": "logical_equivalence", "semantic_type": "logical_connective", "arity": 2, "meaning": "logically equivalent", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1201, "symbol": "⊢", "unicode_codepoint": "U+22A2", "concept_name": "syntactic_entailment", "semantic_type": "inference", "arity": 2, "meaning": "syntactically entails", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1202, "symbol": "⊨", "unicode_codepoint": "U+22A8", "concept_name": "semantic_entailment", "semantic_type": "inference", "arity": 2, "meaning": "semantically entails", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1203, "symbol": "⊭", "unicode_codepoint": "U+22AD", "concept_name": "does_not_entail", "semantic_type": "inference", "arity": 2, "meaning": "does not entail", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1204, "symbol": "⊤", "unicode_codepoint": "U+22A4", "concept_name": "logical_truth", "semantic_type": "inference", "arity": 0, "meaning": "logical truth", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1205, "symbol": "⊥", "unicode_codepoint": "U+22A5", "concept_name": "logical_falsehood", "semantic_type": "inference", "arity": 0, "meaning": "logical falsehood", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1301, "symbol": "∈", "unicode_codepoint": "U+2208", "concept_name": "set_membership", "semantic_type": "set_theory", "arity": 2, "meaning": "is element of", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1302, "symbol": "∉", "unicode_codepoint": "U+2209", "concept_name": "not_set_membership", "semantic_type": "set_theory", "arity": 2, "meaning": "is not element of", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1303, "symbol": "⊂", "unicode_codepoint": "U+2282", "concept_name": "proper_subset", "semantic_type": "set_theory", "arity": 2, "meaning": "is proper subset of", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1304, "symbol": "⊆", "unicode_codepoint": "U+2286", "concept_name": "subset_or_equal", "semantic_type": "set_theory", "arity": 2, "meaning": "is subset or equal to", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1305, "symbol": "⊇", "unicode_codepoint": "U+2287", "concept_name": "superset_or_equal", "semantic_type": "set_theory", "arity": 2, "meaning": "is superset or equal to", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1306, "symbol": "∩", "unicode_codepoint": "U+2229", "concept_name": "set_intersection", "semantic_type": "set_theory", "arity": 2, "meaning": "intersection of sets", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1307, "symbol": "∪", "unicode_codepoint": "U+222A", "concept_name": "set_union", "semantic_type": "set_theory", "arity": 2, "meaning": "union of sets", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1308, "symbol": "∅", "unicode_codepoint": "U+2205", "concept_name": "empty_set", "semantic_type": "set_theory", "arity": 0, "meaning": "empty set", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1401, "symbol": "∫", "unicode_codepoint": "U+222B", "concept_name": "definite_integral", "semantic_type": "mathematics", "arity": 3, "meaning": "definite integral", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1402, "symbol": "∑", "unicode_codepoint": "U+2211", "concept_name": "summation", "semantic_type": "mathematics", "arity": 3, "meaning": "summation over range", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1403, "symbol": "∏", "unicode_codepoint": "U+220F", "concept_name": "product", "semantic_type": "mathematics", "arity": 3, "meaning": "product over range", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1404, "symbol": "∂", "unicode_codepoint": "U+2202", "concept_name": "partial_derivative", "semantic_type": "mathematics", "arity": 2, "meaning": "partial derivative", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1405, "symbol": "∇", "unicode_codepoint": "U+2207", "concept_name": "gradient", "semantic_type": "mathematics", "arity": 1, "meaning": "gradient operator", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1406, "symbol": "√", "unicode_codepoint": "U+221A", "concept_name": "square_root", "semantic_type": "mathematics", "arity": 1, "meaning": "square root", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1407, "symbol": "∞", "unicode_codepoint": "U+221E", "concept_name": "infinity", "semantic_type": "mathematics", "arity": 0, "meaning": "infinity", "logical_strength": 1.0, "aliases": []}, {"concept_id": 1501, "symbol": "🧠", "unicode_codepoint": "U+1F9E0", "concept_name": "reasoning_process", "semantic_type": "meta_concept", "arity": 1, "meaning": "reasoning or thinking process", "logical_strength": 0.9, "aliases": []}, {"concept_id": 1502, "symbol": "⚡", "unicode_codepoint": "U+26A1", "concept_name": "function_concept", "semantic_type": "meta_concept", "arity": 1, "meaning": "function or energy concept", "logical_strength": 0.8, "aliases": []}, {"concept_id": 1503, "symbol": "🔄", "unicode_codepoint": "U+1F504", "concept_name": "iterative_process", "semantic_type": "meta_concept", "arity": 1, "meaning": "iteration or cycle", "logical_strength": 0.8, "aliases": []}, {"concept_id": 1504, "symbol": "🏛", "unicode_codepoint": "U+1F3DB", "concept_name": "structural_concept", "semantic_type": "meta_concept", "arity": 1, "meaning": "structural or architectural concept", "logical_strength": 0.7, "aliases": []}, {"concept_id": 1505, "symbol": "📖", "unicode_codepoint": "U+1F4D6", "concept_name": "knowledge_concept", "semantic_type": "meta_concept", "arity": 1, "meaning": "knowledge or learning", "logical_strength": 0.8, "aliases": []}, {"concept_id": 1506, "symbol": "🔮", "unicode_codepoint": "U+1F52E", "concept_name": "prediction_concept", "semantic_type": "meta_concept", "arity": 1, "meaning": "prediction or foresight", "logical_strength": 0.7, "aliases": []}, {"concept_id": 2000, "symbol": "=", "unicode_codepoint": "U+003D", "concept_name": "equality", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical equality relation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2001, "symbol": "±", "unicode_codepoint": "U+00B1", "concept_name": "unicode_00b1", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '±' (U+00B1)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2002, "symbol": "≤", "unicode_codepoint": "U+2264", "concept_name": "less_equal", "semantic_type": "mathematics", "arity": 2, "meaning": "less than or equal relation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2003, "symbol": "∆", "unicode_codepoint": "U+2206", "concept_name": "unicode_2206", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∆' (U+2206)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2004, "symbol": "️", "unicode_codepoint": "U+FE0F", "concept_name": "unicode_fe0f", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '️' (U+FE0F)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2005, "symbol": "/", "unicode_codepoint": "U+002F", "concept_name": "division", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical division operation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2006, "symbol": "²", "unicode_codepoint": "U+00B2", "concept_name": "digit_²", "semantic_type": "literal", "arity": 1, "meaning": "numeric digit '²'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2007, "symbol": "?", "unicode_codepoint": "U+003F", "concept_name": "question", "semantic_type": "meta_concept", "arity": 2, "meaning": "question or unknown placeholder", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2008, "symbol": "🏛️", "unicode_codepoint": "multi-char", "concept_name": "multichar_7949", "semantic_type": "meta_concept", "arity": 2, "meaning": "multi-character symbol '🏛️'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2009, "symbol": "🔢", "unicode_codepoint": "U+1F522", "concept_name": "math_context", "semantic_type": "meta_concept", "arity": 2, "meaning": "mathematical context indicator", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2010, "symbol": "∴", "unicode_codepoint": "U+2234", "concept_name": "therefore", "semantic_type": "logical_connective", "arity": 2, "meaning": "logical therefore conclusion", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2011, "symbol": "∘", "unicode_codepoint": "U+2218", "concept_name": "composition", "semantic_type": "mathematics", "arity": 2, "meaning": "function composition operator", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2012, "symbol": "⟦", "unicode_codepoint": "U+27E6", "concept_name": "bracket_open", "semantic_type": "meta_concept", "arity": 2, "meaning": "semantic bracket opening", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2013, "symbol": "|", "unicode_codepoint": "U+007C", "concept_name": "pipe", "semantic_type": "logical_connective", "arity": 2, "meaning": "logical pipe or separator", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2014, "symbol": "⟧", "unicode_codepoint": "U+27E7", "concept_name": "bracket_close", "semantic_type": "meta_concept", "arity": 2, "meaning": "semantic bracket closing", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2015, "symbol": "+", "unicode_codepoint": "U+002B", "concept_name": "addition", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical addition operation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2016, "symbol": "≈", "unicode_codepoint": "U+2248", "concept_name": "unicode_2248", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '≈' (U+2248)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2017, "symbol": "↔", "unicode_codepoint": "U+2194", "concept_name": "unicode_2194", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '↔' (U+2194)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2018, "symbol": "∮", "unicode_codepoint": "U+222E", "concept_name": "unicode_222e", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∮' (U+222E)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2019, "symbol": "∳", "unicode_codepoint": "U+2233", "concept_name": "counterclockwise_integral", "semantic_type": "mathematics", "arity": 2, "meaning": "counterclockwise contour integral", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2020, "symbol": "ℝ", "unicode_codepoint": "U+211D", "concept_name": "variable_ℝ", "semantic_type": "variable", "arity": 1, "meaning": "variable or identifier 'ℝ'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2021, "symbol": "⇄", "unicode_codepoint": "U+21C4", "concept_name": "unicode_21c4", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '⇄' (U+21C4)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2022, "symbol": "∱", "unicode_codepoint": "U+2231", "concept_name": "unicode_2231", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∱' (U+2231)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2023, "symbol": "⟩", "unicode_codepoint": "U+27E9", "concept_name": "unicode_27e9", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '⟩' (U+27E9)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2024, "symbol": "-", "unicode_codepoint": "U+002D", "concept_name": "subtraction", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical subtraction operation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2025, "symbol": "è", "unicode_codepoint": "U+00E8", "concept_name": "variable_è", "semantic_type": "variable", "arity": 1, "meaning": "variable or identifier 'è'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2026, "symbol": "≥", "unicode_codepoint": "U+2265", "concept_name": "greater_equal", "semantic_type": "mathematics", "arity": 2, "meaning": "greater than or equal relation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2027, "symbol": "↕", "unicode_codepoint": "U+2195", "concept_name": "unicode_2195", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '↕' (U+2195)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2028, "symbol": "⟨", "unicode_codepoint": "U+27E8", "concept_name": "unicode_27e8", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '⟨' (U+27E8)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2029, "symbol": "ò", "unicode_codepoint": "U+00F2", "concept_name": "variable_ò", "semantic_type": "variable", "arity": 1, "meaning": "variable or identifier 'ò'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2030, "symbol": "⇆", "unicode_codepoint": "U+21C6", "concept_name": "bidirectional", "semantic_type": "meta_concept", "arity": 2, "meaning": "bidirectional arrow", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2031, "symbol": "³", "unicode_codepoint": "U+00B3", "concept_name": "digit_³", "semantic_type": "literal", "arity": 1, "meaning": "numeric digit '³'", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2032, "symbol": "≠", "unicode_codepoint": "U+2260", "concept_name": "inequality", "semantic_type": "mathematics", "arity": 2, "meaning": "mathematical inequality relation", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2033, "symbol": "⊈", "unicode_codepoint": "U+2288", "concept_name": "not_subset", "semantic_type": "set_theory", "arity": 2, "meaning": "not subset relation", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2034, "symbol": "∯", "unicode_codepoint": "U+222F", "concept_name": "unicode_222f", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∯' (U+222F)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2035, "symbol": "⟷", "unicode_codepoint": "U+27F7", "concept_name": "unicode_27f7", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '⟷' (U+27F7)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2036, "symbol": "⤴", "unicode_codepoint": "U+2934", "concept_name": "return", "semantic_type": "meta_concept", "arity": 2, "meaning": "function return or output", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2037, "symbol": "λ", "unicode_codepoint": "U+03BB", "concept_name": "lambda", "semantic_type": "mathematics", "arity": 2, "meaning": "lambda function", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2038, "symbol": "∲", "unicode_codepoint": "U+2232", "concept_name": "clockwise_integral", "semantic_type": "mathematics", "arity": 2, "meaning": "clockwise contour integral", "logical_strength": 1.0, "aliases": []}, {"concept_id": 2039, "symbol": "→", "unicode_codepoint": "U+2192", "concept_name": "arrow", "semantic_type": "meta_concept", "arity": 2, "meaning": "functional arrow or mapping", "logical_strength": 0.8, "aliases": []}, {"concept_id": 2040, "symbol": "∰", "unicode_codepoint": "U+2230", "concept_name": "unicode_2230", "semantic_type": "meta_concept", "arity": 2, "meaning": "unicode symbol '∰' (U+2230)", "logical_strength": 0.8, "aliases": []}, {"concept_id": 3000, "symbol": "∄", "unicode_codepoint": "U+2204", "concept_name": "not_exists", "semantic_type": "quantifier", "arity": 3, "meaning": "there does not exist", "logical_strength": 1.0, "aliases": []}, {"concept_id": 3001, "symbol": "⊕", "unicode_codepoint": "U+2295", "concept_name": "exclusive_or", "semantic_type": "logical_connective", "arity": 2, "meaning": "exclusive or (XOR)", "logical_strength": 0.9, "aliases": []}, {"concept_id": 3002, "symbol": "⊬", "unicode_codepoint": "U+22AC", "concept_name": "does_not_prove", "semantic_type": "inference", "arity": 2, "meaning": "does not prove", "logical_strength": 1.0, "aliases": []}, {"concept_id": 3003, "symbol": "⊦", "unicode_codepoint": "U+22A6", "concept_name": "assertion", "semantic_type": "inference", "arity": 2, "meaning": "assertion or theorem", "logical_strength": 1.0, "aliases": []}, {"concept_id": 3004, "symbol": "⊧", "unicode_codepoint": "U+22A7", "concept_name": "models", "semantic_type": "inference", "arity": 2, "meaning": "models or satisfies", "logical_strength": 1.0, "aliases": []}, {"concept_id": 3005, "symbol": "≢", "unicode_codepoint": "U+2262", "concept_name": "not_equivalent", "semantic_type": "logical_connective", "arity": 2, "meaning": "not logically equivalent", "logical_strength": 0.9, "aliases": []}, {"concept_id": 3006, "symbol": "⊬", "unicode_codepoint": "U+22AC", "concept_name": "not_syntactic_consequence", "semantic_type": "logical_connective", "arity": 2, "meaning": "not syntactic consequence", "logical_strength": 0.9, "aliases": []}, {"concept_id": 3007, "symbol": "⊸", "unicode_codepoint": "U+22B8", "concept_name": "linear_implication", "semantic_type": "logical_connective", "arity": 2, "meaning": "linear logic implication", "logical_strength": 0.9, "aliases": []}, {"concept_id": 3008, "symbol": "⊗", "unicode_codepoint": "U+2297", "concept_name": "tensor_product", "semantic_type": "logical_connective", "arity": 2, "meaning": "tensor product", "logical_strength": 0.9, "aliases": []}]}