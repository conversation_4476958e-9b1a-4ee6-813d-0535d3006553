# NEUROGLYPH Pre-commit Configuration
# Enforces Immutable Principles compliance before commits

repos:
  - repo: local
    hooks:
      # NEUROGLYPH Symbol Uniqueness Linter
      - id: neuroglyph-symbol-uniqueness
        name: NEUROGLYPH Symbol Uniqueness Check
        entry: python3 tools/check_symbol_uniqueness.py
        language: system
        files: ^(data/.*\.json|scripts/.*\.py|neuroglyph/.*\.py)$
        always_run: true
        pass_filenames: false
        description: "Verifies Unicode uniqueness and immutable principles compliance"
        
      # Tokenizer Freeze Validation
      - id: neuroglyph-tokenizer-freeze
        name: NEUROGLYPH Tokenizer Freeze Check
        entry: python3 scripts/tokenizer_freeze_validator.py --action validate
        language: system
        files: ^(.*tokenizer.*\.json|scripts/.*training.*\.py)$
        always_run: false
        pass_filenames: false
        description: "Validates tokenizer freeze integrity"
        
      # Dataset Audit
      - id: neuroglyph-dataset-audit
        name: NEUROGLYPH Dataset Audit
        entry: python3 scripts/test_expanded_dataset_audit.py
        language: system
        files: ^data/.*certified.*\.json$
        always_run: false
        pass_filenames: false
        description: "Audits dataset compliance with 5 Immutable Principles"
        
      # Python Code Quality
      - id: python-syntax-check
        name: Python Syntax Check
        entry: python3 -m py_compile
        language: system
        files: \.py$
        description: "Checks Python syntax"
        
  # Standard hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        exclude: ^docs/.*\.md$
      - id: end-of-file-fixer
        exclude: ^docs/.*\.md$
      - id: check-yaml
      - id: check-json
        files: ^(data/.*\.json|.*config.*\.json)$
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-merge-conflict
        
  # JSON formatting for NEUROGLYPH datasets
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        files: ^(data/.*\.json|.*config.*\.json)$
        args: ['--tab-width=2', '--print-width=120']

# Configuration
default_stages: [commit, push]
fail_fast: true

# CI/CD Integration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks
    
    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
