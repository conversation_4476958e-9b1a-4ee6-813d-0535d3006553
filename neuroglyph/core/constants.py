#!/usr/bin/env python3
"""
NEUROGLYPH - Immutable Audit Constants

🔒 LOCKED AUDIT STANDARDS - NON MODIFICABILE
===============================================

Questi valori rappresentano i PRINCIPI IMMUTABILI di NEUROGLYPH
e NON POSSONO essere modificati senza violare l'integrità epistemologica.

ATTENZIONE: Qualsiasi modifica a questo file invalida automaticamente
tutti i test di audit e compromette la certificazione scientifica.

HASH INTEGRITY: Questo file è protetto da SHA256 checksum.
Modifiche non autorizzate causeranno fallimento CI/CD.

PRINCIPI IMMUTABILI NON NEGOZIABILI:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint  
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita (≥95%)
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

# ============================================================================
# 🔒 IMMUTABLE AUDIT STANDARDS - DO NOT MODIFY
# ============================================================================

# === FIDELITY STANDARDS ===
AUDIT_FIDELITY_THRESHOLD = 0.95
"""
Soglia minima di fidelity per roundtrip AST ↔ NEUROGLYPH ↔ AST.
IMMUTABILE: Rappresenta il 95% richiesto dai Principi Immutabili.
"""

AUDIT_FIDELITY_PERFECT = 1.0
"""
Fidelity perfetta per exact match.
IMMUTABILE: Standard di eccellenza assoluta.
"""

# === SUCCESS RATE STANDARDS ===
AUDIT_SUCCESS_RATE_REQUIRED = 0.95
"""
Tasso di successo minimo richiesto per pattern validi (24/25 = 95%).
IMMUTABILE: Standard epistemologico per certificazione.
"""

AUDIT_SUCCESS_RATE_PERFECT = 1.0
"""
Tasso di successo perfetto (25/25 = 100%).
IMMUTABILE: Obiettivo di eccellenza.
"""

# === REJECTION RATE STANDARDS ===
AUDIT_REJECTION_RATE_MIN = 0.95
"""
Tasso minimo di rifiuto per pattern sintatticamente invalidi.
IMMUTABILE: Zero tolerance per errori semantici.
"""

AUDIT_REJECTION_RATE_PERFECT = 1.0
"""
Tasso di rifiuto perfetto per pattern invalidi.
IMMUTABILE: Standard di rigore assoluto.
"""

# === ROUNDTRIP STANDARDS ===
AUDIT_ROUNDTRIP_REQUIRED = True
"""
Roundtrip AST ↔ NEUROGLYPH obbligatorio per ogni pattern.
IMMUTABILE: Principio di reversibilità.
"""

AUDIT_SEMANTIC_ZERO_TOLERANCE = True
"""
Zero tolerance per errori semantici o sintattici.
IMMUTABILE: Principio di rigore scientifico.
"""

# === PATTERN VALIDATION STANDARDS ===
AUDIT_VALID_PATTERNS_MIN = 25
"""
Numero minimo di pattern validi che devono passare.
IMMUTABILE: Copertura completa dei costrutti NEUROGLYPH.
"""

AUDIT_INVALID_PATTERNS_MIN = 50
"""
Numero minimo di pattern invalidi da testare per rigore.
IMMUTABILE: Verifica robustezza parser.
"""

# === TOKENIZATION STANDARDS ===
AUDIT_ZERO_SPLITTING_REQUIRED = True
"""
Zero-splitting obbligatorio: 1 simbolo = 1 token.
IMMUTABILE: Principio di atomicità.
"""

AUDIT_REGISTRY_COMPLIANCE_REQUIRED = True
"""
Compliance al registry simbolico obbligatoria.
IMMUTABILE: Principio di unicità Unicode.
"""

# === QUALITY GATES ===
AUDIT_QUALITY_GATE_FIDELITY = 0.95
"""
Quality gate per fidelity: blocca pipeline se < 95%.
IMMUTABILE: Controllo automatico qualità.
"""

AUDIT_QUALITY_GATE_SUCCESS = 0.95
"""
Quality gate per success rate: blocca pipeline se < 95%.
IMMUTABILE: Controllo automatico qualità.
"""

AUDIT_QUALITY_GATE_REJECTION = 0.95
"""
Quality gate per rejection rate: blocca pipeline se < 95%.
IMMUTABILE: Controllo automatico qualità.
"""

# ============================================================================
# 🔒 INTEGRITY VERIFICATION CONSTANTS
# ============================================================================

AUDIT_CONSTANTS_VERSION = "1.0.0"
"""
Versione delle costanti di audit.
IMMUTABILE: Tracciamento modifiche autorizzate.
"""

AUDIT_LOCK_ENABLED = True
"""
Sistema di lock attivo per protezione integrità.
IMMUTABILE: Protezione meccanica.
"""

AUDIT_HASH_VERIFICATION_REQUIRED = True
"""
Verifica hash SHA256 obbligatoria.
IMMUTABILE: Controllo integrità file.
"""

# ============================================================================
# 🔒 ERROR MESSAGES - IMMUTABLE
# ============================================================================

AUDIT_ERROR_FIDELITY_VIOLATION = (
    "🚨 AUDIT VIOLATION: Fidelity {actual:.1%} < {required:.1%} "
    "violates Immutable Principle #3 (Reversibility)"
)

AUDIT_ERROR_SUCCESS_VIOLATION = (
    "🚨 AUDIT VIOLATION: Success rate {actual:.1%} < {required:.1%} "
    "violates epistemological standards"
)

AUDIT_ERROR_REJECTION_VIOLATION = (
    "🚨 AUDIT VIOLATION: Rejection rate {actual:.1%} < {required:.1%} "
    "violates zero tolerance principle"
)

AUDIT_ERROR_CONSTANTS_MODIFIED = (
    "🔒 INTEGRITY VIOLATION: Audit constants have been modified. "
    "This compromises NEUROGLYPH epistemological integrity."
)

AUDIT_ERROR_HASH_MISMATCH = (
    "🔐 HASH VIOLATION: constants.py integrity check failed. "
    "File has been tampered with."
)

# ============================================================================
# 🔒 VALIDATION SETS - IMMUTABLE
# ============================================================================

AUDIT_RESERVED_OPERATORS = frozenset({
    '∀', '∃', '∧', '∨', '⇒', '¬', '⊢', '⊨', '≡', '⇔',
    '∪', '∩', '∈', '⊆', '⊇', '⊂', '⊃', '∅',
    '∫', '∑', '∏', '∂', '∇', '△', '∞',
    '+', '-', '*', '/', '=', '≠', '<', '>', '≤', '≥',
    '(', ')', '[', ']', '{', '}', ':', ';', ',', '.',
    'd', 'dx', 'dy', 'dz'
})
"""
Set immutabile di operatori riservati.
IMMUTABILE: Non possono essere usati come nomi di variabili.
"""

AUDIT_INVALID_PATTERNS = frozenset({
    'BROKEN_SYNTAX', '¬∨', '∀∃', '⇒∧', '∩∪', '∈⊆',
    '', '   ', '∧', '∨', '⇒', '¬', '∀', '∃'
})
"""
Set immutabile di pattern sintatticamente invalidi.
IMMUTABILE: Devono sempre essere rifiutati dal parser.
"""

# ============================================================================
# 🔒 METADATA - IMMUTABLE
# ============================================================================

AUDIT_CREATION_DATE = "2024-12-19"
AUDIT_AUTHOR = "NEUROGLYPH Development Team"
AUDIT_PURPOSE = "Immutable epistemological standards enforcement"
AUDIT_MODIFICATION_POLICY = "FORBIDDEN - Violates Immutable Principles"

# ============================================================================
# 🔒 LOCK VERIFICATION FUNCTION
# ============================================================================

def verify_audit_constants_integrity():
    """
    Verifica l'integrità delle costanti di audit.
    
    Questa funzione DEVE essere chiamata all'inizio di ogni test critico
    per garantire che i Principi Immutabili non siano stati violati.
    
    Raises:
        AssertionError: Se le costanti sono state modificate
    """
    # Verifica valori critici
    assert AUDIT_FIDELITY_THRESHOLD == 0.95, AUDIT_ERROR_CONSTANTS_MODIFIED
    assert AUDIT_SUCCESS_RATE_REQUIRED == 0.95, AUDIT_ERROR_CONSTANTS_MODIFIED  
    assert AUDIT_REJECTION_RATE_MIN == 0.95, AUDIT_ERROR_CONSTANTS_MODIFIED
    assert AUDIT_ROUNDTRIP_REQUIRED is True, AUDIT_ERROR_CONSTANTS_MODIFIED
    assert AUDIT_SEMANTIC_ZERO_TOLERANCE is True, AUDIT_ERROR_CONSTANTS_MODIFIED
    assert AUDIT_LOCK_ENABLED is True, AUDIT_ERROR_CONSTANTS_MODIFIED
    
    # Verifica immutabilità set
    assert len(AUDIT_RESERVED_OPERATORS) == 49, AUDIT_ERROR_CONSTANTS_MODIFIED
    assert len(AUDIT_INVALID_PATTERNS) == 14, AUDIT_ERROR_CONSTANTS_MODIFIED
    
    return True


# ============================================================================
# 🔒 EXPORT CONTROL - ONLY IMMUTABLE CONSTANTS
# ============================================================================

__all__ = [
    # Fidelity standards
    'AUDIT_FIDELITY_THRESHOLD',
    'AUDIT_FIDELITY_PERFECT',
    
    # Success rate standards  
    'AUDIT_SUCCESS_RATE_REQUIRED',
    'AUDIT_SUCCESS_RATE_PERFECT',
    
    # Rejection rate standards
    'AUDIT_REJECTION_RATE_MIN', 
    'AUDIT_REJECTION_RATE_PERFECT',
    
    # Roundtrip standards
    'AUDIT_ROUNDTRIP_REQUIRED',
    'AUDIT_SEMANTIC_ZERO_TOLERANCE',
    
    # Pattern standards
    'AUDIT_VALID_PATTERNS_MIN',
    'AUDIT_INVALID_PATTERNS_MIN',
    
    # Tokenization standards
    'AUDIT_ZERO_SPLITTING_REQUIRED',
    'AUDIT_REGISTRY_COMPLIANCE_REQUIRED',
    
    # Quality gates
    'AUDIT_QUALITY_GATE_FIDELITY',
    'AUDIT_QUALITY_GATE_SUCCESS', 
    'AUDIT_QUALITY_GATE_REJECTION',
    
    # Integrity verification
    'AUDIT_CONSTANTS_VERSION',
    'AUDIT_LOCK_ENABLED',
    'AUDIT_HASH_VERIFICATION_REQUIRED',
    
    # Error messages
    'AUDIT_ERROR_FIDELITY_VIOLATION',
    'AUDIT_ERROR_SUCCESS_VIOLATION',
    'AUDIT_ERROR_REJECTION_VIOLATION',
    'AUDIT_ERROR_CONSTANTS_MODIFIED',
    'AUDIT_ERROR_HASH_MISMATCH',
    
    # Validation sets
    'AUDIT_RESERVED_OPERATORS',
    'AUDIT_INVALID_PATTERNS',
    
    # Verification function
    'verify_audit_constants_integrity'
]
