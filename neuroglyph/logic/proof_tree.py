"""
NEUROGLYPH Proof Tree
Strutture per alberi di prova verificabili
"""

import time
import uuid
from typing import List, Dict, Set, Optional, Any
from dataclasses import dataclass, field
from enum import Enum

from .formula import Formula, Variable, Term


class ProofRule(Enum):
    """Regole di inferenza logica."""
    MODUS_PONENS = "modus_ponens"           # P → Q, P ⊢ Q
    MODUS_TOLLENS = "modus_tollens"         # P → Q, ¬Q ⊢ ¬P
    UNIVERSAL_INST = "universal_inst"       # ∀x P(x) ⊢ P(t)
    EXISTENTIAL_GEN = "existential_gen"     # P(t) ⊢ ∃x P(x)
    CONJUNCTION_INTRO = "conjunction_intro" # P, Q ⊢ P ∧ Q
    CONJUNCTION_ELIM = "conjunction_elim"   # P ∧ Q ⊢ P (or Q)
    DISJUNCTION_INTRO = "disjunction_intro" # P ⊢ P ∨ Q
    DISJUNCTION_ELIM = "disjunction_elim"   # P ∨ Q, ¬P ⊢ Q
    ASSUMPTION = "assumption"               # Assunzione iniziale
    AXIOM = "axiom"                        # Assioma
    PREMISE = "premise"                    # Premessa data
    KG_LOOKUP = "kg_lookup"                # Fatto dal Knowledge Graph (Fase 6.0)


@dataclass
class ProofStep:
    """Singolo step in un proof tree."""
    
    step_id: str = field(default_factory=lambda: str(uuid.uuid4())[:8])
    formula: Formula = None
    rule: ProofRule = ProofRule.ASSUMPTION
    premises: List['ProofStep'] = field(default_factory=list)
    substitution: Dict[Variable, Term] = field(default_factory=dict)
    justification: str = ""
    timestamp: float = field(default_factory=time.time)
    
    def __str__(self) -> str:
        return f"{self.step_id}: {self.formula} [{self.rule.value}]"
    
    def to_dict(self) -> Dict[str, Any]:
        """Serializza step."""
        return {
            'step_id': self.step_id,
            'formula': self.formula.to_dict() if self.formula else None,
            'rule': self.rule.value,
            'premises': [p.step_id for p in self.premises],
            'substitution': {str(k): str(v) for k, v in self.substitution.items()},
            'justification': self.justification,
            'timestamp': self.timestamp
        }


@dataclass
class KGFactStep(ProofStep):
    """
    Step speciale per fatti provenienti dal Knowledge Graph.

    Estende ProofStep con metadati specifici per KG lookup.
    """

    fact_id: int = None                    # ID del fatto nel KG
    kg_source: str = "knowledge_graph"     # Fonte del fatto
    confidence: float = 1.0                # Confidence del fatto KG

    def __post_init__(self):
        """Inizializza step KG con regola specifica."""
        if self.rule != ProofRule.KG_LOOKUP:
            self.rule = ProofRule.KG_LOOKUP

        if self.justification == "":
            self.justification = f"Knowledge Graph lookup: fact_id={self.fact_id}"

    def to_dict(self) -> Dict[str, Any]:
        """Serializza step KG con metadati aggiuntivi."""
        base_dict = super().to_dict()
        base_dict.update({
            'fact_id': self.fact_id,
            'kg_source': self.kg_source,
            'confidence': self.confidence,
            'step_type': 'kg_fact'
        })
        return base_dict


@dataclass
class ProofTree:
    """Albero di prova completo."""
    
    proof_id: str = field(default_factory=lambda: str(uuid.uuid4())[:12])
    conclusion: Formula = None
    premises: List[Formula] = field(default_factory=list)
    steps: List[ProofStep] = field(default_factory=list)
    is_valid: bool = False
    depth: int = 0
    created_at: float = field(default_factory=time.time)
    
    def add_step(self, step: ProofStep) -> str:
        """Aggiunge step al proof tree."""
        self.steps.append(step)
        # FIX: Depth = numero di step sequenziali, non livelli di dipendenza
        self.depth = len(self.steps)
        return step.step_id
    
    def add_premise(self, formula: Formula, justification: str = "") -> str:
        """Aggiunge premessa."""
        step = ProofStep(
            formula=formula,
            rule=ProofRule.PREMISE,
            justification=justification or f"Given premise: {formula}"
        )
        return self.add_step(step)
    
    def add_axiom(self, formula: Formula, justification: str = "") -> str:
        """Aggiunge assioma."""
        step = ProofStep(
            formula=formula,
            rule=ProofRule.AXIOM,
            justification=justification or f"Axiom: {formula}"
        )
        return self.add_step(step)

    def add_kg_fact(self, formula: Formula, fact_id: int, confidence: float = 1.0, justification: str = "") -> str:
        """
        Aggiunge fatto dal Knowledge Graph.

        Args:
            formula: Formula del fatto
            fact_id: ID del fatto nel KG
            confidence: Confidence del fatto (default: 1.0)
            justification: Giustificazione personalizzata

        Returns:
            ID dello step aggiunto
        """
        step = KGFactStep(
            formula=formula,
            rule=ProofRule.KG_LOOKUP,
            fact_id=fact_id,
            confidence=confidence,
            justification=justification or f"Knowledge Graph fact #{fact_id}: {formula}"
        )
        return self.add_step(step)

    def apply_modus_ponens(self, implication_step: ProofStep, antecedent_step: ProofStep) -> str:
        """Applica modus ponens: P → Q, P ⊢ Q."""
        from .formula import Implication
        
        if not isinstance(implication_step.formula, Implication):
            raise ValueError("First premise must be implication")
        
        if implication_step.formula.antecedent != antecedent_step.formula:
            raise ValueError("Antecedent mismatch in modus ponens")
        
        conclusion = implication_step.formula.consequent
        
        step = ProofStep(
            formula=conclusion,
            rule=ProofRule.MODUS_PONENS,
            premises=[implication_step, antecedent_step],
            justification=f"Modus ponens: {implication_step.formula}, {antecedent_step.formula} ⊢ {conclusion}"
        )
        
        return self.add_step(step)
    
    def apply_universal_instantiation(self, universal_step: ProofStep, term: Term) -> str:
        """Applica istanziazione universale: ∀x P(x) ⊢ P(t)."""
        from .formula import Universal
        
        if not isinstance(universal_step.formula, Universal):
            raise ValueError("Premise must be universal quantification")
        
        universal_formula = universal_step.formula
        substitution = {universal_formula.variable: term}
        instantiated = universal_formula.formula.substitute(substitution)
        
        step = ProofStep(
            formula=instantiated,
            rule=ProofRule.UNIVERSAL_INST,
            premises=[universal_step],
            substitution=substitution,
            justification=f"Universal instantiation: {universal_formula} with {term} ⊢ {instantiated}"
        )
        
        return self.add_step(step)
    
    def apply_conjunction_introduction(self, left_step: ProofStep, right_step: ProofStep) -> str:
        """Applica introduzione congiunzione: P, Q ⊢ P ∧ Q."""
        from .formula import Conjunction
        
        conjunction = Conjunction(left_step.formula, right_step.formula)
        
        step = ProofStep(
            formula=conjunction,
            rule=ProofRule.CONJUNCTION_INTRO,
            premises=[left_step, right_step],
            justification=f"Conjunction introduction: {left_step.formula}, {right_step.formula} ⊢ {conjunction}"
        )
        
        return self.add_step(step)
    
    def apply_conjunction_elimination(self, conjunction_step: ProofStep, left: bool = True) -> str:
        """Applica eliminazione congiunzione: P ∧ Q ⊢ P (or Q)."""
        from .formula import Conjunction
        
        if not isinstance(conjunction_step.formula, Conjunction):
            raise ValueError("Premise must be conjunction")
        
        conjunction = conjunction_step.formula
        conclusion = conjunction.left if left else conjunction.right
        side = "left" if left else "right"
        
        step = ProofStep(
            formula=conclusion,
            rule=ProofRule.CONJUNCTION_ELIM,
            premises=[conjunction_step],
            justification=f"Conjunction elimination ({side}): {conjunction} ⊢ {conclusion}"
        )
        
        return self.add_step(step)
    
    def validate(self) -> bool:
        """Valida proof tree."""
        if not self.steps:
            self.is_valid = False
            return False
        
        # Verifica che ogni step sia valido
        for step in self.steps:
            if not self._validate_step(step):
                self.is_valid = False
                return False
        
        # Verifica che la conclusione sia derivata
        if self.conclusion:
            final_steps = [s for s in self.steps if s.formula == self.conclusion]
            if not final_steps:
                self.is_valid = False
                return False
        
        self.is_valid = True
        return True
    
    def _validate_step(self, step: ProofStep) -> bool:
        """Valida singolo step."""
        if step.rule in [ProofRule.PREMISE, ProofRule.AXIOM, ProofRule.ASSUMPTION, ProofRule.KG_LOOKUP]:
            return True
        
        if step.rule == ProofRule.MODUS_PONENS:
            return self._validate_modus_ponens(step)
        
        if step.rule == ProofRule.UNIVERSAL_INST:
            return self._validate_universal_instantiation(step)
        
        if step.rule == ProofRule.CONJUNCTION_INTRO:
            return self._validate_conjunction_introduction(step)
        
        if step.rule == ProofRule.CONJUNCTION_ELIM:
            return self._validate_conjunction_elimination(step)
        
        # Altri rules...
        return True
    
    def _validate_modus_ponens(self, step: ProofStep) -> bool:
        """Valida modus ponens."""
        from .formula import Implication
        
        if len(step.premises) != 2:
            return False
        
        impl_step, ant_step = step.premises
        
        if not isinstance(impl_step.formula, Implication):
            return False
        
        if impl_step.formula.antecedent != ant_step.formula:
            return False
        
        if step.formula != impl_step.formula.consequent:
            return False
        
        return True
    
    def _validate_universal_instantiation(self, step: ProofStep) -> bool:
        """Valida istanziazione universale."""
        from .formula import Universal
        
        if len(step.premises) != 1:
            return False
        
        universal_step = step.premises[0]
        
        if not isinstance(universal_step.formula, Universal):
            return False
        
        # Verifica sostituzione
        if not step.substitution:
            return False
        
        universal_formula = universal_step.formula
        expected = universal_formula.formula.substitute(step.substitution)
        
        return step.formula == expected
    
    def _validate_conjunction_introduction(self, step: ProofStep) -> bool:
        """Valida introduzione congiunzione."""
        from .formula import Conjunction
        
        if len(step.premises) != 2:
            return False
        
        if not isinstance(step.formula, Conjunction):
            return False
        
        left_step, right_step = step.premises
        conjunction = step.formula
        
        return (conjunction.left == left_step.formula and 
                conjunction.right == right_step.formula)
    
    def _validate_conjunction_elimination(self, step: ProofStep) -> bool:
        """Valida eliminazione congiunzione."""
        from .formula import Conjunction
        
        if len(step.premises) != 1:
            return False
        
        conjunction_step = step.premises[0]
        
        if not isinstance(conjunction_step.formula, Conjunction):
            return False
        
        conjunction = conjunction_step.formula
        
        return (step.formula == conjunction.left or 
                step.formula == conjunction.right)
    
    def get_final_step(self) -> Optional[ProofStep]:
        """Ottiene step finale (conclusione)."""
        if not self.steps:
            return None
        return self.steps[-1]
    
    def to_natural_language(self) -> str:
        """Converte proof tree in spiegazione naturale."""
        if not self.steps:
            return "Empty proof"
        
        lines = [f"Proof {self.proof_id}:"]
        lines.append("=" * 40)
        
        for i, step in enumerate(self.steps, 1):
            lines.append(f"{i}. {step.justification}")
        
        lines.append("=" * 40)
        final_step = self.get_final_step()
        if final_step:
            lines.append(f"Therefore: {final_step.formula}")
        
        return "\n".join(lines)
    
    def to_dict(self) -> Dict[str, Any]:
        """Serializza proof tree."""
        return {
            'proof_id': self.proof_id,
            'conclusion': self.conclusion.to_dict() if self.conclusion else None,
            'premises': [p.to_dict() for p in self.premises],
            'steps': [s.to_dict() for s in self.steps],
            'is_valid': self.is_valid,
            'depth': self.depth,
            'created_at': self.created_at
        }
