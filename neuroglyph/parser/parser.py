"""
NEUROGLYPH Native Parser - Parser simbolico per codice NEUROGLYPH.

Implementa parsing completo da codice simbolico NEUROGLYPH a AST nativo
usando grammatica Lark e trasformatori custom.
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from lark import Lark, Transformer, Tree, Token
from lark.exceptions import LarkError

from ..ast.nodes import (
    NGASTNode, QuantifiedExpression, LogicalExpression, MathematicalExpression,
    SetExpression, MetaExpression, InferenceExpression, FunctionExpression,
    Variable, Literal, create_variable, create_literal, create_logical_expr
)


class NGParseError(Exception):
    """Errore di parsing NEUROGLYPH."""
    pass


class NGASTTransformer(Transformer):
    """
    Trasforma parse tree Lark in NGAST.
    
    Ogni metodo corrisponde a una regola della grammatica e restituisce
    il nodo AST appropriato.
    """
    
    def __init__(self):
        super().__init__()
        self.symbol_registry = self._load_symbol_registry()
    
    def _load_symbol_registry(self) -> Dict[str, Any]:
        """Carica registry simboli per validazione."""
        try:
            from ...utils.registry_loader import load_registry_fast
            return load_registry_fast()
        except ImportError:
            # Fallback per testing
            return {}
    
    # Espressioni principali
    def expression(self, items):
        """Espressione generica - passa attraverso il primo item."""
        return items[0] if items else None
    
    def logical_expr(self, items):
        """P ⇒ Q, P ∧ Q, ¬P"""
        if len(items) == 2:  # Unary: ¬P
            op, expr = items
            return LogicalExpression(operator=str(op), left=None, right=expr)
        elif len(items) == 3:  # Binary: P ⇒ Q
            left, op, right = items
            return LogicalExpression(operator=str(op), left=left, right=right)
        else:
            # Parentesi: (P)
            return items[0]
    
    def mathematical_expr(self, items):
        """Operatori matematici: ∫ f(x), ∑ aᵢ"""
        if len(items) == 2:  # MATH_OP expression
            op, expr = items
            return MathematicalExpression(operator=str(op), expression=expr)
        else:
            return items[0]
    
    def set_expr(self, items):
        """x ∈ A, A ⊂ B, A ∩ B"""
        if len(items) == 1:  # EMPTY_SET
            return SetExpression(operator="empty")
        elif len(items) == 3:  # left op right
            left, op, right = items
            return SetExpression(operator=str(op), left=left, right=right)

    def meta_expr(self, items):
        """🧠 P, ⚡ func"""
        if len(items) == 2:
            symbol, content = items
            return MetaExpression(meta_symbol=str(symbol), content=content)

    def function_expr(self, items):
        """f(x), g(x,y)"""
        name = str(items[0])
        # Estrai argomenti tra parentesi
        args = []
        if len(items) > 2:  # Ha argomenti
            for i in range(2, len(items)):
                if str(items[i]) != "," and str(items[i]) != "(" and str(items[i]) != ")":
                    args.append(items[i])
        return FunctionExpression(name=name, arguments=args)
    
    # Atomi
    def atom(self, items):
        """Delega a sottotipi."""
        return items[0]

    # Token handlers per grammatica semplificata
    def IDENTIFIER(self, token):
        """Identificatore: x, y, func_name"""
        return create_variable(str(token))

    def NUMBER(self, token):
        """Numero: 42, 3.14"""
        value_str = str(token)
        if '.' in value_str or 'e' in value_str.lower():
            return create_literal(float(value_str))
        else:
            return create_literal(int(value_str))

    def STRING(self, token):
        """Stringa: "hello" """
        value = str(token)[1:-1]  # Rimuovi quote
        return create_literal(value)

    def SYMBOL(self, token):
        """Simbolo speciale."""
        return create_literal(str(token), literal_type="special")

    def EMPTY_SET(self, token):
        """Insieme vuoto: ∅"""
        return SetExpression(operator="empty")



class NGParser:
    """
    Parser simbolico NEUROGLYPH nativo.
    
    Converte codice simbolico NEUROGLYPH in AST nativo usando grammatica Lark.
    """
    
    def __init__(self, grammar_path: Optional[str] = None):
        """
        Inizializza parser con grammatica.

        Args:
            grammar_path: Percorso al file grammatica (default: auto-detect)
        """
        if grammar_path is None:
            # Auto-detect grammar path - usa versione semplificata per ora
            current_dir = Path(__file__).parent
            grammar_path = current_dir / "grammar_simple.lark"

        self.grammar_path = Path(grammar_path)
        self.transformer = NGASTTransformer()
        self.parser = self._create_parser()

        # Statistiche parsing
        self.parse_count = 0
        self.error_count = 0
        self.last_error = None
    
    def _create_parser(self) -> Lark:
        """Crea parser Lark dalla grammatica."""
        if not self.grammar_path.exists():
            raise NGParseError(f"Grammar file not found: {self.grammar_path}")
        
        try:
            with open(self.grammar_path, 'r', encoding='utf-8') as f:
                grammar = f.read()
            
            return Lark(
                grammar,
                parser='lalr',  # Parser più veloce e compatibile con transformer
                debug=False
            )
        except Exception as e:
            raise NGParseError(f"Failed to create parser: {e}")
    
    def parse(self, neuroglyph_code: str) -> NGASTNode:
        """
        Parse NEUROGLYPH code in AST simbolico.
        
        Args:
            neuroglyph_code: Codice simbolico NEUROGLYPH
            
        Returns:
            Root node dell'AST simbolico
            
        Raises:
            NGParseError: Se il parsing fallisce
        """
        self.parse_count += 1
        
        try:
            # Preprocessing del codice
            cleaned_code = self._preprocess_code(neuroglyph_code)

            # Parsing con Lark
            parse_tree = self.parser.parse(cleaned_code)

            # Trasforma parse tree in AST
            ast_tree = self.transformer.transform(parse_tree)

            # Estrai AST dal Tree wrapper
            if hasattr(ast_tree, 'children') and ast_tree.children:
                ast_root = ast_tree.children[0]
            else:
                ast_root = ast_tree

            # Post-processing dell'AST
            processed_ast = self._postprocess_ast(ast_root)

            return processed_ast
            
        except LarkError as e:
            self.error_count += 1
            self.last_error = str(e)
            raise NGParseError(f"Parse error: {e}")
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            raise NGParseError(f"Unexpected error: {e}")
    
    def _preprocess_code(self, code: str) -> str:
        """
        Preprocessing del codice prima del parsing.
        
        - Normalizza whitespace
        - Gestisce caratteri Unicode
        - Rimuove commenti
        """
        # Normalizza whitespace
        code = ' '.join(code.split())
        
        # Rimuovi commenti
        lines = code.split('\n')
        cleaned_lines = []
        for line in lines:
            if '//' in line:
                line = line[:line.index('//')]
            cleaned_lines.append(line.strip())
        
        return ' '.join(cleaned_lines).strip()
    
    def _postprocess_ast(self, ast: NGASTNode) -> NGASTNode:
        """
        Post-processing dell'AST dopo parsing.
        
        - Validazione semantica
        - Ottimizzazioni
        - Annotazioni metadata
        """
        # Per ora passa attraverso, ma qui si possono aggiungere:
        # - Validazione simboli nel registry
        # - Inferenza tipi
        # - Ottimizzazioni AST
        return ast
    
    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche di parsing."""
        return {
            'parse_count': self.parse_count,
            'error_count': self.error_count,
            'success_rate': (self.parse_count - self.error_count) / max(1, self.parse_count),
            'last_error': self.last_error
        }
    
    def reset_stats(self):
        """Reset statistiche."""
        self.parse_count = 0
        self.error_count = 0
        self.last_error = None


# Factory function
def create_parser(grammar_path: Optional[str] = None) -> NGParser:
    """Factory per creare parser NEUROGLYPH."""
    return NGParser(grammar_path)
