#!/usr/bin/env python3
"""
Test per NEUROGLYPH Conceptual Parser.

Valida parsing semantico da token concettuali a AST concettuale
con pattern matching e costruzione di concetti logici.
"""

import pytest
import sys
from pathlib import Path
from typing import List

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.conceptual_parser import create_conceptual_parser, SemanticParseError
from neuroglyph.conceptual.ast.conceptual_ast import (
    ConceptualAST, UniversalQuantification, ExistentialQuantification,
    MaterialImplication, LogicalConjunction, LogicalDisjunction, 
    LogicalNegation, Variable, Literal
)


class TestConceptualParser:
    """Test suite per parser concettuale."""
    
    def setup_method(self):
        """Setup per ogni test."""
        self.tokenizer = create_conceptual_tokenizer()
        self.parser = create_conceptual_parser(self.tokenizer)
    
    def test_simple_variable_parsing(self):
        """Test parsing variabili semplici."""
        test_cases = [
            ("P", Variable, "P"),
            ("x", Variable, "x"),
            ("variable_name", Variable, "variable_name"),
        ]
        
        for code, expected_type, expected_name in test_cases:
            ast = self.parser.parse(code)
            
            assert isinstance(ast, ConceptualAST)
            assert isinstance(ast.root_concept, expected_type)
            assert ast.root_concept.name == expected_name
    
    def test_literal_parsing(self):
        """Test parsing letterali."""
        test_cases = [
            ("42", Literal, 42, "int"),
            ("3.14", Literal, 3.14, "float"),
        ]
        
        for code, expected_type, expected_value, expected_literal_type in test_cases:
            ast = self.parser.parse(code)
            
            assert isinstance(ast, ConceptualAST)
            assert isinstance(ast.root_concept, expected_type)
            assert ast.root_concept.value == expected_value
            assert ast.root_concept.literal_type == expected_literal_type
    
    def test_logical_implication_parsing(self):
        """Test parsing implicazione materiale."""
        ast = self.parser.parse("P ⇒ Q")
        
        assert isinstance(ast, ConceptualAST)
        assert isinstance(ast.root_concept, MaterialImplication)
        
        implication = ast.root_concept
        assert isinstance(implication.premise, Variable)
        assert implication.premise.name == "P"
        assert isinstance(implication.conclusion, Variable)
        assert implication.conclusion.name == "Q"
        
        # Test semantica
        assert implication.semantic_meaning() == "If variable 'P' then variable 'Q'"
        assert implication.logical_strength() == 0.8
        assert implication.to_neuroglyph() == "P ⇒ Q"
    
    def test_logical_conjunction_parsing(self):
        """Test parsing congiunzione logica."""
        ast = self.parser.parse("P ∧ Q")
        
        assert isinstance(ast, ConceptualAST)
        assert isinstance(ast.root_concept, LogicalConjunction)
        
        conjunction = ast.root_concept
        assert isinstance(conjunction.left, Variable)
        assert conjunction.left.name == "P"
        assert isinstance(conjunction.right, Variable)
        assert conjunction.right.name == "Q"
        
        # Test semantica
        assert conjunction.semantic_meaning() == "variable 'P' and variable 'Q'"
        assert conjunction.logical_strength() == 1.0
        assert conjunction.to_neuroglyph() == "P ∧ Q"
    
    def test_logical_disjunction_parsing(self):
        """Test parsing disgiunzione logica."""
        ast = self.parser.parse("P ∨ Q")
        
        assert isinstance(ast, ConceptualAST)
        assert isinstance(ast.root_concept, LogicalDisjunction)
        
        disjunction = ast.root_concept
        assert isinstance(disjunction.left, Variable)
        assert disjunction.left.name == "P"
        assert isinstance(disjunction.right, Variable)
        assert disjunction.right.name == "Q"
        
        # Test semantica
        assert disjunction.semantic_meaning() == "variable 'P' or variable 'Q'"
        assert disjunction.logical_strength() == 0.6
        assert disjunction.to_neuroglyph() == "P ∨ Q"
    
    def test_logical_negation_parsing(self):
        """Test parsing negazione logica."""
        ast = self.parser.parse("¬P")
        
        assert isinstance(ast, ConceptualAST)
        assert isinstance(ast.root_concept, LogicalNegation)
        
        negation = ast.root_concept
        assert isinstance(negation.operand, Variable)
        assert negation.operand.name == "P"
        
        # Test semantica
        assert negation.semantic_meaning() == "not variable 'P'"
        assert negation.logical_strength() == 1.0
        assert negation.to_neuroglyph() == "¬P"
    
    def test_universal_quantification_parsing(self):
        """Test parsing quantificazione universale."""
        ast = self.parser.parse("∀x: P")
        
        assert isinstance(ast, ConceptualAST)
        assert isinstance(ast.root_concept, UniversalQuantification)
        
        quantification = ast.root_concept
        assert quantification.variable == "x"
        assert quantification.domain is None
        assert isinstance(quantification.body, Variable)
        assert quantification.body.name == "P"
        
        # Test semantica
        assert "For all x" in quantification.semantic_meaning()
        assert quantification.logical_strength() == 1.0
        assert quantification.to_neuroglyph() == "∀x: P"
    
    def test_existential_quantification_parsing(self):
        """Test parsing quantificazione esistenziale."""
        ast = self.parser.parse("∃y: Q")
        
        assert isinstance(ast, ConceptualAST)
        assert isinstance(ast.root_concept, ExistentialQuantification)
        
        quantification = ast.root_concept
        assert quantification.variable == "y"
        assert quantification.domain is None
        assert isinstance(quantification.body, Variable)
        assert quantification.body.name == "Q"
        
        # Test semantica
        assert "There exists y" in quantification.semantic_meaning()
        assert quantification.logical_strength() == 0.7
        assert quantification.to_neuroglyph() == "∃y: Q"
    
    def test_semantic_equivalence(self):
        """Test equivalenza semantica."""
        # Congiunzione è commutativa
        ast1 = self.parser.parse("P ∧ Q")
        ast2 = self.parser.parse("Q ∧ P")
        
        # Per ora il parser non supporta commutatività automatica
        # ma i concetti dovrebbero essere equivalenti se implementato
        assert isinstance(ast1.root_concept, LogicalConjunction)
        assert isinstance(ast2.root_concept, LogicalConjunction)
    
    def test_roundtrip_fidelity(self):
        """Test fedeltà roundtrip AST → NEUROGLYPH → AST."""
        test_cases = [
            "P",
            "P ⇒ Q", 
            "P ∧ Q",
            "P ∨ Q",
            "¬P",
            "∀x: P",
            "∃y: Q",
        ]
        
        for original_code in test_cases:
            # Parse → AST
            ast1 = self.parser.parse(original_code)
            
            # AST → NEUROGLYPH
            reconstructed_code = ast1.to_neuroglyph()
            
            # Re-parse → AST
            ast2 = self.parser.parse(reconstructed_code)
            
            # Verifica equivalenza
            assert ast1.root_concept.to_neuroglyph() == ast2.root_concept.to_neuroglyph()
            assert type(ast1.root_concept) == type(ast2.root_concept)
    
    def test_ast_metadata(self):
        """Test metadati AST."""
        ast = self.parser.parse("P ⇒ Q")
        
        assert ast.concept_metadata is not None
        assert ast.concept_metadata.complexity_score >= 0.0
        assert ast.concept_metadata.semantic_depth >= 0
        assert ast.concept_metadata.concept_count > 0
        
        assert ast.logical_structure is not None
        assert ast.logical_structure.depth >= 0
    
    def test_logical_strength_calculation(self):
        """Test calcolo forza logica."""
        test_cases = [
            ("P ∧ Q", 1.0),      # Congiunzione: forza massima
            ("P ⇒ Q", 0.8),      # Implicazione: forza alta
            ("P ∨ Q", 0.6),      # Disgiunzione: forza media
            ("¬P", 1.0),         # Negazione: forza massima
            ("∀x: P", 1.0),      # Quantificazione universale: forza massima
            ("∃y: Q", 0.7),      # Quantificazione esistenziale: forza alta
        ]
        
        for code, expected_strength in test_cases:
            ast = self.parser.parse(code)
            actual_strength = ast.root_concept.logical_strength()
            assert actual_strength == expected_strength, f"'{code}' should have strength {expected_strength}"
    
    def test_parse_statistics(self):
        """Test statistiche di parsing."""
        self.parser.reset_stats()
        
        # Parse multiple expressions
        expressions = ["P", "P ⇒ Q", "P ∧ Q", "¬P"]
        for expr in expressions:
            self.parser.parse(expr)
        
        stats = self.parser.get_parse_stats()
        
        assert stats['total_parses'] == len(expressions)
        assert stats['successful_parses'] == len(expressions)
        assert stats['success_rate'] == 1.0
        assert stats['semantic_errors'] == 0
        assert len(stats['pattern_matches']) > 0
    
    def test_error_handling(self):
        """Test gestione errori."""
        # Espressioni che dovrebbero fallire
        invalid_expressions = [
            "",           # Vuoto
            "⇒",          # Operatore senza operandi
            "P ⇒",        # Implicazione incompleta
            "∧ Q",        # Congiunzione senza primo operando
        ]
        
        for expr in invalid_expressions:
            with pytest.raises(SemanticParseError):
                self.parser.parse(expr)


def test_parser_factory():
    """Test factory function."""
    tokenizer = create_conceptual_tokenizer()
    parser = create_conceptual_parser(tokenizer)
    
    assert parser is not None
    assert parser.tokenizer == tokenizer
    
    # Test parsing base
    ast = parser.parse("P ⇒ Q")
    assert isinstance(ast, ConceptualAST)
    assert isinstance(ast.root_concept, MaterialImplication)


if __name__ == "__main__":
    # Test standalone
    print("🧪 Testing NEUROGLYPH Conceptual Parser...")
    
    tokenizer = create_conceptual_tokenizer()
    parser = create_conceptual_parser(tokenizer)
    
    test_cases = [
        "P",
        "P ⇒ Q",
        "P ∧ Q",
        "P ∨ Q", 
        "¬P",
        "∀x: P",
        "∃y: Q",
    ]
    
    for case in test_cases:
        print(f"\n🔍 Parsing: '{case}'")
        try:
            ast = parser.parse(case)
            concept = ast.root_concept
            
            print(f"  Type: {type(concept).__name__}")
            print(f"  Semantic: {concept.semantic_meaning()}")
            print(f"  Strength: {concept.logical_strength()}")
            print(f"  Roundtrip: {concept.to_neuroglyph()}")
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    # Statistiche finali
    stats = parser.get_parse_stats()
    print(f"\n📊 Parse Statistics:")
    print(f"  Success rate: {stats['success_rate']:.1%}")
    print(f"  Pattern matches: {stats['pattern_matches']}")
    
    print("\n✅ All tests completed!")
