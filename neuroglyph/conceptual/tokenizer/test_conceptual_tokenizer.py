#!/usr/bin/env python3
"""
Test per NEUROGLYPH Conceptual Tokenizer.

Valida tokenizzazione atomica 1:1 simbolo→concetto con zero-splitting garantito.
"""

import pytest
import sys
from pathlib import Path
from typing import List

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import (
    NGConceptualTokenizer, ConceptRegistry, ConceptToken, ConceptType,
    create_conceptual_tokenizer
)


class TestConceptualTokenizer:
    """Test suite per tokenizer concettuale."""
    
    def setup_method(self):
        """Setup per ogni test."""
        self.tokenizer = create_conceptual_tokenizer()
        self.registry = self.tokenizer.registry
    
    def test_atomic_symbol_tokenization(self):
        """Test tokenizzazione simboli atomici."""
        test_cases = [
            ("∀", 1, "universal_quantification"),
            ("∃", 1, "existential_quantification"),
            ("⇒", 1, "material_implication"),
            ("∧", 1, "logical_conjunction"),
            ("∨", 1, "logical_disjunction"),
            ("¬", 1, "logical_negation"),
            ("⊢", 1, "syntactic_entailment"),
            ("∈", 1, "set_membership"),
            ("∅", 1, "empty_set"),
            ("∫", 1, "definite_integral"),
            ("🧠", 1, "reasoning_process"),
        ]
        
        for symbol, expected_count, expected_concept in test_cases:
            tokens = self.tokenizer.tokenize(symbol)
            
            assert len(tokens) == expected_count, f"Symbol '{symbol}' should produce exactly {expected_count} token"
            assert tokens[0].symbol == symbol, f"Token symbol should be '{symbol}'"
            assert tokens[0].concept_name == expected_concept, f"Concept should be '{expected_concept}'"
            assert tokens[0].semantic_type != ConceptType.LITERAL, f"'{symbol}' should not be literal"
    
    def test_zero_splitting_guarantee(self):
        """Test garanzia zero-splitting."""
        # Simboli che NON devono essere splittati
        atomic_symbols = ["∀", "∃", "⇒", "⇔", "∧", "∨", "¬", "⊢", "⊨", "∈", "∉", "⊂", "∅", "∫", "∑", "∂", "🧠", "⚡"]
        
        for symbol in atomic_symbols:
            tokens = self.tokenizer.tokenize(symbol)
            
            # Deve produrre esattamente 1 token
            assert len(tokens) == 1, f"Symbol '{symbol}' was split into {len(tokens)} tokens (should be 1)"
            
            # Il token deve contenere l'intero simbolo
            assert tokens[0].symbol == symbol, f"Token symbol '{tokens[0].symbol}' != original '{symbol}'"
            assert tokens[0].length == len(symbol), f"Token length {tokens[0].length} != symbol length {len(symbol)}"
    
    def test_multi_character_variables(self):
        """Test tokenizzazione variabili multi-carattere."""
        test_cases = [
            ("variable_name", ["variable_name"]),
            ("func_name(x)", ["func_name", "(", "x", ")"]),
            ("P_1 ⇒ Q_2", ["P_1", "⇒", "Q_2"]),
            ("long_identifier_123", ["long_identifier_123"]),
            ("_private_var", ["_private_var"]),
        ]

        for expression, expected_symbols in test_cases:
            tokens = self.tokenizer.tokenize(expression)
            actual_symbols = [t.symbol for t in tokens]

            assert actual_symbols == expected_symbols, f"Multi-char expression '{expression}' tokenized incorrectly"

            # Verifica che le variabili siano classificate correttamente
            for token in tokens:
                if token.symbol in expected_symbols and token.symbol.replace('_', '').replace('(', '').replace(')', '').isalnum():
                    if token.symbol not in ['(', ')'] and not token.symbol.isdigit():
                        assert token.semantic_type == ConceptType.VARIABLE, f"'{token.symbol}' should be VARIABLE"

    def test_number_tokenization(self):
        """Test tokenizzazione numeri multi-carattere."""
        test_cases = [
            ("42", ["42"]),
            ("3.14", ["3.14"]),
            ("1.5e-10", ["1.5e-10"]),
            ("2E+5", ["2E+5"]),
            ("0.001", ["0.001"]),
        ]

        for expression, expected_symbols in test_cases:
            tokens = self.tokenizer.tokenize(expression)
            actual_symbols = [t.symbol for t in tokens]

            assert actual_symbols == expected_symbols, f"Number expression '{expression}' tokenized incorrectly"

            # Verifica che i numeri siano classificati correttamente
            for token in tokens:
                if token.symbol in expected_symbols:
                    assert token.semantic_type == ConceptType.LITERAL, f"'{token.symbol}' should be LITERAL"
                    assert "number" in token.concept_name, f"'{token.symbol}' should have 'number' in concept_name"

    def test_complex_expression_tokenization(self):
        """Test tokenizzazione espressioni complesse."""
        test_cases = [
            ("P ⇒ Q", ["P", "⇒", "Q"]),
            ("∀x: P(x)", ["∀", "x", ":", "P", "(", "x", ")"]),
            ("P ∧ Q ∨ R", ["P", "∧", "Q", "∨", "R"]),
            ("x ∈ A", ["x", "∈", "A"]),
            ("🧠 P ⊢ Q", ["🧠", "P", "⊢", "Q"]),
            ("∫ f(x) dx", ["∫", "f", "(", "x", ")", "dx"]),  # dx come identificatore
            ("func_name(var_1, var_2)", ["func_name", "(", "var_1", ",", "var_2", ")"]),
        ]

        for expression, expected_symbols in test_cases:
            tokens = self.tokenizer.tokenize(expression)
            actual_symbols = [t.symbol for t in tokens]

            assert actual_symbols == expected_symbols, f"Expression '{expression}' tokenized incorrectly"
    
    def test_concept_id_mapping(self):
        """Test mappatura corretta concept ID."""
        test_cases = [
            ("∀", 1001),
            ("∃", 1002),
            ("⇒", 1101),
            ("∧", 1103),
            ("⊢", 1201),
            ("∈", 1301),
            ("∫", 1401),
            ("🧠", 1501),
        ]
        
        for symbol, expected_id in test_cases:
            tokens = self.tokenizer.tokenize(symbol)
            assert len(tokens) == 1
            assert tokens[0].concept_id == expected_id, f"Symbol '{symbol}' should have concept_id {expected_id}"
    
    def test_semantic_type_classification(self):
        """Test classificazione tipi semantici."""
        test_cases = [
            ("∀", ConceptType.QUANTIFIER),
            ("⇒", ConceptType.LOGICAL_CONNECTIVE),
            ("⊢", ConceptType.INFERENCE),
            ("∈", ConceptType.SET_THEORY),
            ("∫", ConceptType.MATHEMATICS),
            ("🧠", ConceptType.META_CONCEPT),
            ("x", ConceptType.VARIABLE),
            ("42", ConceptType.LITERAL),
        ]
        
        for symbol, expected_type in test_cases:
            tokens = self.tokenizer.tokenize(symbol)
            assert len(tokens) == 1
            assert tokens[0].semantic_type == expected_type, f"Symbol '{symbol}' should have type {expected_type}"
    
    def test_logical_strength_assignment(self):
        """Test assegnazione forza logica."""
        test_cases = [
            ("∀", 1.0),  # Quantificazione universale: forza massima
            ("⇒", 0.8),  # Implicazione: forza alta
            ("∧", 1.0),  # Congiunzione: forza massima
            ("🧠", 0.9),  # Reasoning: forza alta ma non massima
        ]
        
        for symbol, expected_strength in test_cases:
            tokens = self.tokenizer.tokenize(symbol)
            assert len(tokens) == 1
            assert tokens[0].logical_strength == expected_strength, f"Symbol '{symbol}' should have strength {expected_strength}"
    
    def test_position_tracking(self):
        """Test tracciamento posizioni."""
        expression = "P ⇒ Q"
        tokens = self.tokenizer.tokenize(expression)
        
        expected_positions = [0, 2, 4]  # P at 0, ⇒ at 2, Q at 4
        actual_positions = [t.position for t in tokens]
        
        assert actual_positions == expected_positions, f"Position tracking incorrect: {actual_positions}"
    
    def test_unknown_symbol_handling(self):
        """Test gestione simboli sconosciuti."""
        # Reset stats
        self.tokenizer.reset_stats()
        
        # Tokenizza espressione con simbolo sconosciuto
        tokens = self.tokenizer.tokenize("P ⇒ ★ Q")  # ★ non è nel registry
        
        # Dovrebbe comunque produrre token
        assert len(tokens) == 4  # P, ⇒, ★, Q
        
        # Verifica statistiche
        stats = self.tokenizer.get_tokenization_stats()
        assert stats['unknown_symbols'] == 1
        assert stats['total_tokens'] == 4
    
    def test_tokenization_statistics(self):
        """Test statistiche di tokenizzazione."""
        self.tokenizer.reset_stats()
        
        # Tokenizza espressione mista
        tokens = self.tokenizer.tokenize("∀x ∈ A: P(x) ⇒ Q(x)")
        
        stats = self.tokenizer.get_tokenization_stats()
        
        # Verifica statistiche
        assert stats['total_tokens'] > 0
        assert stats['concept_tokens'] > 0
        assert 0.0 <= stats['zero_splitting_rate'] <= 1.0
        
        # Verifica che simboli concettuali siano riconosciuti
        concept_symbols = ["∀", "∈", "⇒"]
        concept_tokens = [t for t in tokens if t.symbol in concept_symbols]
        assert len(concept_tokens) == len(concept_symbols)
    
    def test_registry_completeness(self):
        """Test completezza registry."""
        # Simboli critici che devono essere nel registry
        critical_symbols = [
            # Quantificatori
            "∀", "∃",
            # Logica
            "⇒", "⇔", "∧", "∨", "¬", "≡",
            # Inferenza
            "⊢", "⊨", "⊭", "⊤", "⊥",
            # Set theory
            "∈", "∉", "⊂", "⊆", "⊇", "∩", "∪", "∅",
            # Matematica
            "∫", "∑", "∏", "∂", "∇", "√", "∞",
            # Meta-concetti
            "🧠", "⚡", "🔄", "🏛", "📖", "🔮"
        ]
        
        for symbol in critical_symbols:
            assert self.registry.has_symbol(symbol), f"Critical symbol '{symbol}' missing from registry"
            
            concept = self.registry.get_concept(symbol)
            assert concept is not None, f"Concept definition missing for '{symbol}'"
            assert concept.concept_id > 0, f"Invalid concept_id for '{symbol}'"
    
    def test_roundtrip_fidelity(self):
        """Test fedeltà roundtrip simboli."""
        test_expressions = [
            "∀x ∈ ℝ: x² ≥ 0",
            "P ⇒ Q ∧ R",
            "🧠 (P ⊢ Q) ⇒ (¬Q ⊢ ¬P)",
            "∫₀¹ f(x) dx = F(1) - F(0)",
            "{x ∈ A | P(x)} ⊆ A"
        ]
        
        for expression in test_expressions:
            # Tokenizza
            tokens = self.tokenizer.tokenize(expression)
            
            # Ricostruisci stringa
            reconstructed = "".join(t.symbol for t in tokens)
            
            # Rimuovi spazi per confronto
            original_no_spaces = expression.replace(" ", "")
            reconstructed_no_spaces = reconstructed.replace(" ", "")
            
            # Verifica che i simboli siano preservati
            # (gli spazi potrebbero essere gestiti diversamente)
            for char in original_no_spaces:
                if not char.isspace():
                    assert char in reconstructed_no_spaces, f"Symbol '{char}' lost in roundtrip"


def test_concept_registry_operations():
    """Test operazioni registry concettuale."""
    registry = ConceptRegistry()
    
    # Test simboli base
    assert registry.has_symbol("∀")
    assert registry.has_symbol("⇒")
    assert registry.has_symbol("🧠")
    
    # Test retrieval
    concept = registry.get_concept("∀")
    assert concept is not None
    assert concept.concept_id == 1001
    assert concept.semantic_type == ConceptType.QUANTIFIER
    
    # Test get by ID
    concept_by_id = registry.get_concept_by_id(1001)
    assert concept_by_id == concept


def test_tokenizer_factory():
    """Test factory function."""
    tokenizer = create_conceptual_tokenizer()
    assert isinstance(tokenizer, NGConceptualTokenizer)
    assert tokenizer.registry is not None
    
    # Test tokenizzazione base
    tokens = tokenizer.tokenize("P ⇒ Q")
    assert len(tokens) == 3
    assert tokens[1].concept_name == "material_implication"


if __name__ == "__main__":
    # Test standalone
    print("🧪 Testing NEUROGLYPH Conceptual Tokenizer...")
    
    tokenizer = create_conceptual_tokenizer()
    
    test_cases = [
        "∀x: P(x)",
        "P ⇒ Q",
        "🧠 reasoning",
        "x ∈ A ∩ B",
        "∫ f(x) dx"
    ]
    
    for case in test_cases:
        print(f"\n🔤 Tokenizing: '{case}'")
        tokens = tokenizer.tokenize(case)
        
        for token in tokens:
            print(f"  {token}")
        
        stats = tokenizer.get_tokenization_stats()
        print(f"  Zero-splitting rate: {stats['zero_splitting_rate']:.1%}")
        
        tokenizer.reset_stats()
    
    print("\n✅ All tests completed!")
