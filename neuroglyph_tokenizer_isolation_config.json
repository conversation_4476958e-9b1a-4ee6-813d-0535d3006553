{"model_type": "neuroglyph_isolated", "base_model": "Qwen/Qwen2.5-Coder-1.5B-Instruct", "special_tokens": {"<NG_UNIVERSAL_QUANTIFIER>": "∀", "<NG_EXISTENTIAL_QUANTIFIER>": "∃", "<NG_LOGICAL_AND>": "∧", "<NG_LOGICAL_OR>": "∨", "<NG_LOGICAL_NOT>": "¬", "<NG_MATERIAL_IMPLICATION>": "⇒", "<NG_LOGICAL_EQUIVALENCE>": "⇔", "<NG_NG_SYMBOL_8866>": "⊢", "<NG_NG_SYMBOL_8872>": "⊨", "<NG_NG_SYMBOL_8746>": "∪", "<NG_NG_SYMBOL_8745>": "∩", "<NG_SUBSET_RELATION>": "⊆", "<NG_NG_SYMBOL_8839>": "⊇", "<NG_SET_MEMBERSHIP>": "∈", "<NG_NG_SYMBOL_8713>": "∉", "<NG_NG_SYMBOL_8709>": "∅", "<NG_NG_SYMBOL_8834>": "⊂", "<NG_NG_SYMBOL_8835>": "⊃", "<NG_NG_SYMBOL_8800>": "≠", "<NG_NG_SYMBOL_8804>": "≤", "<NG_NG_SYMBOL_8805>": "≥", "<NG_NG_SYMBOL_177>": "±", "<NG_NG_SYMBOL_8734>": "∞", "<NG_NG_SYMBOL_8721>": "∑", "<NG_NG_SYMBOL_8719>": "∏", "<NG_NG_SYMBOL_178>": "²", "<NG_NG_SYMBOL_183>": "·", "<NG_NG_SYMBOL_8477>": "ℝ", "<NG_NG_SYMBOL_8469>": "ℕ", "<NG_NG_SYMBOL_8484>": "ℤ", "<NG_NG_SYMBOL_8474>": "ℚ", "<NG_NG_SYMBOL_8450>": "ℂ", "<NG_NG_SYMBOL_120121>": "𝔹", "<NG_NG_SYMBOL_8747>": "∫", "<NG_NG_SYMBOL_8706>": "∂", "<NG_NG_SYMBOL_7522>": "ᵢ", "<NG_NG_SYMBOL_966>": "φ", "<NG_NG_SYMBOL_968>": "ψ", "<NG_NG_SYMBOL_967>": "χ"}, "added_tokens": [{"content": "∀", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∃", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∧", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∨", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "¬", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "⇒", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "⇔", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "⊢", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "⊨", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∪", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∩", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "⊆", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "⊇", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∈", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∉", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∅", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "⊂", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "⊃", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "≠", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "≤", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "≥", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "±", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∞", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∑", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∏", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "²", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "·", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "ℝ", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "ℕ", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "ℤ", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "ℚ", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "ℂ", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "𝔹", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∫", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "∂", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "ᵢ", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "φ", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "ψ", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"content": "χ", "single_word": true, "lstrip": false, "rstrip": false, "normalized": false, "special": true}], "never_split": ["∀", "∃", "∧", "∨", "¬", "⇒", "⇔", "⊢", "⊨", "∪", "∩", "⊆", "⊇", "∈", "∉", "∅", "⊂", "⊃", "≠", "≤", "≥", "±", "∞", "∑", "∏", "²", "·", "ℝ", "ℕ", "ℤ", "ℚ", "ℂ", "𝔹", "∫", "∂", "ᵢ", "φ", "ψ", "χ"], "isolation_patterns": ["([∀∃∧∨¬⇒⇔⊢⊨∪∩⊆⊇∈∉∅⊂⊃≠≤≥±∞∑∏²·ℝℕℤℚℂ𝔹∫∂ᵢφψχ])", "((?:[∀∃][a-zA-Z]\\s*[∈∉]\\s*[ℝℕℤℚℂ𝔹])|(?:[∀∃][a-zA-Z]))", "([⇒⇔])", "(\\([^)]*[∀∃∧∨¬⇒⇔][^)]*\\))"], "pre_tokenizer": {"type": "custom_neuroglyph", "patterns": ["([∀∃∧∨¬⇒⇔⊢⊨∪∩⊆⊇∈∉∅⊂⊃≠≤≥±∞∑∏²·ℝℕℤℚℂ𝔹∫∂ᵢφψχ])", "((?:[∀∃][a-zA-Z]\\s*[∈∉]\\s*[ℝℕℤℚℂ𝔹])|(?:[∀∃][a-zA-Z]))", "([⇒⇔])", "(\\([^)]*[∀∃∧∨¬⇒⇔][^)]*\\))"], "preserve_symbols": true}, "post_processor": {"type": "neuroglyph_validator", "validate_atomicity": true, "validate_semantics": true}, "validation": {"required": true, "atomicity_threshold": 1.0, "roundtrip_threshold": 0.99}}