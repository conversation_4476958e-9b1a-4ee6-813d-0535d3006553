#!/usr/bin/env python3
"""
NEUROGLYPH - Restore Rigorous Standards

Ripristina standard rigorosi e verifica che solo pattern
semanticamente validi passino i test.

PRINCIPI IMMUTABILI NON NEGOZIABILI:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita (≥95%)
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

import sys
from pathlib import Path
from difflib import SequenceMatcher

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.conceptual_parser import create_conceptual_parser, SemanticParseError


class RigorousStandardsRestorer:
    """Ripristina standard rigorosi per NEUROGLYPH."""
    
    def __init__(self):
        self.tokenizer = create_conceptual_tokenizer()
        self.parser = create_conceptual_parser(self.tokenizer)
        self.min_fidelity = 0.95  # STANDARD IMMUTABILE
    
    def test_with_rigorous_standards(self):
        """Test con standard rigorosi - solo pattern semanticamente validi."""
        print("🎯 NEUROGLYPH RIGOROUS STANDARDS TEST")
        print("="*80)
        print(f"IMMUTABLE STANDARD: ≥{self.min_fidelity:.1%} fidelity")
        print("SEMANTIC VALIDITY: Only meaningful patterns allowed")
        print("ZERO TOLERANCE: No syntactic errors accepted")
        print("="*80)
        
        # SOLO pattern semanticamente validi e significativi
        valid_patterns = [
            # Quantificatori semanticamente corretti
            ("∀x: P(x)", "Universal Quantification"),
            ("∃x: P(x)", "Existential Quantification"),
            ("∀x ∈ ℝ: P(x)", "Universal with Domain"),
            ("∃x ∈ ℕ: P(x)", "Existential with Domain"),
            ("∀x ∃y: R(x,y)", "Nested Quantifiers"),
            
            # Logica semanticamente corretta
            ("P ⇒ Q", "Material Implication"),
            ("P ∧ Q", "Logical Conjunction"),
            ("P ∨ Q", "Logical Disjunction"),
            ("¬P", "Logical Negation"),
            ("¬(P ∨ Q)", "Negated Disjunction"),
            ("P ∧ Q ⇒ R", "Compound Implication"),
            
            # Set theory semanticamente corretta
            ("A ∪ B", "Set Union"),
            ("A ∩ B", "Set Intersection"),
            ("x ∈ A", "Set Membership"),
            ("A ⊆ B", "Subset Relation"),
            
            # Matematica semanticamente corretta
            ("x + y", "Addition"),
            ("f(x)", "Function Call"),
            ("x = y", "Equality"),
            ("x ≠ y", "Inequality"),
            ("x ≤ y", "Less Equal"),
            ("x ≥ y", "Greater Equal"),
            
            # Matematica avanzata semanticamente corretta
            ("∫ f(x) dx", "Integral"),
            ("∑ xᵢ", "Summation"),
            ("∏ xᵢ", "Product"),
            ("∂f/∂x", "Partial Derivative")
        ]
        
        # Pattern che DEVONO fallire (test di controllo)
        invalid_patterns = [
            ("BROKEN_SYNTAX", "Broken Syntax"),
            ("∀x: P(x) ∧", "Incomplete Expression"),
            ("P ⇒ Q ∨", "Malformed Logic"),
            ("∫ f(x)", "Incomplete Integral"),
            ("¬¬¬P", "Triple Negation"),
            ("∀∃x: P(x)", "Malformed Quantifier"),
            ("A ∪ ∩ B", "Double Operator"),
            ("", "Empty String"),
            ("   ", "Whitespace Only"),
        ]
        
        # Test pattern validi
        print("\n✅ TESTING VALID PATTERNS (must pass with ≥95% fidelity)")
        valid_passed = 0
        valid_results = []
        
        for pattern, name in valid_patterns:
            result = self._test_single_pattern(pattern, name, should_pass=True)
            valid_results.append(result)
            
            if result['meets_standard']:
                valid_passed += 1
                print(f"  ✅ {name}: {result['fidelity']:.1%}")
            else:
                print(f"  ❌ {name}: {result['fidelity']:.1%} (FAILED)")
                if result['error']:
                    print(f"      Error: {result['error']}")
        
        # Test pattern invalidi
        print(f"\n❌ TESTING INVALID PATTERNS (must fail)")
        invalid_failed = 0
        
        for pattern, name in invalid_patterns:
            result = self._test_single_pattern(pattern, name, should_pass=False)
            
            if not result['meets_standard']:
                invalid_failed += 1
                print(f"  ✅ {name}: CORRECTLY FAILED ({result['fidelity']:.1%})")
            else:
                print(f"  ❌ {name}: INCORRECTLY PASSED ({result['fidelity']:.1%})")
        
        # Calcola risultati finali
        valid_rate = valid_passed / len(valid_patterns)
        invalid_rate = invalid_failed / len(invalid_patterns)
        
        print(f"\n" + "="*80)
        print("🎯 RIGOROUS STANDARDS ASSESSMENT")
        print("="*80)
        
        print(f"📊 VALID PATTERNS: {valid_passed}/{len(valid_patterns)} ({valid_rate:.1%})")
        print(f"📊 INVALID PATTERNS: {invalid_failed}/{len(invalid_patterns)} correctly failed ({invalid_rate:.1%})")
        
        # Standard rigorosi
        valid_threshold = 0.92  # 23/25 = 92% minimum
        invalid_threshold = 0.90  # 90% degli invalidi devono fallire
        
        print(f"\n🎯 RIGOROUS THRESHOLDS:")
        print(f"  Valid patterns: ≥{valid_threshold:.1%} (achieved: {valid_rate:.1%})")
        print(f"  Invalid rejection: ≥{invalid_threshold:.1%} (achieved: {invalid_rate:.1%})")
        
        # Assessment finale
        meets_valid_standard = valid_rate >= valid_threshold
        meets_invalid_standard = invalid_rate >= invalid_threshold
        overall_rigorous = meets_valid_standard and meets_invalid_standard
        
        print(f"\n🏆 FINAL ASSESSMENT:")
        if overall_rigorous:
            print("✅ RIGOROUS STANDARDS: MAINTAINED")
            print("✅ SEMANTIC VALIDITY: PRESERVED")
            print("✅ ZERO TOLERANCE: ENFORCED")
            print("✅ READY FOR FINE-TUNING")
        else:
            print("❌ RIGOROUS STANDARDS: COMPROMISED")
            print("❌ SEMANTIC VALIDITY: LOST")
            print("❌ ZERO TOLERANCE: VIOLATED")
            print("❌ NOT READY FOR FINE-TUNING")
        
        # Dettagli per debugging
        if not meets_valid_standard:
            print(f"\n🔧 VALID PATTERNS ISSUES:")
            for result in valid_results:
                if not result['meets_standard']:
                    print(f"  • {result['name']}: {result['fidelity']:.1%}")
        
        print("\n" + "="*80)
        
        return overall_rigorous, valid_rate, invalid_rate
    
    def _test_single_pattern(self, pattern: str, name: str, should_pass: bool) -> dict:
        """Test rigoroso di singolo pattern."""
        result = {
            'name': name,
            'pattern': pattern,
            'fidelity': 0.0,
            'meets_standard': False,
            'error': None,
            'should_pass': should_pass
        }
        
        try:
            # Test parsing
            ast = self.parser.parse(pattern)
            
            if ast and ast.root_concept:
                # Test serialization
                try:
                    output = ast.to_neuroglyph()
                    fidelity = self._calculate_fidelity(pattern, output)
                    result['fidelity'] = fidelity
                    result['meets_standard'] = fidelity >= self.min_fidelity
                    
                except Exception as e:
                    result['error'] = f"Serialization failed: {e}"
            else:
                result['error'] = "Parsing failed - no AST"
        
        except SemanticParseError as e:
            result['error'] = f"Parse error: {e}"
        except Exception as e:
            result['error'] = f"Unexpected error: {e}"
        
        return result
    
    def _calculate_fidelity(self, original: str, reconstructed: str) -> float:
        """Calcolo fidelity IDENTICO - nessuna modifica."""
        # Exact match = 100% fidelity
        if original.strip() == reconstructed.strip():
            return 1.0
        
        # Normalized comparison
        orig_norm = self._normalize(original)
        recon_norm = self._normalize(reconstructed)
        
        if orig_norm == recon_norm:
            return 1.0
        
        # Sequence similarity
        similarity = SequenceMatcher(None, orig_norm, recon_norm).ratio()
        return similarity
    
    def _normalize(self, text: str) -> str:
        """Normalizzazione IDENTICA - nessuna modifica."""
        import re
        normalized = re.sub(r'\s+', ' ', text.strip())
        return normalized


def main():
    """Main entry point."""
    restorer = RigorousStandardsRestorer()
    
    print("🚨 RESTORING RIGOROUS STANDARDS")
    print("⚠️  ZERO TOLERANCE FOR SEMANTIC ERRORS")
    print("🎯 ONLY MEANINGFUL PATTERNS ALLOWED")
    
    rigorous, valid_rate, invalid_rate = restorer.test_with_rigorous_standards()
    
    if rigorous:
        print(f"\n🎉 SUCCESS: Rigorous standards maintained!")
        print(f"📊 Valid patterns: {valid_rate:.1%}")
        print(f"📊 Invalid rejection: {invalid_rate:.1%}")
        print(f"✅ Ready for certified fine-tuning")
        sys.exit(0)
    else:
        print(f"\n💥 FAILURE: Standards compromised!")
        print(f"📊 Valid patterns: {valid_rate:.1%}")
        print(f"📊 Invalid rejection: {invalid_rate:.1%}")
        print(f"❌ Must fix before fine-tuning")
        sys.exit(1)


if __name__ == "__main__":
    main()
