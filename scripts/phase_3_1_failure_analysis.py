#!/usr/bin/env python3
"""
NEUROGLYPH Phase 3.1 - Roundtrip Failure Analysis

Analizza e cataloga tutti i failure cases del roundtrip per identificare
pattern specifici e quantificare occorrenze nel dataset 20K.

Principi Immutabili:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail

Usage:
    python3 scripts/phase_3_1_failure_analysis.py
    python3 scripts/phase_3_1_failure_analysis.py --full-dataset
"""

import argparse
import json
import time
import re
from pathlib import Path
from typing import List, Dict, Any, Set, Tuple
from collections import defaultdict, Counter
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.conceptual_parser import create_conceptual_parser, SemanticParseError


class RoundtripFailureAnalyzer:
    """
    Analizzatore per failure cases del roundtrip NEUROGLYPH.
    
    Identifica, classifica e quantifica pattern di fallimento
    per guidare l'estensione del parser concettuale.
    """
    
    def __init__(self, dataset_path: str = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl"):
        self.dataset_path = Path(dataset_path)
        self.output_dir = Path("data/verification")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Inizializza sistema concettuale
        self.tokenizer = create_conceptual_tokenizer()
        self.parser = create_conceptual_parser(self.tokenizer)
        
        # Pattern di failure predefiniti
        self.failure_patterns = self._initialize_failure_patterns()
        
        # Statistiche analisi
        self.analysis_stats = {
            'total_examples': 0,
            'parsing_failures': 0,
            'roundtrip_failures': 0,
            'pattern_matches': defaultdict(int),
            'uncategorized_failures': [],
            'category_distribution': defaultdict(int)
        }
    
    def _initialize_failure_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Inizializza pattern di failure predefiniti."""
        return {
            # COMPREHENSIONS
            "list_comprehension": {
                "regex": r"\[.*for.*in.*\]",
                "description": "List comprehensions [expr for x in iterable]",
                "examples": ["[x*2 for x in range(3)]", "[x for x in lst if x > 0]"],
                "complexity": "medium",
                "ast_nodes": ["ListComp", "comprehension"]
            },
            "dict_comprehension": {
                "regex": r"\{.*:.*for.*in.*\}",
                "description": "Dict comprehensions {k:v for x in iterable}",
                "examples": ["{x: x*2 for x in range(3)}"],
                "complexity": "medium",
                "ast_nodes": ["DictComp"]
            },
            "set_comprehension": {
                "regex": r"\{.*for.*in.*\}",
                "description": "Set comprehensions {expr for x in iterable}",
                "examples": ["{x*2 for x in range(3)}"],
                "complexity": "medium",
                "ast_nodes": ["SetComp"]
            },
            "nested_comprehension": {
                "regex": r"\[.*\[.*for.*\].*for.*\]",
                "description": "Nested comprehensions",
                "examples": ["[[y for y in x] for x in matrix]"],
                "complexity": "high",
                "ast_nodes": ["ListComp", "nested"]
            },
            
            # DECORATORS
            "simple_decorator": {
                "regex": r"@\w+\s*\ndef",
                "description": "Simple decorators @decorator",
                "examples": ["@property\ndef func():"],
                "complexity": "low",
                "ast_nodes": ["FunctionDef", "decorator_list"]
            },
            "parametric_decorator": {
                "regex": r"@\w+\([^)]*\)\s*\ndef",
                "description": "Parametric decorators @decorator(args)",
                "examples": ["@retry(times=3)\ndef func():"],
                "complexity": "medium",
                "ast_nodes": ["FunctionDef", "Call"]
            },
            "multiple_decorators": {
                "regex": r"@\w+.*\n@\w+.*\ndef",
                "description": "Multiple decorators",
                "examples": ["@staticmethod\n@property\ndef func():"],
                "complexity": "medium",
                "ast_nodes": ["FunctionDef", "multiple_decorators"]
            },
            
            # TYPE ANNOTATIONS
            "function_annotation": {
                "regex": r"def\s+\w+\([^)]*:\s*\w+[^)]*\)\s*->\s*\w+:",
                "description": "Function type annotations",
                "examples": ["def func(x: int) -> str:"],
                "complexity": "medium",
                "ast_nodes": ["FunctionDef", "arg", "returns"]
            },
            "variable_annotation": {
                "regex": r"\w+\s*:\s*\w+\s*=",
                "description": "Variable type annotations",
                "examples": ["x: int = 5"],
                "complexity": "low",
                "ast_nodes": ["AnnAssign"]
            },
            "complex_annotation": {
                "regex": r":\s*(List|Dict|Tuple|Optional|Union)\[",
                "description": "Complex type annotations",
                "examples": ["x: List[Dict[str, int]]"],
                "complexity": "high",
                "ast_nodes": ["Subscript", "Name"]
            },
            
            # DEFAULT PARAMETERS
            "default_parameters": {
                "regex": r"def\s+\w+\([^)]*=.*\):",
                "description": "Default parameter values",
                "examples": ["def func(a=1, b=True):"],
                "complexity": "low",
                "ast_nodes": ["FunctionDef", "arg", "defaults"]
            },
            "complex_defaults": {
                "regex": r"def\s+\w+\([^)]*=\s*[\[\{].*[\]\}].*\):",
                "description": "Complex default values (lists, dicts)",
                "examples": ["def func(lst=[1,2,3]):"],
                "complexity": "medium",
                "ast_nodes": ["FunctionDef", "List", "Dict"]
            },
            
            # LITERALS
            "set_literal": {
                "regex": r"\{[^:}]*\}",
                "description": "Set literals {1, 2, 3}",
                "examples": ["{1, 2, 3}", "{x, y, z}"],
                "complexity": "low",
                "ast_nodes": ["Set"]
            },
            "f_string": {
                "regex": r"f['\"].*\{.*\}.*['\"]",
                "description": "F-string literals",
                "examples": ["f'Hello {name}'", "f'{x:.2f}'"],
                "complexity": "medium",
                "ast_nodes": ["JoinedStr", "FormattedValue"]
            },
            "raw_string": {
                "regex": r"r['\"].*['\"]",
                "description": "Raw string literals",
                "examples": ["r'\\d+\\w*'"],
                "complexity": "low",
                "ast_nodes": ["Constant"]
            },
            
            # QUANTIFIERS (NEUROGLYPH specific)
            "universal_quantifier": {
                "regex": r"∀\w+.*:",
                "description": "Universal quantifiers ∀x:",
                "examples": ["∀x ∈ ℝ: P(x)", "∀x: P(x) ⇒ Q(x)"],
                "complexity": "high",
                "ast_nodes": ["UniversalQuantification"]
            },
            "existential_quantifier": {
                "regex": r"∃\w+.*:",
                "description": "Existential quantifiers ∃x:",
                "examples": ["∃x ∈ ℕ: P(x)", "∃!x: P(x)"],
                "complexity": "high",
                "ast_nodes": ["ExistentialQuantification"]
            },
            "nested_quantifiers": {
                "regex": r"[∀∃]\w+.*[∀∃]\w+.*:",
                "description": "Nested quantifiers",
                "examples": ["∀x ∃y: R(x,y)", "∃x ∀y: P(x) ⇒ Q(y)"],
                "complexity": "very_high",
                "ast_nodes": ["nested_quantification"]
            },
            
            # MATHEMATICAL EXPRESSIONS
            "complex_math": {
                "regex": r"[∫∑∏∂∇].*",
                "description": "Complex mathematical expressions",
                "examples": ["∫₀¹ f(x)dx", "∑ᵢ₌₁ⁿ xᵢ"],
                "complexity": "high",
                "ast_nodes": ["MathematicalExpression"]
            },
            "subscript_superscript": {
                "regex": r"[a-zA-Z][₀-₉⁰-⁹]+",
                "description": "Subscripts and superscripts",
                "examples": ["x₁", "a²", "H₂O"],
                "complexity": "medium",
                "ast_nodes": ["Subscript", "Superscript"]
            },
            
            # CONTROL FLOW
            "lambda_expression": {
                "regex": r"lambda\s+.*:",
                "description": "Lambda expressions",
                "examples": ["lambda x: x*2", "lambda x, y: x+y"],
                "complexity": "medium",
                "ast_nodes": ["Lambda"]
            },
            "generator_expression": {
                "regex": r"\(.*for.*in.*\)",
                "description": "Generator expressions",
                "examples": ["(x*2 for x in range(10))"],
                "complexity": "medium",
                "ast_nodes": ["GeneratorExp"]
            }
        }
    
    def analyze_dataset(self, sample_size: int = None) -> Dict[str, Any]:
        """
        Analizza dataset per failure cases del roundtrip.
        
        Args:
            sample_size: Se specificato, analizza solo un campione
            
        Returns:
            Risultati analisi completa
        """
        print("🔍 Starting Roundtrip Failure Analysis...")
        print(f"Dataset: {self.dataset_path}")
        
        start_time = time.time()
        
        # Carica dataset
        examples = self._load_dataset()
        
        if sample_size and sample_size < len(examples):
            print(f"📊 Sampling {sample_size} examples from {len(examples)} total")
            import random
            examples = random.sample(examples, sample_size)
        
        self.analysis_stats['total_examples'] = len(examples)
        
        # Analizza ogni esempio
        for i, example in enumerate(examples):
            if i % 1000 == 0:
                print(f"Analyzing {i}/{len(examples)} examples...")
            
            self._analyze_single_example(example, i)
        
        # Calcola risultati finali
        results = self._calculate_analysis_results()
        results['elapsed_time_seconds'] = time.time() - start_time
        
        # Salva risultati
        self._save_analysis_results(results)
        self._generate_heatmap(results)
        self._print_analysis_summary(results)
        
        return results
    
    def _load_dataset(self) -> List[Dict[str, Any]]:
        """Carica dataset JSONL."""
        examples = []
        
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    example = json.loads(line.strip())
                    examples.append(example)
                except json.JSONDecodeError as e:
                    print(f"Warning: Invalid JSON at line {line_num}")
        
        print(f"📄 Loaded {len(examples)} examples from dataset")
        return examples
    
    def _analyze_single_example(self, example: Dict[str, Any], index: int):
        """Analizza singolo esempio per failure patterns."""
        try:
            prompt_symbolic = example.get('prompt_symbolic', '')
            response_symbolic = example.get('response_symbolic', '')
            
            # Analizza entrambi prompt e response
            for text_type, text in [('prompt', prompt_symbolic), ('response', response_symbolic)]:
                if not text:
                    continue
                
                # Test parsing
                parsing_success = self._test_parsing(text)
                if not parsing_success:
                    self.analysis_stats['parsing_failures'] += 1
                    self._categorize_failure(text, 'parsing_failure', index, text_type)
                    continue
                
                # Test roundtrip
                roundtrip_success = self._test_roundtrip(text)
                if not roundtrip_success:
                    self.analysis_stats['roundtrip_failures'] += 1
                    self._categorize_failure(text, 'roundtrip_failure', index, text_type)
        
        except Exception as e:
            self.analysis_stats['uncategorized_failures'].append({
                'index': index,
                'error': str(e),
                'example_id': example.get('id', 'unknown')
            })
    
    def _test_parsing(self, text: str) -> bool:
        """Testa se il testo può essere parsato."""
        try:
            ast = self.parser.parse(text)
            return ast is not None and ast.root_concept is not None
        except SemanticParseError:
            return False
        except Exception:
            return False
    
    def _test_roundtrip(self, text: str) -> bool:
        """Testa roundtrip fidelity."""
        try:
            # Parse → AST → NEUROGLYPH
            ast = self.parser.parse(text)
            reconstructed = ast.to_neuroglyph()
            
            # Verifica equivalenza (normalizzata)
            return self._semantic_equivalent(text, reconstructed)
        except Exception:
            return False
    
    def _semantic_equivalent(self, original: str, reconstructed: str) -> bool:
        """Verifica equivalenza semantica normalizzata."""
        # Normalizza spazi e caratteri
        orig_norm = ''.join(original.split())
        recon_norm = ''.join(reconstructed.split())
        
        # Verifica uguaglianza esatta o alta similarità
        if orig_norm == recon_norm:
            return True
        
        # Calcola similarità
        from difflib import SequenceMatcher
        similarity = SequenceMatcher(None, orig_norm, recon_norm).ratio()
        return similarity >= 0.95  # 95% similarità minima
    
    def _categorize_failure(self, text: str, failure_type: str, index: int, text_type: str):
        """Categorizza failure secondo pattern predefiniti."""
        matched_patterns = []
        
        # Testa ogni pattern
        for pattern_name, pattern_info in self.failure_patterns.items():
            if re.search(pattern_info['regex'], text, re.MULTILINE | re.DOTALL):
                matched_patterns.append(pattern_name)
                self.analysis_stats['pattern_matches'][pattern_name] += 1
                self.analysis_stats['category_distribution'][pattern_info['complexity']] += 1
        
        # Se nessun pattern matcha, aggiungi a uncategorized
        if not matched_patterns:
            self.analysis_stats['uncategorized_failures'].append({
                'index': index,
                'text_type': text_type,
                'failure_type': failure_type,
                'text': text[:200] + '...' if len(text) > 200 else text
            })
    
    def _calculate_analysis_results(self) -> Dict[str, Any]:
        """Calcola risultati finali dell'analisi."""
        total = self.analysis_stats['total_examples']
        
        results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S UTC'),
            'dataset_path': str(self.dataset_path),
            'total_examples': total,
            'parsing_failures': self.analysis_stats['parsing_failures'],
            'roundtrip_failures': self.analysis_stats['roundtrip_failures'],
            'parsing_success_rate': 1 - (self.analysis_stats['parsing_failures'] / total) if total > 0 else 0,
            'roundtrip_success_rate': 1 - (self.analysis_stats['roundtrip_failures'] / total) if total > 0 else 0,
            'pattern_distribution': dict(self.analysis_stats['pattern_matches']),
            'complexity_distribution': dict(self.analysis_stats['category_distribution']),
            'uncategorized_count': len(self.analysis_stats['uncategorized_failures']),
            'uncategorized_failures': self.analysis_stats['uncategorized_failures'][:50],  # Primi 50
            'top_failure_patterns': dict(Counter(self.analysis_stats['pattern_matches']).most_common(20)),
            'pattern_details': self.failure_patterns
        }
        
        return results
    
    def _save_analysis_results(self, results: Dict[str, Any]):
        """Salva risultati analisi."""
        output_file = self.output_dir / "rt_failures_by_category.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📄 Analysis results saved: {output_file}")
    
    def _generate_heatmap(self, results: Dict[str, Any]):
        """Genera heatmap dei failure patterns."""
        # Semplificato - implementazione completa richiederebbe matplotlib
        heatmap_data = {
            'complexity_vs_frequency': {},
            'pattern_intensity': {}
        }
        
        # Raggruppa per complessità
        for pattern_name, count in results['pattern_distribution'].items():
            if pattern_name in self.failure_patterns:
                complexity = self.failure_patterns[pattern_name]['complexity']
                if complexity not in heatmap_data['complexity_vs_frequency']:
                    heatmap_data['complexity_vs_frequency'][complexity] = 0
                heatmap_data['complexity_vs_frequency'][complexity] += count
        
        # Intensità pattern
        max_count = max(results['pattern_distribution'].values()) if results['pattern_distribution'] else 1
        for pattern_name, count in results['pattern_distribution'].items():
            heatmap_data['pattern_intensity'][pattern_name] = count / max_count
        
        # Salva heatmap data
        heatmap_file = self.output_dir / "rt_failures_heatmap.json"
        with open(heatmap_file, 'w', encoding='utf-8') as f:
            json.dump(heatmap_data, f, indent=2, ensure_ascii=False)
        
        print(f"📊 Heatmap data saved: {heatmap_file}")
    
    def _print_analysis_summary(self, results: Dict[str, Any]):
        """Stampa summary dell'analisi."""
        print("\n" + "="*80)
        print("🔍 ROUNDTRIP FAILURE ANALYSIS SUMMARY")
        print("="*80)
        
        print(f"Dataset: {results['dataset_path']}")
        print(f"Total Examples: {results['total_examples']:,}")
        print(f"Elapsed Time: {results['elapsed_time_seconds']:.1f}s")
        
        print(f"\n📊 FAILURE RATES:")
        print(f"  Parsing Failures: {results['parsing_failures']:,} ({(1-results['parsing_success_rate']):.1%})")
        print(f"  Roundtrip Failures: {results['roundtrip_failures']:,} ({(1-results['roundtrip_success_rate']):.1%})")
        print(f"  Parsing Success Rate: {results['parsing_success_rate']:.1%}")
        print(f"  Roundtrip Success Rate: {results['roundtrip_success_rate']:.1%}")
        
        print(f"\n🎯 TOP FAILURE PATTERNS:")
        for pattern, count in results['top_failure_patterns'].items():
            if pattern in self.failure_patterns:
                complexity = self.failure_patterns[pattern]['complexity']
                description = self.failure_patterns[pattern]['description']
                print(f"  {pattern}: {count:,} ({complexity}) - {description}")
        
        print(f"\n📈 COMPLEXITY DISTRIBUTION:")
        for complexity, count in results['complexity_distribution'].items():
            print(f"  {complexity}: {count:,} failures")
        
        print(f"\n🔍 UNCATEGORIZED FAILURES: {results['uncategorized_count']:,}")
        
        print("\n" + "="*80)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='NEUROGLYPH Roundtrip Failure Analysis')
    parser.add_argument('--full-dataset', action='store_true', help='Analyze full dataset (slow)')
    parser.add_argument('--sample', type=int, default=1000, help='Sample size for analysis')
    
    args = parser.parse_args()
    
    analyzer = RoundtripFailureAnalyzer()
    
    try:
        sample_size = None if args.full_dataset else args.sample
        results = analyzer.analyze_dataset(sample_size=sample_size)
        
        print(f"\n🎉 Analysis completed!")
        print(f"📄 Results: data/verification/rt_failures_by_category.json")
        print(f"📊 Heatmap: data/verification/rt_failures_heatmap.json")
        
    except Exception as e:
        print(f"💥 Analysis error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
