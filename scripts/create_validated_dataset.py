#!/usr/bin/env python3
"""
NEUROGLYPH - Create Validated Dataset

Crea dataset filtrato contenente solo esempi con roundtrip fidelity ≥95%.
Questo garantisce qualità perfetta per fine-tuning.

Principi Immutabili:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail

Usage:
    python3 scripts/create_validated_dataset.py
    python3 scripts/create_validated_dataset.py --target-size 5000
"""

import argparse
import json
import time
from pathlib import Path
from typing import List, Dict, Any
from difflib import SequenceMatcher
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.conceptual_parser import create_conceptual_parser, SemanticParseError


class ValidatedDatasetCreator:
    """
    Creatore di dataset validato con roundtrip fidelity garantita.
    
    Filtra esempi dal dataset 20K mantenendo solo quelli con
    roundtrip perfetto o quasi-perfetto (≥95% fidelity).
    """
    
    def __init__(self, 
                 input_dataset: str = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl",
                 output_dataset: str = "data/datasets/symbolic/neuroglyph_validated.jsonl"):
        
        self.input_path = Path(input_dataset)
        self.output_path = Path(output_dataset)
        self.output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Inizializza sistema concettuale
        self.tokenizer = create_conceptual_tokenizer()
        self.parser = create_conceptual_parser(self.tokenizer)
        
        # Statistiche validazione
        self.validation_stats = {
            'total_examples': 0,
            'validated_examples': 0,
            'parsing_failures': 0,
            'roundtrip_failures': 0,
            'fidelity_failures': 0,
            'validation_rate': 0.0,
            'average_fidelity': 0.0,
            'quality_distribution': {
                'perfect': 0,      # 100% fidelity
                'excellent': 0,    # 95-99% fidelity
                'good': 0,         # 90-94% fidelity
                'poor': 0          # <90% fidelity
            }
        }
    
    def create_validated_dataset(self, target_size: int = None, min_fidelity: float = 0.95) -> Dict[str, Any]:
        """
        Crea dataset validato filtrando esempi con alta fidelity.
        
        Args:
            target_size: Numero target di esempi (None = tutti validi)
            min_fidelity: Fidelity minima richiesta (default 95%)
            
        Returns:
            Risultati della validazione
        """
        print("🔍 Creating validated dataset...")
        print(f"Input: {self.input_path}")
        print(f"Output: {self.output_path}")
        print(f"Min Fidelity: {min_fidelity:.1%}")
        
        start_time = time.time()
        
        # Carica dataset originale
        original_examples = self._load_dataset()
        self.validation_stats['total_examples'] = len(original_examples)
        
        print(f"📄 Loaded {len(original_examples)} examples")
        
        # Valida e filtra esempi
        validated_examples = []
        
        for i, example in enumerate(original_examples):
            if i % 1000 == 0:
                print(f"Validating {i}/{len(original_examples)} examples...")
            
            # Valida esempio
            validation_result = self._validate_example(example, min_fidelity)
            
            if validation_result['is_valid']:
                validated_examples.append(example)
                self.validation_stats['validated_examples'] += 1
                
                # Aggiorna distribuzione qualità
                fidelity = validation_result['fidelity']
                if fidelity >= 1.0:
                    self.validation_stats['quality_distribution']['perfect'] += 1
                elif fidelity >= 0.95:
                    self.validation_stats['quality_distribution']['excellent'] += 1
                elif fidelity >= 0.90:
                    self.validation_stats['quality_distribution']['good'] += 1
                else:
                    self.validation_stats['quality_distribution']['poor'] += 1
            
            # Interrompi se raggiunto target
            if target_size and len(validated_examples) >= target_size:
                print(f"🎯 Reached target size: {target_size}")
                break
        
        # Calcola statistiche finali
        self._calculate_final_stats()
        
        # Salva dataset validato
        self._save_validated_dataset(validated_examples)
        
        elapsed_time = time.time() - start_time
        
        results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S UTC'),
            'input_dataset': str(self.input_path),
            'output_dataset': str(self.output_path),
            'min_fidelity': min_fidelity,
            'target_size': target_size,
            'total_examples': self.validation_stats['total_examples'],
            'validated_examples': self.validation_stats['validated_examples'],
            'validation_rate': self.validation_stats['validation_rate'],
            'average_fidelity': self.validation_stats['average_fidelity'],
            'quality_distribution': self.validation_stats['quality_distribution'],
            'elapsed_time_seconds': elapsed_time,
            'success': True
        }
        
        self._print_results(results)
        
        return results
    
    def _load_dataset(self) -> List[Dict[str, Any]]:
        """Carica dataset JSONL."""
        examples = []
        
        with open(self.input_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    example = json.loads(line.strip())
                    examples.append(example)
                except json.JSONDecodeError:
                    print(f"Warning: Invalid JSON at line {line_num}")
        
        return examples
    
    def _validate_example(self, example: Dict[str, Any], min_fidelity: float) -> Dict[str, Any]:
        """
        Valida singolo esempio per roundtrip fidelity.
        
        Returns:
            Dict con is_valid, fidelity, error
        """
        result = {
            'is_valid': False,
            'fidelity': 0.0,
            'error': None
        }
        
        try:
            # Estrai testo simbolico
            prompt_symbolic = example.get('prompt_symbolic', '')
            response_symbolic = example.get('response_symbolic', '')
            
            # Valida entrambi prompt e response
            prompt_fidelity = self._test_roundtrip_fidelity(prompt_symbolic) if prompt_symbolic else 1.0
            response_fidelity = self._test_roundtrip_fidelity(response_symbolic) if response_symbolic else 1.0
            
            # Fidelity complessiva (media pesata)
            overall_fidelity = (prompt_fidelity + response_fidelity) / 2
            
            result['fidelity'] = overall_fidelity
            result['is_valid'] = overall_fidelity >= min_fidelity
            
        except Exception as e:
            result['error'] = str(e)
            self.validation_stats['roundtrip_failures'] += 1
        
        return result
    
    def _test_roundtrip_fidelity(self, text: str) -> float:
        """
        Testa fidelity roundtrip per singolo testo.
        
        Returns:
            Fidelity score 0.0-1.0
        """
        if not text or not text.strip():
            return 1.0  # Testo vuoto = perfetto
        
        try:
            # Parse
            ast = self.parser.parse(text)
            
            if not ast or not ast.root_concept:
                self.validation_stats['parsing_failures'] += 1
                return 0.0
            
            # Serialize
            reconstructed = ast.to_neuroglyph()
            
            # Calcola fidelity
            fidelity = self._calculate_fidelity(text, reconstructed)
            
            return fidelity
        
        except SemanticParseError:
            self.validation_stats['parsing_failures'] += 1
            return 0.0
        except Exception:
            self.validation_stats['roundtrip_failures'] += 1
            return 0.0
    
    def _calculate_fidelity(self, original: str, reconstructed: str) -> float:
        """Calcola fidelity tra testo originale e ricostruito."""
        # Normalizza entrambi
        orig_norm = self._normalize_text(original)
        recon_norm = self._normalize_text(reconstructed)
        
        # Match esatto = fidelity perfetta
        if orig_norm == recon_norm:
            return 1.0
        
        # Calcola similarità con SequenceMatcher
        similarity = SequenceMatcher(None, orig_norm, recon_norm).ratio()
        
        return similarity
    
    def _normalize_text(self, text: str) -> str:
        """Normalizza testo per confronto."""
        import re
        
        # Rimuovi spazi extra
        normalized = re.sub(r'\s+', ' ', text.strip())
        
        # Normalizza parentesi
        normalized = re.sub(r'\s*\(\s*', '(', normalized)
        normalized = re.sub(r'\s*\)\s*', ')', normalized)
        
        # Normalizza operatori comuni
        normalized = re.sub(r'\s*:\s*', ': ', normalized)
        normalized = re.sub(r'\s*∈\s*', ' ∈ ', normalized)
        normalized = re.sub(r'\s*⇒\s*', ' ⇒ ', normalized)
        normalized = re.sub(r'\s*∧\s*', ' ∧ ', normalized)
        normalized = re.sub(r'\s*∨\s*', ' ∨ ', normalized)
        
        return normalized.strip()
    
    def _calculate_final_stats(self):
        """Calcola statistiche finali."""
        total = self.validation_stats['total_examples']
        validated = self.validation_stats['validated_examples']
        
        if total > 0:
            self.validation_stats['validation_rate'] = validated / total
        
        # Calcola fidelity media (approssimata)
        quality_dist = self.validation_stats['quality_distribution']
        total_quality = sum(quality_dist.values())
        
        if total_quality > 0:
            weighted_fidelity = (
                quality_dist['perfect'] * 1.0 +
                quality_dist['excellent'] * 0.97 +
                quality_dist['good'] * 0.92 +
                quality_dist['poor'] * 0.85
            ) / total_quality
            
            self.validation_stats['average_fidelity'] = weighted_fidelity
    
    def _save_validated_dataset(self, examples: List[Dict[str, Any]]):
        """Salva dataset validato."""
        print(f"💾 Saving {len(examples)} validated examples...")
        
        with open(self.output_path, 'w', encoding='utf-8') as f:
            for example in examples:
                json.dump(example, f, ensure_ascii=False)
                f.write('\n')
        
        print(f"✅ Validated dataset saved: {self.output_path}")
    
    def _print_results(self, results: Dict[str, Any]):
        """Stampa risultati formattati."""
        print("\n" + "="*80)
        print("🔍 VALIDATED DATASET CREATION RESULTS")
        print("="*80)
        
        print(f"📄 INPUT: {results['input_dataset']}")
        print(f"💾 OUTPUT: {results['output_dataset']}")
        print(f"⏱️ TIME: {results['elapsed_time_seconds']:.1f}s")
        
        print(f"\n📊 VALIDATION STATISTICS:")
        print(f"  Total Examples: {results['total_examples']:,}")
        print(f"  Validated Examples: {results['validated_examples']:,}")
        print(f"  Validation Rate: {results['validation_rate']:.1%}")
        print(f"  Average Fidelity: {results['average_fidelity']:.1%}")
        
        print(f"\n🎯 QUALITY DISTRIBUTION:")
        quality_dist = results['quality_distribution']
        total_quality = sum(quality_dist.values())
        
        for quality, count in quality_dist.items():
            percentage = (count / total_quality * 100) if total_quality > 0 else 0
            print(f"  {quality.title()}: {count:,} ({percentage:.1f}%)")
        
        # Assessment
        validation_rate = results['validation_rate']
        if validation_rate >= 0.50:
            assessment = "✅ EXCELLENT"
        elif validation_rate >= 0.30:
            assessment = "🟡 GOOD"
        elif validation_rate >= 0.10:
            assessment = "🟠 ACCEPTABLE"
        else:
            assessment = "❌ POOR"
        
        print(f"\n🏆 OVERALL ASSESSMENT: {assessment}")
        print(f"📈 DATASET READY FOR FINE-TUNING: {'✅ YES' if validation_rate >= 0.10 else '❌ NO'}")
        
        print("\n" + "="*80)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Create Validated NEUROGLYPH Dataset')
    parser.add_argument('--target-size', type=int, help='Target number of validated examples')
    parser.add_argument('--min-fidelity', type=float, default=0.95, help='Minimum fidelity threshold')
    parser.add_argument('--input', type=str, 
                       default="data/datasets/symbolic/neuroglyph_symbolic_final.jsonl",
                       help='Input dataset path')
    parser.add_argument('--output', type=str,
                       default="data/datasets/symbolic/neuroglyph_validated.jsonl", 
                       help='Output dataset path')
    
    args = parser.parse_args()
    
    creator = ValidatedDatasetCreator(args.input, args.output)
    
    try:
        results = creator.create_validated_dataset(
            target_size=args.target_size,
            min_fidelity=args.min_fidelity
        )
        
        if results['success'] and results['validated_examples'] > 0:
            print(f"\n🎉 SUCCESS! Created validated dataset with {results['validated_examples']} examples")
            print(f"📊 Validation rate: {results['validation_rate']:.1%}")
            print(f"🎯 Ready for fine-tuning!")
        else:
            print(f"\n❌ FAILED! No examples passed validation")
            print(f"💡 Try lowering --min-fidelity threshold")
            sys.exit(1)
    
    except Exception as e:
        print(f"💥 Error creating validated dataset: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
