#!/usr/bin/env python3
"""
NEUROGLYPH - Test AST Serialization Fix

Testa i fix della serializzazione AST per rispettare i Principi Immutabili.
Focus su PRIORITÀ 1: Quantificatori con standard rigorosi ≥95% fidelity.

PRINCIPI IMMUTABILI NON NEGOZIABILI:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita (≥95%)
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.ast.conceptual_ast import (
    UniversalQuantification, ExistentialQuantification, MaterialImplication,
    LogicalConjunction, LogicalDisjunction, LogicalNegation,
    Variable, FunctionCall, BinaryOperation,
    create_variable, create_function_call, create_binary_operation
)


class ASTSerializationTester:
    """Tester rigoroso per serializzazione AST con standard immutabili."""
    
    def __init__(self):
        self.min_fidelity = 0.95  # STANDARD IMMUTABILE
        self.test_results = []
    
    def test_priority_1_quantifiers(self):
        """Test PRIORITÀ 1: Quantificatori con standard rigorosi."""
        print("🔧 TESTING PRIORITY 1: QUANTIFIERS")
        print("="*60)
        print(f"IMMUTABLE STANDARD: ≥{self.min_fidelity:.1%} fidelity")
        print("="*60)
        
        # Test cases per quantificatori
        test_cases = [
            # Quantificatori semplici
            {
                'name': 'Universal Simple',
                'ast': UniversalQuantification(
                    variable='x',
                    body=create_function_call('P', [create_variable('x')])
                ),
                'expected': '∀x: P(x)'
            },
            {
                'name': 'Existential Simple', 
                'ast': ExistentialQuantification(
                    variable='x',
                    body=create_function_call('Q', [create_variable('x')])
                ),
                'expected': '∃x: Q(x)'
            },
            
            # Quantificatori con dominio
            {
                'name': 'Universal with Domain',
                'ast': UniversalQuantification(
                    variable='x',
                    domain=create_variable('ℝ'),
                    body=create_function_call('P', [create_variable('x')])
                ),
                'expected': '∀x ∈ ℝ: P(x)'
            },
            {
                'name': 'Existential with Domain',
                'ast': ExistentialQuantification(
                    variable='x', 
                    domain=create_variable('ℕ'),
                    body=create_function_call('P', [create_variable('x')])
                ),
                'expected': '∃x ∈ ℕ: P(x)'
            },
            
            # Quantificatori con body complesso
            {
                'name': 'Universal Complex Body',
                'ast': UniversalQuantification(
                    variable='x',
                    body=LogicalConjunction(
                        create_function_call('P', [create_variable('x')]),
                        create_function_call('Q', [create_variable('x')])
                    )
                ),
                'expected': '∀x: P(x) ∧ Q(x)'
            },
            
            # Quantificatori annidati
            {
                'name': 'Nested Quantifiers',
                'ast': UniversalQuantification(
                    variable='x',
                    body=ExistentialQuantification(
                        variable='y',
                        body=create_function_call('R', [create_variable('x'), create_variable('y')])
                    )
                ),
                'expected': '∀x: ∃y: R(x, y)'
            }
        ]
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n[{i:2d}/{total_tests}] {test_case['name']}")
            
            result = self._test_single_ast(test_case['ast'], test_case['expected'])
            self.test_results.append({
                'name': test_case['name'],
                'category': 'quantifiers',
                **result
            })
            
            if result['meets_standard']:
                passed_tests += 1
                print(f"    ✅ PASS - Fidelity: {result['fidelity']:.1%}")
                print(f"    📤 Output: {result['output']}")
            else:
                print(f"    ❌ FAIL - Fidelity: {result['fidelity']:.1%}")
                print(f"    📤 Expected: {test_case['expected']}")
                print(f"    📤 Got:      {result['output']}")
                if result['error']:
                    print(f"    💥 Error: {result['error']}")
        
        success_rate = passed_tests / total_tests
        
        print(f"\n📊 PRIORITY 1 RESULTS:")
        print(f"  Passed: {passed_tests}/{total_tests}")
        print(f"  Success Rate: {success_rate:.1%}")
        print(f"  Standard: {'✅ MET' if success_rate >= self.min_fidelity else '❌ FAILED'}")
        
        return success_rate >= self.min_fidelity
    
    def test_priority_2_binary_operations(self):
        """Test PRIORITÀ 2: Operazioni binarie."""
        print("\n🔧 TESTING PRIORITY 2: BINARY OPERATIONS")
        print("="*60)
        
        test_cases = [
            # Operazioni matematiche
            {
                'name': 'Addition',
                'ast': create_binary_operation(
                    create_variable('x'),
                    '+',
                    create_variable('y')
                ),
                'expected': 'x + y'
            },
            
            # Operazioni insiemistiche
            {
                'name': 'Set Union',
                'ast': create_binary_operation(
                    create_variable('A'),
                    '∪',
                    create_variable('B')
                ),
                'expected': 'A ∪ B'
            },
            {
                'name': 'Set Intersection',
                'ast': create_binary_operation(
                    create_variable('A'),
                    '∩',
                    create_variable('B')
                ),
                'expected': 'A ∩ B'
            },
            {
                'name': 'Set Membership',
                'ast': create_binary_operation(
                    create_variable('x'),
                    '∈',
                    create_variable('A')
                ),
                'expected': 'x ∈ A'
            },
            {
                'name': 'Subset Relation',
                'ast': create_binary_operation(
                    create_variable('A'),
                    '⊆',
                    create_variable('B')
                ),
                'expected': 'A ⊆ B'
            }
        ]
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n[{i:2d}/{total_tests}] {test_case['name']}")
            
            result = self._test_single_ast(test_case['ast'], test_case['expected'])
            self.test_results.append({
                'name': test_case['name'],
                'category': 'binary_operations',
                **result
            })
            
            if result['meets_standard']:
                passed_tests += 1
                print(f"    ✅ PASS - Fidelity: {result['fidelity']:.1%}")
            else:
                print(f"    ❌ FAIL - Fidelity: {result['fidelity']:.1%}")
                print(f"    📤 Expected: {test_case['expected']}")
                print(f"    📤 Got:      {result['output']}")
        
        success_rate = passed_tests / total_tests
        
        print(f"\n📊 PRIORITY 2 RESULTS:")
        print(f"  Passed: {passed_tests}/{total_tests}")
        print(f"  Success Rate: {success_rate:.1%}")
        print(f"  Standard: {'✅ MET' if success_rate >= self.min_fidelity else '❌ FAILED'}")
        
        return success_rate >= self.min_fidelity
    
    def test_existing_working_patterns(self):
        """Test pattern che già funzionano (non devono regredire)."""
        print("\n🔧 TESTING EXISTING WORKING PATTERNS")
        print("="*60)
        
        test_cases = [
            {
                'name': 'Material Implication',
                'ast': MaterialImplication(
                    create_variable('P'),
                    create_variable('Q')
                ),
                'expected': 'P ⇒ Q'
            },
            {
                'name': 'Logical Conjunction',
                'ast': LogicalConjunction(
                    create_variable('P'),
                    create_variable('Q')
                ),
                'expected': 'P ∧ Q'
            },
            {
                'name': 'Logical Disjunction',
                'ast': LogicalDisjunction(
                    create_variable('P'),
                    create_variable('Q')
                ),
                'expected': 'P ∨ Q'
            },
            {
                'name': 'Logical Negation',
                'ast': LogicalNegation(
                    create_variable('P')
                ),
                'expected': '¬P'
            }
        ]
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n[{i:2d}/{total_tests}] {test_case['name']}")
            
            result = self._test_single_ast(test_case['ast'], test_case['expected'])
            self.test_results.append({
                'name': test_case['name'],
                'category': 'existing_working',
                **result
            })
            
            if result['meets_standard']:
                passed_tests += 1
                print(f"    ✅ PASS - Fidelity: {result['fidelity']:.1%}")
            else:
                print(f"    ❌ FAIL - Fidelity: {result['fidelity']:.1%}")
                print(f"    📤 Expected: {test_case['expected']}")
                print(f"    📤 Got:      {result['output']}")
        
        success_rate = passed_tests / total_tests
        
        print(f"\n📊 EXISTING PATTERNS RESULTS:")
        print(f"  Passed: {passed_tests}/{total_tests}")
        print(f"  Success Rate: {success_rate:.1%}")
        print(f"  Regression: {'❌ YES' if success_rate < 1.0 else '✅ NO'}")
        
        return success_rate >= 1.0  # Nessuna regressione ammessa
    
    def _test_single_ast(self, ast, expected: str) -> dict:
        """Test rigoroso di singolo AST."""
        result = {
            'output': None,
            'fidelity': 0.0,
            'meets_standard': False,
            'error': None
        }
        
        try:
            output = ast.to_neuroglyph()
            result['output'] = output
            
            # Calcola fidelity
            fidelity = self._calculate_fidelity(expected, output)
            result['fidelity'] = fidelity
            result['meets_standard'] = fidelity >= self.min_fidelity
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _calculate_fidelity(self, expected: str, actual: str) -> float:
        """Calcolo rigoroso della fidelity."""
        # Exact match = 100% fidelity
        if expected.strip() == actual.strip():
            return 1.0
        
        # Normalized comparison
        expected_norm = self._normalize(expected)
        actual_norm = self._normalize(actual)
        
        if expected_norm == actual_norm:
            return 1.0
        
        # Sequence similarity
        from difflib import SequenceMatcher
        similarity = SequenceMatcher(None, expected_norm, actual_norm).ratio()
        return similarity
    
    def _normalize(self, text: str) -> str:
        """Normalizzazione minimale per confronto."""
        import re
        normalized = re.sub(r'\s+', ' ', text.strip())
        return normalized
    
    def print_final_summary(self):
        """Stampa summary finale rigoroso."""
        categories = {}
        for result in self.test_results:
            cat = result['category']
            if cat not in categories:
                categories[cat] = {'passed': 0, 'total': 0}
            categories[cat]['total'] += 1
            if result['meets_standard']:
                categories[cat]['passed'] += 1
        
        total_passed = sum(cat['passed'] for cat in categories.values())
        total_tests = sum(cat['total'] for cat in categories.values())
        overall_success = total_passed / total_tests if total_tests > 0 else 0
        
        print("\n" + "="*80)
        print("🔧 AST SERIALIZATION FIX - FINAL RESULTS")
        print("="*80)
        
        print(f"📊 OVERALL RESULTS:")
        print(f"  Total Tests: {total_tests}")
        print(f"  Passed: {total_passed}")
        print(f"  Success Rate: {overall_success:.1%}")
        
        print(f"\n📋 BY CATEGORY:")
        for category, stats in categories.items():
            success_rate = stats['passed'] / stats['total']
            status = "✅ PASS" if success_rate >= self.min_fidelity else "❌ FAIL"
            print(f"  {category}: {stats['passed']}/{stats['total']} ({success_rate:.1%}) {status}")
        
        print(f"\n🎯 IMMUTABLE STANDARD ASSESSMENT:")
        print(f"  Required Success Rate: ≥{self.min_fidelity:.1%}")
        print(f"  Achieved Success Rate: {overall_success:.1%}")
        
        if overall_success >= self.min_fidelity:
            print(f"  Status: ✅ MEETS IMMUTABLE STANDARDS")
            print(f"  Decision: ✅ PROCEED with fine-tuning")
        else:
            print(f"  Status: ❌ FAILS IMMUTABLE STANDARDS")
            print(f"  Gap: {self.min_fidelity - overall_success:.1%} improvement needed")
            print(f"  Decision: ❌ MUST FIX before proceeding")
        
        print("\n" + "="*80)
        
        return overall_success >= self.min_fidelity


def main():
    """Main entry point."""
    tester = ASTSerializationTester()
    
    print("🚨 STARTING AST SERIALIZATION FIX TEST")
    print("⚠️  IMMUTABLE STANDARDS ENFORCED")
    print("🎯 TARGET: ≥95% fidelity on ALL patterns")
    
    # Test in ordine di priorità
    priority_1_success = tester.test_priority_1_quantifiers()
    priority_2_success = tester.test_priority_2_binary_operations()
    no_regression = tester.test_existing_working_patterns()
    
    # Summary finale
    overall_success = tester.print_final_summary()
    
    if overall_success and priority_1_success and no_regression:
        print("\n🎉 SUCCESS: AST serialization fixes meet immutable standards!")
        sys.exit(0)
    else:
        print("\n💥 FAILURE: AST serialization fixes fail immutable standards!")
        print("🔧 REQUIRED ACTION: Fix serialization methods")
        sys.exit(1)


if __name__ == "__main__":
    main()
