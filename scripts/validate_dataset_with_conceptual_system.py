#!/usr/bin/env python3
"""
NEUROGLYPH Dataset Validation with Conceptual System

Valida il dataset 20k con il sistema concettuale completo:
- Registry compliance al 100%
- Tokenization integrity con zero-splitting
- Parsing semantico su tutti gli esempi
- Roundtrip fidelity validation

Usage:
    python3 scripts/validate_dataset_with_conceptual_system.py
    python3 scripts/validate_dataset_with_conceptual_system.py --sample 1000
"""

import argparse
import json
import time
import random
from pathlib import Path
from typing import List, Dict, Any, Set
import sys
from collections import defaultdict, Counter

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.conceptual_parser import create_conceptual_parser, SemanticParseError


class DatasetConceptualValidator:
    """
    Validatore completo per dataset NEUROGLYPH con sistema concettuale.
    
    Verifica registry compliance, tokenization integrity, parsing semantico
    e roundtrip fidelity su tutto il dataset 20k.
    """
    
    def __init__(self, dataset_path: str, output_dir: str = "data/validation"):
        self.dataset_path = Path(dataset_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Inizializza sistema concettuale
        self.tokenizer = create_conceptual_tokenizer()
        self.parser = create_conceptual_parser(self.tokenizer)
        
        # Statistiche di validazione
        self.validation_stats = {
            'total_examples': 0,
            'registry_compliant': 0,
            'tokenization_successful': 0,
            'parsing_successful': 0,
            'roundtrip_successful': 0,
            'symbol_coverage': {},
            'category_stats': defaultdict(int),
            'difficulty_stats': defaultdict(int),
            'errors': []
        }
        
        # Criteri di qualità
        self.quality_thresholds = {
            'registry_compliance': 1.0,      # 100%
            'tokenization_integrity': 0.98,  # 98%
            'parsing_success_rate': 0.95,    # 95%
            'roundtrip_fidelity': 0.95,      # 95%
            'symbol_coverage': 0.90          # 90% simboli usati
        }
    
    def validate_complete_dataset(self, sample_size: int = None) -> Dict[str, Any]:
        """
        Valida dataset completo o campione.
        
        Args:
            sample_size: Se specificato, valida solo un campione random
            
        Returns:
            Risultati validazione completa
        """
        print("🔬 Starting NEUROGLYPH Dataset Validation with Conceptual System...")
        print(f"Dataset: {self.dataset_path}")
        
        start_time = time.time()
        
        # Carica dataset
        examples = self._load_dataset()
        
        if sample_size and sample_size < len(examples):
            print(f"📊 Sampling {sample_size} examples from {len(examples)} total")
            examples = random.sample(examples, sample_size)
        
        self.validation_stats['total_examples'] = len(examples)
        
        # Reset statistiche
        self.tokenizer.reset_stats()
        self.parser.reset_stats()
        
        # Valida ogni esempio
        for i, example in enumerate(examples):
            if i % 1000 == 0:
                print(f"Processing {i}/{len(examples)} examples...")
            
            self._validate_single_example(example, i)
        
        # Calcola metriche finali
        results = self._calculate_final_metrics()
        results['elapsed_time_seconds'] = time.time() - start_time
        
        # Salva risultati
        self._save_validation_results(results)
        self._print_validation_summary(results)
        
        return results
    
    def _load_dataset(self) -> List[Dict[str, Any]]:
        """Carica dataset JSONL."""
        examples = []
        
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    example = json.loads(line.strip())
                    examples.append(example)
                except json.JSONDecodeError as e:
                    self.validation_stats['errors'].append({
                        'type': 'json_decode_error',
                        'line': line_num,
                        'error': str(e)
                    })
        
        print(f"📄 Loaded {len(examples)} examples from dataset")
        return examples
    
    def _validate_single_example(self, example: Dict[str, Any], index: int):
        """Valida singolo esempio del dataset."""
        try:
            # Estrai campi
            prompt_symbolic = example.get('prompt_symbolic', '')
            response_symbolic = example.get('response_symbolic', '')
            symbols_used = example.get('symbols_used', [])
            category = example.get('category', 'unknown')
            difficulty = example.get('difficulty', 'unknown')
            
            # Aggiorna statistiche categoria/difficoltà
            self.validation_stats['category_stats'][category] += 1
            self.validation_stats['difficulty_stats'][difficulty] += 1
            
            # 1. Registry Compliance Check
            registry_compliant = self._check_registry_compliance(symbols_used)
            if registry_compliant:
                self.validation_stats['registry_compliant'] += 1
            
            # 2. Tokenization Validation
            tokenization_success = self._validate_tokenization(prompt_symbolic, response_symbolic)
            if tokenization_success:
                self.validation_stats['tokenization_successful'] += 1
            
            # 3. Parsing Validation
            parsing_success = self._validate_parsing(prompt_symbolic, response_symbolic)
            if parsing_success:
                self.validation_stats['parsing_successful'] += 1
            
            # 4. Roundtrip Validation
            roundtrip_success = self._validate_roundtrip(prompt_symbolic, response_symbolic)
            if roundtrip_success:
                self.validation_stats['roundtrip_successful'] += 1
            
            # 5. Symbol Coverage Tracking
            for symbol in symbols_used:
                if symbol not in self.validation_stats['symbol_coverage']:
                    self.validation_stats['symbol_coverage'][symbol] = 0
                self.validation_stats['symbol_coverage'][symbol] += 1
            
        except Exception as e:
            self.validation_stats['errors'].append({
                'type': 'validation_error',
                'index': index,
                'example_id': example.get('id', 'unknown'),
                'error': str(e)
            })
    
    def _check_registry_compliance(self, symbols_used: List[str]) -> bool:
        """Verifica che tutti i simboli siano nel registry."""
        try:
            for symbol in symbols_used:
                if not self.tokenizer.registry.has_symbol(symbol):
                    return False
            return True
        except Exception:
            return False
    
    def _validate_tokenization(self, prompt: str, response: str) -> bool:
        """Valida tokenizzazione con zero-splitting."""
        try:
            # Tokenizza prompt e response
            prompt_tokens = self.tokenizer.tokenize(prompt)
            response_tokens = self.tokenizer.tokenize(response)
            
            # Verifica che non ci siano errori
            return len(prompt_tokens) > 0 and len(response_tokens) > 0
            
        except Exception:
            return False
    
    def _validate_parsing(self, prompt: str, response: str) -> bool:
        """Valida parsing semantico."""
        try:
            # Prova a parsare prompt e response
            prompt_ast = self.parser.parse(prompt)
            response_ast = self.parser.parse(response)
            
            # Verifica che gli AST siano validi
            return (prompt_ast is not None and 
                   response_ast is not None and
                   prompt_ast.root_concept is not None and
                   response_ast.root_concept is not None)
            
        except SemanticParseError:
            return False
        except Exception:
            return False
    
    def _validate_roundtrip(self, prompt: str, response: str) -> bool:
        """Valida roundtrip semantico."""
        try:
            # Parse → AST → NEUROGLYPH
            prompt_ast = self.parser.parse(prompt)
            response_ast = self.parser.parse(response)
            
            prompt_reconstructed = prompt_ast.to_neuroglyph()
            response_reconstructed = response_ast.to_neuroglyph()
            
            # Verifica equivalenza semantica (normalizzata)
            prompt_match = self._semantic_equivalent(prompt, prompt_reconstructed)
            response_match = self._semantic_equivalent(response, response_reconstructed)
            
            return prompt_match and response_match
            
        except Exception:
            return False
    
    def _semantic_equivalent(self, original: str, reconstructed: str) -> bool:
        """Verifica equivalenza semantica normalizzata."""
        # Normalizza spazi e caratteri
        orig_norm = ''.join(original.split())
        recon_norm = ''.join(reconstructed.split())
        
        # Verifica uguaglianza esatta o alta similarità
        if orig_norm == recon_norm:
            return True
        
        # Calcola similarità
        from difflib import SequenceMatcher
        similarity = SequenceMatcher(None, orig_norm, recon_norm).ratio()
        return similarity >= 0.90  # 90% similarità minima
    
    def _calculate_final_metrics(self) -> Dict[str, Any]:
        """Calcola metriche finali di validazione."""
        total = self.validation_stats['total_examples']
        
        if total == 0:
            return {'error': 'No examples processed'}
        
        # Calcola percentuali
        metrics = {
            'registry_compliance_rate': self.validation_stats['registry_compliant'] / total,
            'tokenization_success_rate': self.validation_stats['tokenization_successful'] / total,
            'parsing_success_rate': self.validation_stats['parsing_successful'] / total,
            'roundtrip_fidelity_rate': self.validation_stats['roundtrip_successful'] / total,
        }
        
        # Symbol coverage
        total_symbols_in_registry = len(self.tokenizer.registry.get_all_symbols())
        symbols_used_count = len(self.validation_stats['symbol_coverage'])
        metrics['symbol_coverage_rate'] = symbols_used_count / total_symbols_in_registry if total_symbols_in_registry > 0 else 0
        
        # Overall quality score
        metrics['overall_quality_score'] = (
            metrics['registry_compliance_rate'] * 0.25 +
            metrics['tokenization_success_rate'] * 0.20 +
            metrics['parsing_success_rate'] * 0.25 +
            metrics['roundtrip_fidelity_rate'] * 0.25 +
            metrics['symbol_coverage_rate'] * 0.05
        )
        
        # Quality assessment
        quality_pass = all(
            metrics[key.replace('_', '_')] >= threshold
            for key, threshold in self.quality_thresholds.items()
            if key.replace('_', '_') in metrics
        )
        
        return {
            'dataset_path': str(self.dataset_path),
            'total_examples': total,
            'metrics': metrics,
            'quality_thresholds': self.quality_thresholds,
            'quality_assessment': {
                'overall_pass': quality_pass,
                'failed_criteria': [
                    key for key, threshold in self.quality_thresholds.items()
                    if metrics.get(key.replace('_', '_'), 0) < threshold
                ]
            },
            'statistics': {
                'category_distribution': dict(self.validation_stats['category_stats']),
                'difficulty_distribution': dict(self.validation_stats['difficulty_stats']),
                'symbol_usage': dict(self.validation_stats['symbol_coverage']),
                'error_count': len(self.validation_stats['errors']),
                'top_symbols': dict(Counter(self.validation_stats['symbol_coverage']).most_common(20))
            },
            'component_stats': {
                'tokenizer': self.tokenizer.get_tokenization_stats(),
                'parser': self.parser.get_parse_stats()
            }
        }
    
    def _save_validation_results(self, results: Dict[str, Any]):
        """Salva risultati validazione."""
        timestamp = int(time.time())
        output_file = self.output_dir / f"dataset_validation_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📄 Validation results saved: {output_file}")
    
    def _print_validation_summary(self, results: Dict[str, Any]):
        """Stampa summary validazione."""
        metrics = results['metrics']
        
        print("\n" + "="*80)
        print("🔬 NEUROGLYPH DATASET VALIDATION SUMMARY")
        print("="*80)
        
        print(f"Dataset: {results['dataset_path']}")
        print(f"Total Examples: {results['total_examples']:,}")
        print(f"Elapsed Time: {results['elapsed_time_seconds']:.1f}s")
        
        print(f"\n📊 CORE METRICS:")
        for metric, value in metrics.items():
            if metric.endswith('_rate') or metric.endswith('_score'):
                print(f"  {metric}: {value:.1%}")
        
        print(f"\n🎯 QUALITY ASSESSMENT:")
        assessment = results['quality_assessment']
        print(f"Overall Pass: {'✅ PASS' if assessment['overall_pass'] else '❌ FAIL'}")
        
        if assessment['failed_criteria']:
            print("❌ Failed Criteria:")
            for criterion in assessment['failed_criteria']:
                threshold = results['quality_thresholds'][criterion]
                actual = metrics.get(criterion.replace('_', '_'), 0)
                print(f"  - {criterion}: {actual:.1%} < {threshold:.1%}")
        
        # Statistiche distribuzione
        stats = results['statistics']
        print(f"\n📈 DISTRIBUTION STATISTICS:")
        print(f"Categories: {len(stats['category_distribution'])}")
        print(f"Difficulties: {len(stats['difficulty_distribution'])}")
        print(f"Unique Symbols Used: {len(stats['symbol_usage'])}")
        print(f"Errors: {stats['error_count']}")
        
        print(f"\n🔝 TOP SYMBOLS:")
        for symbol, count in list(stats['top_symbols'].items())[:10]:
            print(f"  {symbol}: {count:,} uses")
        
        print("\n" + "="*80)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='NEUROGLYPH Dataset Validation')
    parser.add_argument('--dataset', type=str, 
                       default='data/datasets/symbolic/neuroglyph_symbolic_final.jsonl',
                       help='Path to dataset file')
    parser.add_argument('--sample', type=int, help='Sample size for validation')
    parser.add_argument('--output-dir', type=str, default='data/validation',
                       help='Output directory for results')
    
    args = parser.parse_args()
    
    validator = DatasetConceptualValidator(
        dataset_path=args.dataset,
        output_dir=args.output_dir
    )
    
    try:
        results = validator.validate_complete_dataset(sample_size=args.sample)
        
        # Exit code basato su qualità
        if results['quality_assessment']['overall_pass']:
            print("\n🎉 DATASET VALIDATION PASSED!")
            sys.exit(0)
        else:
            print("\n💥 DATASET VALIDATION FAILED!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 VALIDATION ERROR: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
