#!/usr/bin/env python3
"""
NEUROGLYPH - Generate Audit Hash

Script autorizzato per rigenerare l'hash di audit dopo modifiche legittime.

ATTENZIONE: Usare solo per modifiche autorizzate e documentate.
"""

import sys
import hashlib
from pathlib import Path


def generate_audit_hash():
    """Genera hash di audit autorizzato."""
    print("🔐 NEUROGLYPH AUDIT HASH GENERATOR")
    print("="*50)
    print("⚠️  AUTHORIZED USE ONLY")
    print("="*50)
    
    project_root = Path(__file__).parent.parent
    constants_file = project_root / "neuroglyph" / "core" / "constants.py"
    hash_lock_file = project_root / "audit_hash.lock"
    
    # Verifica che il file constants.py esista
    if not constants_file.exists():
        print(f"❌ ERROR: constants.py not found at {constants_file}")
        return False
    
    # Calcola hash corrente
    print(f"📁 Reading: {constants_file}")
    with open(constants_file, 'rb') as f:
        data = f.read()
        new_hash = hashlib.sha256(data).hexdigest()
    
    print(f"🔐 New hash: {new_hash}")
    
    # Salva nuovo hash
    print(f"💾 Writing: {hash_lock_file}")
    with open(hash_lock_file, 'w') as f:
        f.write(f"{new_hash}  {constants_file.relative_to(project_root)}\n")
    
    print(f"✅ Audit hash updated successfully")
    print(f"🔒 File integrity protection restored")
    
    # Verifica che il nuovo hash funzioni
    print(f"\n🧪 Verifying new hash...")
    try:
        # Test hash verification
        with open(constants_file, 'rb') as f:
            verify_data = f.read()
            verify_hash = hashlib.sha256(verify_data).hexdigest()
        
        with open(hash_lock_file, 'r') as f:
            saved_hash = f.read().strip().split()[0]
        
        if verify_hash == saved_hash:
            print(f"✅ Hash verification: PASSED")
            return True
        else:
            print(f"❌ Hash verification: FAILED")
            print(f"   Expected: {saved_hash}")
            print(f"   Got:      {verify_hash}")
            return False
    
    except Exception as e:
        print(f"❌ Hash verification error: {e}")
        return False


def main():
    """Main entry point."""
    print("🚨 STARTING AUTHORIZED HASH GENERATION")
    print("⚠️  This operation updates audit protection")
    
    success = generate_audit_hash()
    
    if success:
        print(f"\n🎉 SUCCESS: Audit hash generated!")
        print(f"🔒 Audit lock system restored")
        print(f"✅ Ready for testing")
        sys.exit(0)
    else:
        print(f"\n💥 FAILURE: Hash generation failed!")
        print(f"🔧 Manual intervention required")
        sys.exit(1)


if __name__ == "__main__":
    main()
