#!/usr/bin/env python3
"""
NEUROGLYPH - Test Parser Rigor

Test rigoroso per verificare che il parser rifiuti correttamente
pattern sintatticamente invalidi e mantenga standard assoluti.

OBIETTIVO: Rejection rate ≥95% su pattern invalidi
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.conceptual_parser import create_conceptual_parser, SemanticParseError


class ParserRigorTester:
    """Tester rigoroso per parser NEUROGLYPH."""
    
    def __init__(self):
        self.tokenizer = create_conceptual_tokenizer()
        self.parser = create_conceptual_parser(self.tokenizer)
    
    def test_parser_rigor(self):
        """Test rigoroso del parser con pattern invalidi."""
        print("🔥 NEUROGLYPH PARSER RIGOR TEST")
        print("="*80)
        print("OBJECTIVE: Rejection rate ≥95% on invalid patterns")
        print("STANDARD: Zero tolerance for syntactic errors")
        print("="*80)
        
        # Pattern che DEVONO essere rifiutati
        invalid_inputs = [
            # Pattern sintatticamente malformati
            ("BROKEN_SYNTAX", "Broken Syntax Token"),
            ("¬∨P", "Malformed Negation"),
            ("∀∃x", "Malformed Quantifier Sequence"),
            ("P ⇒", "Incomplete Implication"),
            ("∧ Q", "Leading Conjunction"),
            ("P ∨", "Trailing Disjunction"),
            ("x =", "Incomplete Equality"),
            ("≠ y", "Leading Inequality"),
            
            # Operatori isolati
            ("∑", "Isolated Summation"),
            ("∏", "Isolated Product"),
            ("∫", "Isolated Integral"),
            ("∂", "Isolated Partial"),
            ("∀", "Isolated Universal"),
            ("∃", "Isolated Existential"),
            ("∧", "Isolated Conjunction"),
            ("∨", "Isolated Disjunction"),
            ("⇒", "Isolated Implication"),
            ("¬", "Isolated Negation"),
            
            # Integrali malformati
            ("∫ ∈ ℝ", "Integral Without Function"),
            ("∫ f", "Incomplete Integral"),
            ("∫ dx", "Integral Without Function 2"),
            
            # Funzioni malformate
            ("f()", "Function Without Argument"),
            ("(x)", "Parentheses Without Function"),
            ("f(x,)", "Trailing Comma"),
            
            # Quantificatori malformati
            ("∀ ∈ ℕ: x", "Quantifier Missing Variable"),
            ("∀x ∈: P", "Missing Domain"),
            ("∀x: ", "Missing Body"),
            ("∃: P(x)", "Missing Variable"),
            
            # Set theory malformata
            ("A ∪", "Incomplete Union"),
            ("∩ B", "Leading Intersection"),
            ("x ∈", "Incomplete Membership"),
            ("⊆ B", "Leading Subset"),
            
            # Pattern vuoti/spazi
            ("", "Empty String"),
            ("   ", "Whitespace Only"),
            ("\t\n", "Tab Newline Only"),
            
            # Doppi operatori
            ("A ∪ ∩ B", "Double Set Operators"),
            ("P ∧ ∨ Q", "Double Logic Operators"),
            ("x = ≠ y", "Double Equality Operators"),
            ("∀∀x: P", "Double Universal"),
            ("∃∃x: P", "Double Existential"),
            
            # Parentesi malformate
            ("(P ∨ Q", "Unclosed Parenthesis"),
            ("P ∨ Q)", "Unopened Parenthesis"),
            ("((P))", "Double Parentheses"),
            ("()", "Empty Parentheses"),
            
            # Simboli non validi come variabili
            ("∧", "Conjunction As Variable"),
            ("∨", "Disjunction As Variable"),
            ("⇒", "Implication As Variable"),
            ("¬", "Negation As Variable"),
            ("∀", "Universal As Variable"),
            ("∃", "Existential As Variable"),
            ("∫", "Integral As Variable"),
            ("∑", "Summation As Variable"),
            ("∏", "Product As Variable"),
            ("∂", "Partial As Variable"),
        ]
        
        print(f"\n🧪 TESTING {len(invalid_inputs)} INVALID PATTERNS")
        
        correctly_rejected = 0
        incorrectly_accepted = []
        
        for i, (pattern, description) in enumerate(invalid_inputs, 1):
            result = self._test_invalid_pattern(pattern, description)
            
            if result['correctly_rejected']:
                correctly_rejected += 1
                print(f"  [{i:2d}] ✅ {description}: CORRECTLY REJECTED")
                if result['error_type']:
                    print(f"      Error: {result['error_type']}")
            else:
                incorrectly_accepted.append(result)
                print(f"  [{i:2d}] ❌ {description}: INCORRECTLY ACCEPTED")
                print(f"      Input: '{pattern}'")
                if result['output']:
                    print(f"      Output: '{result['output']}'")
        
        # Calcola rejection rate
        total_invalid = len(invalid_inputs)
        rejection_rate = correctly_rejected / total_invalid
        
        print(f"\n" + "="*80)
        print("🎯 PARSER RIGOR ASSESSMENT")
        print("="*80)
        
        print(f"📊 INVALID PATTERNS TESTED: {total_invalid}")
        print(f"📊 CORRECTLY REJECTED: {correctly_rejected}")
        print(f"📊 INCORRECTLY ACCEPTED: {len(incorrectly_accepted)}")
        print(f"📊 REJECTION RATE: {rejection_rate:.1%}")
        
        # Standard rigoroso
        target_rejection_rate = 0.95
        
        print(f"\n🎯 RIGOROUS STANDARD:")
        print(f"  Target Rejection Rate: ≥{target_rejection_rate:.1%}")
        print(f"  Achieved Rejection Rate: {rejection_rate:.1%}")
        
        # Assessment finale
        meets_standard = rejection_rate >= target_rejection_rate
        
        if meets_standard:
            print(f"\n✅ PARSER RIGOR: MAINTAINED")
            print(f"✅ REJECTION STANDARD: MET")
            print(f"✅ ZERO TOLERANCE: ENFORCED")
            print(f"✅ READY FOR PRODUCTION")
        else:
            print(f"\n❌ PARSER RIGOR: COMPROMISED")
            print(f"❌ REJECTION STANDARD: FAILED")
            print(f"❌ ZERO TOLERANCE: VIOLATED")
            print(f"❌ NOT READY FOR PRODUCTION")
            
            print(f"\n🔧 INCORRECTLY ACCEPTED PATTERNS:")
            for result in incorrectly_accepted:
                print(f"  • {result['description']}: '{result['pattern']}'")
                if result['output']:
                    print(f"    Output: '{result['output']}'")
        
        print(f"\n" + "="*80)
        
        return meets_standard, rejection_rate
    
    def _test_invalid_pattern(self, pattern: str, description: str) -> dict:
        """Testa singolo pattern invalido."""
        result = {
            'pattern': pattern,
            'description': description,
            'correctly_rejected': False,
            'error_type': None,
            'output': None
        }
        
        try:
            # Prova parsing
            ast = self.parser.parse(pattern)
            
            if ast and ast.root_concept:
                # Se parsing riesce, prova serializzazione
                try:
                    output = ast.to_neuroglyph()
                    result['output'] = output
                    
                    # Pattern invalido NON dovrebbe mai produrre output valido
                    result['correctly_rejected'] = False
                    
                except Exception as e:
                    # Serializzazione fallita = corretto per pattern invalidi
                    result['correctly_rejected'] = True
                    result['error_type'] = f"Serialization error: {type(e).__name__}"
            else:
                # Parsing fallito = corretto per pattern invalidi
                result['correctly_rejected'] = True
                result['error_type'] = "Parse failed - no AST"
        
        except SemanticParseError as e:
            # Errore semantico = corretto per pattern invalidi
            result['correctly_rejected'] = True
            result['error_type'] = f"SemanticParseError: {str(e)[:50]}..."
        except Exception as e:
            # Altri errori = corretto per pattern invalidi
            result['correctly_rejected'] = True
            result['error_type'] = f"{type(e).__name__}: {str(e)[:50]}..."
        
        return result


def main():
    """Main entry point."""
    tester = ParserRigorTester()
    
    print("🚨 STARTING PARSER RIGOR TEST")
    print("⚠️  ZERO TOLERANCE FOR INVALID PATTERNS")
    print("🎯 TARGET: ≥95% rejection rate")
    
    meets_standard, rejection_rate = tester.test_parser_rigor()
    
    if meets_standard:
        print(f"\n🎉 SUCCESS: Parser rigor maintained!")
        print(f"📊 Rejection rate: {rejection_rate:.1%}")
        print(f"✅ Ready for certified fine-tuning")
        sys.exit(0)
    else:
        print(f"\n💥 FAILURE: Parser rigor compromised!")
        print(f"📊 Rejection rate: {rejection_rate:.1%}")
        print(f"❌ Must fix before fine-tuning")
        sys.exit(1)


if __name__ == "__main__":
    main()
