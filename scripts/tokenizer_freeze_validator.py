#!/usr/bin/env python3
"""
NEUROGLYPH Tokenizer Freeze Validator
Congela tokenizer.json e valida SHA-256 durante training.

SECURITY FEATURES:
- SHA-256 signature validation
- Immutable tokenizer state
- Training interruption su modifiche
- Audit trail completo
"""

import json
import hashlib
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

class TokenizerFreezeValidator:
    """Validatore per tokenizer congelato."""
    
    def __init__(self, tokenizer_dir: str = "neuroglyph_isolated_tokenizer"):
        self.tokenizer_dir = Path(tokenizer_dir)
        self.tokenizer_json_path = self.tokenizer_dir / "tokenizer.json"
        self.config_path = self.tokenizer_dir / "tokenizer_config.json"
        self.freeze_manifest_path = self.tokenizer_dir / "freeze_manifest.json"
        
        # Expected SHA-256 signatures (loaded from manifest or placeholders)
        self.expected_signatures = self._load_expected_signatures()

    def _load_expected_signatures(self) -> Dict[str, str]:
        """Load expected signatures from manifest or return placeholders."""
        if self.freeze_manifest_path.exists():
            try:
                with open(self.freeze_manifest_path, 'r', encoding='utf-8') as f:
                    manifest = json.load(f)
                return manifest.get('signatures', {
                    "tokenizer.json": "PLACEHOLDER_TOKENIZER_SHA256",
                    "tokenizer_config.json": "PLACEHOLDER_CONFIG_SHA256"
                })
            except Exception:
                pass

        return {
            "tokenizer.json": "PLACEHOLDER_TOKENIZER_SHA256",
            "tokenizer_config.json": "PLACEHOLDER_CONFIG_SHA256"
        }

    def calculate_file_sha256(self, file_path: Path) -> str:
        """Calcola SHA-256 di un file."""
        if not file_path.exists():
            return "FILE_NOT_FOUND"
        
        with open(file_path, 'rb') as f:
            return hashlib.sha256(f.read()).hexdigest()
    
    def create_freeze_manifest(self) -> Dict[str, Any]:
        """Crea manifest di congelamento."""
        
        if not self.tokenizer_json_path.exists():
            raise FileNotFoundError(f"Tokenizer not found: {self.tokenizer_json_path}")
        
        # Calculate signatures
        tokenizer_sha = self.calculate_file_sha256(self.tokenizer_json_path)
        config_sha = self.calculate_file_sha256(self.config_path)
        
        # Load tokenizer to extract NEUROGLYPH symbols
        with open(self.tokenizer_json_path, 'r', encoding='utf-8') as f:
            tokenizer_data = json.load(f)
        
        # Extract added tokens (NEUROGLYPH symbols)
        added_tokens = tokenizer_data.get('added_tokens', [])
        neuroglyph_symbols = [token['content'] for token in added_tokens 
                             if token.get('special', False)]
        
        manifest = {
            "freeze_timestamp": datetime.now().isoformat(),
            "freeze_version": "1.0",
            "tokenizer_dir": str(self.tokenizer_dir),
            "signatures": {
                "tokenizer.json": tokenizer_sha,
                "tokenizer_config.json": config_sha
            },
            "neuroglyph_symbols": neuroglyph_symbols,
            "symbol_count": len(neuroglyph_symbols),
            "immutable": True,
            "validation_required": True
        }
        
        # Save manifest
        with open(self.freeze_manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Freeze manifest created: {self.freeze_manifest_path}")
        print(f"   Tokenizer SHA-256: {tokenizer_sha}")
        print(f"   Config SHA-256: {config_sha}")
        print(f"   NEUROGLYPH symbols: {len(neuroglyph_symbols)}")
        
        return manifest
    
    def validate_freeze_integrity(self) -> Dict[str, Any]:
        """Valida integrità del tokenizer congelato."""
        
        if not self.freeze_manifest_path.exists():
            return {
                "valid": False,
                "error": "Freeze manifest not found",
                "action": "Run create_freeze_manifest() first"
            }
        
        # Load manifest
        with open(self.freeze_manifest_path, 'r', encoding='utf-8') as f:
            manifest = json.load(f)
        
        violations = []
        
        # Check file existence
        if not self.tokenizer_json_path.exists():
            violations.append("tokenizer.json not found")
        
        if not self.config_path.exists():
            violations.append("tokenizer_config.json not found")
        
        if violations:
            return {
                "valid": False,
                "violations": violations,
                "action": "Restore missing tokenizer files"
            }
        
        # Validate SHA-256 signatures
        expected_sigs = manifest.get('signatures', {})
        
        tokenizer_sha = self.calculate_file_sha256(self.tokenizer_json_path)
        config_sha = self.calculate_file_sha256(self.config_path)
        
        if tokenizer_sha != expected_sigs.get('tokenizer.json'):
            violations.append(f"tokenizer.json modified: expected {expected_sigs.get('tokenizer.json')}, got {tokenizer_sha}")
        
        if config_sha != expected_sigs.get('tokenizer_config.json'):
            violations.append(f"tokenizer_config.json modified: expected {expected_sigs.get('tokenizer_config.json')}, got {config_sha}")
        
        # Validate NEUROGLYPH symbols
        with open(self.tokenizer_json_path, 'r', encoding='utf-8') as f:
            tokenizer_data = json.load(f)
        
        added_tokens = tokenizer_data.get('added_tokens', [])
        current_symbols = [token['content'] for token in added_tokens 
                          if token.get('special', False)]
        
        expected_symbols = set(manifest.get('neuroglyph_symbols', []))
        current_symbols_set = set(current_symbols)
        
        if current_symbols_set != expected_symbols:
            missing = expected_symbols - current_symbols_set
            extra = current_symbols_set - expected_symbols
            
            if missing:
                violations.append(f"Missing NEUROGLYPH symbols: {missing}")
            if extra:
                violations.append(f"Unexpected symbols added: {extra}")
        
        # Result
        if violations:
            return {
                "valid": False,
                "violations": violations,
                "manifest": manifest,
                "action": "Restore tokenizer from backup or regenerate"
            }
        else:
            return {
                "valid": True,
                "manifest": manifest,
                "validation_timestamp": datetime.now().isoformat(),
                "message": "Tokenizer freeze integrity verified"
            }
    
    def training_pre_check(self) -> bool:
        """Pre-check obbligatorio prima del training."""
        print("🔒 NEUROGLYPH Tokenizer Freeze Validation")
        print("=" * 60)
        
        result = self.validate_freeze_integrity()
        
        if result["valid"]:
            print("✅ TOKENIZER FREEZE VALIDATION PASSED")
            print(f"   Freeze timestamp: {result['manifest']['freeze_timestamp']}")
            print(f"   NEUROGLYPH symbols: {result['manifest']['symbol_count']}")
            print(f"   Validation timestamp: {result['validation_timestamp']}")
            print("🚀 Training can proceed safely")
            return True
        else:
            print("❌ TOKENIZER FREEZE VALIDATION FAILED")
            print("🚨 CRITICAL: Tokenizer integrity compromised")
            
            for violation in result.get("violations", []):
                print(f"   - {violation}")
            
            print(f"\n🔧 Action required: {result.get('action', 'Unknown')}")
            print("🛑 TRAINING BLOCKED until tokenizer is restored")
            return False
    
    def create_training_guard(self) -> str:
        """Crea guard code per training script."""
        
        guard_code = f'''
# NEUROGLYPH TOKENIZER FREEZE GUARD
# Auto-generated - DO NOT MODIFY

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from tokenizer_freeze_validator import TokenizerFreezeValidator

def validate_tokenizer_freeze():
    """Mandatory tokenizer validation before training."""
    validator = TokenizerFreezeValidator()
    
    if not validator.training_pre_check():
        print("💥 TRAINING ABORTED: Tokenizer freeze validation failed")
        print("🔧 Fix tokenizer integrity before proceeding")
        sys.exit(1)
    
    print("✅ Tokenizer freeze validation passed - training authorized")

# MANDATORY: Call before any training operations
validate_tokenizer_freeze()
'''
        
        guard_path = Path("training_tokenizer_guard.py")
        with open(guard_path, 'w', encoding='utf-8') as f:
            f.write(guard_code)
        
        print(f"🛡️ Training guard created: {guard_path}")
        print("📋 Add 'import training_tokenizer_guard' to training scripts")
        
        return str(guard_path)

def main():
    """Main tokenizer freeze operations."""
    import argparse
    
    parser = argparse.ArgumentParser(description="NEUROGLYPH Tokenizer Freeze Validator")
    parser.add_argument("--action", choices=["freeze", "validate", "guard"], 
                       required=True, help="Action to perform")
    parser.add_argument("--tokenizer-dir", default="neuroglyph_isolated_tokenizer",
                       help="Tokenizer directory path")
    
    args = parser.parse_args()
    
    validator = TokenizerFreezeValidator(args.tokenizer_dir)
    
    if args.action == "freeze":
        print("🧊 Creating tokenizer freeze manifest...")
        manifest = validator.create_freeze_manifest()
        print("✅ Tokenizer frozen successfully")
        
    elif args.action == "validate":
        print("🔍 Validating tokenizer freeze integrity...")
        success = validator.training_pre_check()
        sys.exit(0 if success else 1)
        
    elif args.action == "guard":
        print("🛡️ Creating training guard...")
        guard_path = validator.create_training_guard()
        print(f"✅ Guard created: {guard_path}")

if __name__ == "__main__":
    main()
