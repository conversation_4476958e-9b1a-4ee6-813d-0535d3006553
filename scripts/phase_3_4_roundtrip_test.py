#!/usr/bin/env python3
"""
NEUROGLYPH Phase 3.4 - Roundtrip Test Completo

Testa il roundtrip completo con parser esteso e AST migliorati.
Implementa normalizzazione e fuzzy equivalence per raggiungere ≥95% fidelity.

Principi Immutabili:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail

Usage:
    python3 scripts/phase_3_4_roundtrip_test.py
    python3 scripts/phase_3_4_roundtrip_test.py --full-dataset
"""

import argparse
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from difflib import SequenceMatcher
import sys
import re

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import (
    ConceptRegistry, create_conceptual_tokenizer
)
from neuroglyph.conceptual.parser.conceptual_parser import (
    create_conceptual_parser, SemanticParseError
)


class RoundtripTester:
    """
    Tester completo per roundtrip NEUROGLYPH con normalizzazione e fuzzy matching.
    
    Implementa validazione rigorosa secondo principi immutabili.
    """
    
    def __init__(self, registry_path: str = "data/neuroglyph_registry_phase_2_1.json"):
        self.registry_path = Path(registry_path)
        
        # Inizializza sistema concettuale con registry aggiornato
        self.registry = ConceptRegistry(str(self.registry_path))
        self.tokenizer = create_conceptual_tokenizer()
        self.parser = create_conceptual_parser(self.tokenizer)
        
        # Estendi parser con pattern avanzati
        self._extend_parser()
        
        # Statistiche test
        self.test_stats = {
            'total_tests': 0,
            'parsing_successes': 0,
            'roundtrip_successes': 0,
            'exact_matches': 0,
            'fuzzy_matches': 0,
            'failures': [],
            'parsing_rate': 0.0,
            'roundtrip_rate': 0.0,
            'exact_match_rate': 0.0,
            'fuzzy_match_rate': 0.0,
            'overall_fidelity': 0.0
        }
    
    def _extend_parser(self):
        """Estende parser con pattern avanzati (versione semplificata)."""
        # Per ora usiamo il parser base - l'estensione completa richiederebbe
        # integrazione con il sistema di pattern del Phase 3.2
        print("🔧 Using base parser (extended patterns integration pending)")
    
    def test_roundtrip_examples(self) -> Dict[str, Any]:
        """Testa esempi specifici di roundtrip."""
        print("🧪 Testing specific roundtrip examples...")
        
        # Test cases mirati per pattern problematici
        test_cases = [
            # Quantificatori semplici
            "∀x: P(x)",
            "∃x: P(x)",
            
            # Logica base
            "P ⇒ Q",
            "P ∧ Q",
            "P ∨ Q",
            "¬P",
            
            # Set theory
            "A ∪ B",
            "A ∩ B", 
            "x ∈ A",
            "A ⊆ B",
            
            # Matematica
            "x + y",
            "f(x)",
            "x = y",
            "x ≠ y",
            "x ≤ y",
            "x ≥ y",
            
            # Espressioni complesse (che potrebbero fallire)
            "∀x ∈ ℝ: P(x)",
            "∃x ∈ ℕ: P(x)",
            "∀x ∃y: R(x,y)",
            "P ∧ Q ⇒ R",
            "¬(P ∨ Q)",
            
            # Matematica avanzata (che probabilmente falliranno)
            "∫ f(x) dx",
            "∑ xᵢ",
            "∏ xᵢ",
            "∂f/∂x"
        ]
        
        results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S UTC'),
            'total_cases': len(test_cases),
            'test_details': [],
            'summary': {}
        }
        
        for i, test_case in enumerate(test_cases):
            print(f"Testing {i+1}/{len(test_cases)}: {test_case}")
            
            test_result = self._test_single_roundtrip(test_case)
            results['test_details'].append(test_result)
            
            # Aggiorna statistiche
            self.test_stats['total_tests'] += 1
            
            if test_result['parsing_success']:
                self.test_stats['parsing_successes'] += 1
                
                if test_result['roundtrip_success']:
                    self.test_stats['roundtrip_successes'] += 1
                    
                    if test_result['exact_match']:
                        self.test_stats['exact_matches'] += 1
                    elif test_result['fuzzy_match']:
                        self.test_stats['fuzzy_matches'] += 1
            
            if test_result['error']:
                self.test_stats['failures'].append({
                    'input': test_case,
                    'error': test_result['error']
                })
        
        # Calcola metriche finali
        self._calculate_final_metrics()
        results['summary'] = self._get_summary_stats()
        
        return results
    
    def _test_single_roundtrip(self, input_text: str) -> Dict[str, Any]:
        """Testa singolo roundtrip."""
        result = {
            'input': input_text,
            'parsing_success': False,
            'roundtrip_success': False,
            'exact_match': False,
            'fuzzy_match': False,
            'output': None,
            'similarity_score': 0.0,
            'error': None,
            'ast_type': None
        }
        
        try:
            # Step 1: Parsing
            ast = self.parser.parse(input_text)
            
            if ast and ast.root_concept:
                result['parsing_success'] = True
                result['ast_type'] = type(ast.root_concept).__name__
                
                # Step 2: Serializzazione
                try:
                    output = ast.to_neuroglyph()
                    result['output'] = output
                    
                    # Step 3: Confronto
                    exact_match = self._exact_match(input_text, output)
                    fuzzy_match, similarity = self._fuzzy_match(input_text, output)
                    
                    result['exact_match'] = exact_match
                    result['fuzzy_match'] = fuzzy_match
                    result['similarity_score'] = similarity
                    result['roundtrip_success'] = exact_match or fuzzy_match
                    
                except Exception as e:
                    result['error'] = f"Serialization error: {e}"
            else:
                result['error'] = "Parsing failed - no AST generated"
        
        except SemanticParseError as e:
            result['error'] = f"Semantic parse error: {e}"
        except Exception as e:
            result['error'] = f"Unexpected error: {e}"
        
        return result
    
    def _exact_match(self, original: str, reconstructed: str) -> bool:
        """Verifica match esatto."""
        return original.strip() == reconstructed.strip()
    
    def _fuzzy_match(self, original: str, reconstructed: str, threshold: float = 0.90) -> tuple[bool, float]:
        """Verifica match fuzzy con normalizzazione."""
        # Normalizza entrambe le stringhe
        orig_norm = self._normalize_text(original)
        recon_norm = self._normalize_text(reconstructed)
        
        # Calcola similarità
        similarity = SequenceMatcher(None, orig_norm, recon_norm).ratio()
        
        return similarity >= threshold, similarity
    
    def _normalize_text(self, text: str) -> str:
        """Normalizza testo per confronto fuzzy."""
        # Rimuovi spazi extra
        normalized = re.sub(r'\s+', ' ', text.strip())
        
        # Normalizza parentesi
        normalized = re.sub(r'\s*\(\s*', '(', normalized)
        normalized = re.sub(r'\s*\)\s*', ')', normalized)
        
        # Normalizza operatori
        normalized = re.sub(r'\s*:\s*', ': ', normalized)
        normalized = re.sub(r'\s*∈\s*', ' ∈ ', normalized)
        normalized = re.sub(r'\s*⇒\s*', ' ⇒ ', normalized)
        normalized = re.sub(r'\s*∧\s*', ' ∧ ', normalized)
        normalized = re.sub(r'\s*∨\s*', ' ∨ ', normalized)
        
        return normalized.strip()
    
    def _calculate_final_metrics(self):
        """Calcola metriche finali."""
        total = self.test_stats['total_tests']
        
        if total > 0:
            self.test_stats['parsing_rate'] = self.test_stats['parsing_successes'] / total
            self.test_stats['roundtrip_rate'] = self.test_stats['roundtrip_successes'] / total
            self.test_stats['exact_match_rate'] = self.test_stats['exact_matches'] / total
            self.test_stats['fuzzy_match_rate'] = self.test_stats['fuzzy_matches'] / total
            
            # Fidelity complessiva (peso maggiore a exact match)
            self.test_stats['overall_fidelity'] = (
                self.test_stats['exact_match_rate'] * 0.7 +
                self.test_stats['fuzzy_match_rate'] * 0.3
            )
    
    def _get_summary_stats(self) -> Dict[str, Any]:
        """Ottieni statistiche riassuntive."""
        return {
            'total_tests': self.test_stats['total_tests'],
            'parsing_rate': self.test_stats['parsing_rate'],
            'roundtrip_rate': self.test_stats['roundtrip_rate'],
            'exact_match_rate': self.test_stats['exact_match_rate'],
            'fuzzy_match_rate': self.test_stats['fuzzy_match_rate'],
            'overall_fidelity': self.test_stats['overall_fidelity'],
            'failures_count': len(self.test_stats['failures']),
            'quality_assessment': self._assess_quality()
        }
    
    def _assess_quality(self) -> str:
        """Valuta qualità complessiva."""
        fidelity = self.test_stats['overall_fidelity']
        
        if fidelity >= 0.95:
            return "EXCELLENT"
        elif fidelity >= 0.85:
            return "GOOD"
        elif fidelity >= 0.70:
            return "ACCEPTABLE"
        elif fidelity >= 0.50:
            return "POOR"
        else:
            return "CRITICAL"
    
    def test_dataset_sample(self, sample_size: int = 100) -> Dict[str, Any]:
        """Testa campione del dataset."""
        print(f"🧪 Testing dataset sample ({sample_size} examples)...")
        
        # Carica dataset
        dataset_path = Path("data/datasets/symbolic/neuroglyph_symbolic_final.jsonl")
        examples = []
        
        with open(dataset_path, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    example = json.loads(line.strip())
                    examples.append(example)
                    if len(examples) >= sample_size:
                        break
                except json.JSONDecodeError:
                    continue
        
        print(f"📄 Loaded {len(examples)} examples")
        
        # Testa ogni esempio
        results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S UTC'),
            'sample_size': len(examples),
            'test_details': [],
            'summary': {}
        }
        
        for i, example in enumerate(examples):
            if i % 20 == 0:
                print(f"Testing {i}/{len(examples)} examples...")
            
            # Testa prompt_symbolic
            prompt = example.get('prompt_symbolic', '')
            if prompt:
                test_result = self._test_single_roundtrip(prompt)
                test_result['source'] = 'prompt'
                test_result['example_id'] = example.get('id', i)
                results['test_details'].append(test_result)
                
                # Aggiorna statistiche
                self._update_stats_from_result(test_result)
        
        # Calcola metriche finali
        self._calculate_final_metrics()
        results['summary'] = self._get_summary_stats()
        
        return results
    
    def _update_stats_from_result(self, result: Dict[str, Any]):
        """Aggiorna statistiche da risultato singolo."""
        self.test_stats['total_tests'] += 1
        
        if result['parsing_success']:
            self.test_stats['parsing_successes'] += 1
            
            if result['roundtrip_success']:
                self.test_stats['roundtrip_successes'] += 1
                
                if result['exact_match']:
                    self.test_stats['exact_matches'] += 1
                elif result['fuzzy_match']:
                    self.test_stats['fuzzy_matches'] += 1
        
        if result['error']:
            self.test_stats['failures'].append({
                'input': result['input'],
                'error': result['error']
            })
    
    def print_results(self, results: Dict[str, Any]):
        """Stampa risultati formattati."""
        print("\n" + "="*80)
        print("🔄 NEUROGLYPH ROUNDTRIP TEST RESULTS")
        print("="*80)
        
        summary = results['summary']
        
        print(f"📊 CORE METRICS:")
        print(f"  Total Tests: {summary['total_tests']}")
        print(f"  Parsing Rate: {summary['parsing_rate']:.1%}")
        print(f"  Roundtrip Rate: {summary['roundtrip_rate']:.1%}")
        print(f"  Exact Match Rate: {summary['exact_match_rate']:.1%}")
        print(f"  Fuzzy Match Rate: {summary['fuzzy_match_rate']:.1%}")
        print(f"  Overall Fidelity: {summary['overall_fidelity']:.1%}")
        
        print(f"\n🎯 QUALITY ASSESSMENT: {summary['quality_assessment']}")
        
        # Criteri di successo
        target_fidelity = 0.95
        success = summary['overall_fidelity'] >= target_fidelity
        
        print(f"\n✅ SUCCESS CRITERIA:")
        print(f"  Target Fidelity: {target_fidelity:.1%}")
        print(f"  Achieved: {summary['overall_fidelity']:.1%}")
        print(f"  Status: {'✅ PASSED' if success else '❌ FAILED'}")
        
        if summary['failures_count'] > 0:
            print(f"\n🔍 FAILURES: {summary['failures_count']}")
            
            # Mostra primi 5 failure
            for i, failure in enumerate(self.test_stats['failures'][:5]):
                print(f"  {i+1}. {failure['input']}: {failure['error']}")
        
        print("\n" + "="*80)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='NEUROGLYPH Roundtrip Test')
    parser.add_argument('--examples', action='store_true', help='Test specific examples')
    parser.add_argument('--dataset', action='store_true', help='Test dataset sample')
    parser.add_argument('--sample-size', type=int, default=100, help='Dataset sample size')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    
    args = parser.parse_args()
    
    tester = RoundtripTester()
    
    try:
        if args.examples or args.all:
            print("🧪 Phase 3.4: Specific Examples Test")
            results = tester.test_roundtrip_examples()
            tester.print_results(results)
        
        if args.dataset or args.all:
            print("\n🧪 Phase 3.4: Dataset Sample Test")
            results = tester.test_dataset_sample(args.sample_size)
            tester.print_results(results)
        
        if not any([args.examples, args.dataset, args.all]):
            # Default: test examples
            results = tester.test_roundtrip_examples()
            tester.print_results(results)
        
        print("\n🎉 ROUNDTRIP TESTING COMPLETED!")
        
    except Exception as e:
        print(f"💥 Test error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
