#!/usr/bin/env python3
"""
NEUROGLYPH Coverage Analyzer
Analizza copertura simbolica e identifica gap per espansione di eccellenza

Funzionalità:
- Analisi copertura simbolica attuale
- Identificazione gap critici
- Metriche di diversità
- Report di progresso verso target di eccellenza
"""

import json
import statistics
from pathlib import Path
from typing import Dict, List, Tuple
from collections import Counter, defaultdict
from dataclasses import asdict

from excellence_expansion_config import ExcellenceConfig, CoverageReport, QualityMetrics


class CoverageAnalyzer:
    """Analizzatore di copertura simbolica per dataset NEUROGLYPH."""
    
    def __init__(self, config: ExcellenceConfig):
        """
        Inizializza analyzer con configurazione di eccellenza.
        
        Args:
            config: Configurazione target di eccellenza
        """
        self.config = config
        self.target_coverage = config.get_total_symbol_targets()
    
    def analyze_dataset(self, dataset_path: str) -> CoverageReport:
        """
        Analizza copertura simbolica del dataset.
        
        Args:
            dataset_path: Percorso al dataset JSONL
            
        Returns:
            Report di copertura completo
        """
        print(f"📊 Analyzing coverage for: {dataset_path}")
        
        # Inizializza contatori
        symbol_counts = Counter()
        difficulty_counts = Counter()
        prompt_lengths = []
        reasoning_steps = []
        total_examples = 0
        
        # Analizza dataset
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        example = json.loads(line.strip())
                        total_examples += 1
                        
                        # Conta simboli utilizzati
                        symbols_used = example.get('symbols_used', [])
                        for symbol in symbols_used:
                            symbol_counts[symbol] += 1
                        
                        # Conta difficoltà
                        difficulty = example.get('difficulty', 'unknown')
                        difficulty_counts[difficulty] += 1
                        
                        # Metriche di diversità
                        prompt_symbolic = example.get('prompt_symbolic', '')
                        prompt_lengths.append(len(prompt_symbolic))
                        
                        steps = example.get('reasoning_steps', 0)
                        reasoning_steps.append(steps)
        
        except Exception as e:
            print(f"❌ Error analyzing dataset: {e}")
            return CoverageReport()
        
        # Calcola gap di copertura
        coverage_gaps = {}
        for symbol, target in self.target_coverage.items():
            current = symbol_counts.get(symbol, 0)
            gap = max(0, target - current)
            coverage_gaps[symbol] = gap
        
        # Calcola metriche di diversità
        avg_prompt_length = statistics.mean(prompt_lengths) if prompt_lengths else 0
        prompt_length_variance = statistics.variance(prompt_lengths) if len(prompt_lengths) > 1 else 0
        avg_reasoning_steps = statistics.mean(reasoning_steps) if reasoning_steps else 0
        
        # Crea report
        report = CoverageReport(
            total_examples=total_examples,
            symbols_coverage=dict(symbol_counts),
            target_coverage=self.target_coverage,
            coverage_gaps=coverage_gaps,
            avg_prompt_length=avg_prompt_length,
            prompt_length_variance=prompt_length_variance,
            avg_reasoning_steps=avg_reasoning_steps,
            difficulty_distribution=dict(difficulty_counts)
        )
        
        print(f"✅ Analysis completed: {total_examples} examples analyzed")
        return report
    
    def print_coverage_summary(self, report: CoverageReport):
        """Stampa summary della copertura."""
        print(f"\n📊 COVERAGE SUMMARY")
        print("=" * 60)
        print(f"📈 Total examples: {report.total_examples:,}")
        print(f"🎯 Coverage percentage: {report.calculate_coverage_percentage():.1%}")
        
        # Top simboli con più esempi
        top_symbols = sorted(report.symbols_coverage.items(), 
                           key=lambda x: x[1], reverse=True)[:10]
        print(f"\n🔝 TOP SYMBOLS BY COVERAGE:")
        for symbol, count in top_symbols:
            target = report.target_coverage.get(symbol, 0)
            percentage = (count / target * 100) if target > 0 else 0
            print(f"   {symbol}: {count:,} / {target:,} ({percentage:.1f}%)")
        
        # Gap critici
        priority_gaps = report.get_priority_gaps(10)
        if priority_gaps:
            print(f"\n⚠️ PRIORITY GAPS (Top 10):")
            for symbol, gap in priority_gaps:
                current = report.symbols_coverage.get(symbol, 0)
                target = report.target_coverage.get(symbol, 0)
                print(f"   {symbol}: need {gap:,} more ({current:,}/{target:,})")
        
        # Distribuzione difficoltà
        print(f"\n📊 DIFFICULTY DISTRIBUTION:")
        total = sum(report.difficulty_distribution.values())
        for difficulty, count in report.difficulty_distribution.items():
            percentage = (count / total * 100) if total > 0 else 0
            print(f"   {difficulty}: {count:,} ({percentage:.1f}%)")
        
        # Metriche diversità
        print(f"\n🎨 DIVERSITY METRICS:")
        print(f"   Avg prompt length: {report.avg_prompt_length:.1f} chars")
        print(f"   Prompt length variance: {report.prompt_length_variance:.1f}")
        print(f"   Avg reasoning steps: {report.avg_reasoning_steps:.1f}")
    
    def generate_expansion_plan(self, report: CoverageReport) -> Dict[str, int]:
        """
        Genera piano di espansione basato sui gap.
        
        Args:
            report: Report di copertura attuale
            
        Returns:
            Dizionario con simboli e numero di esempi da generare
        """
        expansion_plan = {}
        
        # Identifica gap critici
        priority_gaps = report.get_priority_gaps(20)  # Top 20 gap
        
        for symbol, gap in priority_gaps:
            if gap > 0:
                # Genera in batch per efficienza
                batch_size = min(gap, 500)  # Max 500 per batch
                expansion_plan[symbol] = batch_size
        
        return expansion_plan
    
    def save_coverage_report(self, report: CoverageReport, output_path: str):
        """Salva report di copertura in JSON."""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Converti in dict per serializzazione
        report_dict = asdict(report)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Coverage report saved: {output_file}")
    
    def compare_with_previous(self, current_report: CoverageReport, 
                            previous_report_path: str) -> Dict[str, int]:
        """
        Confronta con report precedente per vedere progressi.
        
        Args:
            current_report: Report attuale
            previous_report_path: Percorso al report precedente
            
        Returns:
            Dizionario con delta per simbolo
        """
        try:
            with open(previous_report_path, 'r', encoding='utf-8') as f:
                previous_data = json.load(f)
            
            previous_coverage = previous_data.get('symbols_coverage', {})
            current_coverage = current_report.symbols_coverage
            
            deltas = {}
            all_symbols = set(previous_coverage.keys()) | set(current_coverage.keys())
            
            for symbol in all_symbols:
                prev_count = previous_coverage.get(symbol, 0)
                curr_count = current_coverage.get(symbol, 0)
                delta = curr_count - prev_count
                if delta != 0:
                    deltas[symbol] = delta
            
            return deltas
            
        except Exception as e:
            print(f"⚠️ Could not compare with previous report: {e}")
            return {}


def main():
    """Pipeline principale di analisi copertura."""
    print("📊 NEUROGLYPH Coverage Analyzer")
    print("=" * 60)
    
    # Carica configurazione di eccellenza
    config = ExcellenceConfig()
    analyzer = CoverageAnalyzer(config)
    
    # Analizza dataset attuale (usa il più recente)
    final_path = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl"
    phase_20k_path = "data/datasets/symbolic/neuroglyph_symbolic_20k.jsonl"
    excellence_path = "data/datasets/symbolic/neuroglyph_symbolic_excellence.jsonl"
    expanded_path = "data/datasets/symbolic/neuroglyph_symbolic_expanded.jsonl"

    # Usa il dataset più recente disponibile
    from pathlib import Path
    if Path(final_path).exists():
        dataset_path = final_path
        print(f"📊 Using Phase 2A.3 FINAL dataset: {final_path}")
    elif Path(phase_20k_path).exists():
        dataset_path = phase_20k_path
        print(f"📊 Using Phase 2A.2 dataset: {phase_20k_path}")
    elif Path(excellence_path).exists():
        dataset_path = excellence_path
        print(f"📊 Using excellence dataset: {excellence_path}")
    else:
        dataset_path = expanded_path
        print(f"📊 Using expanded dataset: {expanded_path}")
    report = analyzer.analyze_dataset(dataset_path)
    
    # Stampa summary
    analyzer.print_coverage_summary(report)
    
    # Genera piano di espansione
    expansion_plan = analyzer.generate_expansion_plan(report)
    
    if expansion_plan:
        print(f"\n🚀 EXPANSION PLAN:")
        print("-" * 30)
        total_to_generate = sum(expansion_plan.values())
        print(f"Total examples to generate: {total_to_generate:,}")
        
        for symbol, count in sorted(expansion_plan.items(), 
                                  key=lambda x: x[1], reverse=True)[:10]:
            print(f"   {symbol}: +{count:,} examples")
    
    # Salva report
    report_path = "data/coverage/coverage_report.json"
    analyzer.save_coverage_report(report, report_path)
    
    # Confronta con precedente se esiste
    previous_report_path = "data/coverage/previous_coverage_report.json"
    if Path(previous_report_path).exists():
        deltas = analyzer.compare_with_previous(report, previous_report_path)
        if deltas:
            print(f"\n📈 PROGRESS SINCE LAST REPORT:")
            print("-" * 30)
            for symbol, delta in sorted(deltas.items(), 
                                      key=lambda x: x[1], reverse=True)[:10]:
                print(f"   {symbol}: {delta:+,} examples")
    
    # Calcola fase attuale
    coverage_percentage = report.calculate_coverage_percentage()
    current_phase = "Phase 2A.1"
    if report.total_examples >= 30000:
        current_phase = "Phase 2A.4"
    elif report.total_examples >= 20000:
        current_phase = "Phase 2A.3"
    elif report.total_examples >= 10000:
        current_phase = "Phase 2A.2"
    
    print(f"\n🎯 CURRENT STATUS:")
    print(f"   Phase: {current_phase}")
    print(f"   Examples: {report.total_examples:,} / {config.target_total_examples:,}")
    print(f"   Coverage: {coverage_percentage:.1%}")
    print(f"   Next milestone: {min(config.target_total_examples, ((report.total_examples // 10000) + 1) * 10000):,} examples")


if __name__ == "__main__":
    main()
