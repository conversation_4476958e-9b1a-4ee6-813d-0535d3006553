#!/usr/bin/env python3
"""
NEUROGLYPH - Debug Roundtrip Detailed

Analisi dettagliata per identificare esattamente quali pattern funzionano
e quali falliscono, mantenendo standard rigorosi ≥95% fidelity.

PRINCIPI IMMUTABILI NON NEGOZIABILI:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita (≥95%)
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.conceptual_parser import create_conceptual_parser, SemanticParseError
from difflib import SequenceMatcher


class RoundtripDebugger:
    """Debugger rigoroso per roundtrip con standard immutabili."""
    
    def __init__(self):
        self.tokenizer = create_conceptual_tokenizer()
        self.parser = create_conceptual_parser(self.tokenizer)
        self.min_fidelity = 0.95  # STANDARD IMMUTABILE
    
    def debug_all_patterns(self):
        """Debug dettagliato di tutti i pattern."""
        print("🔍 NEUROGLYPH ROUNDTRIP DEBUG - STANDARD RIGOROSI")
        print("="*80)
        print(f"MINIMUM FIDELITY REQUIRED: {self.min_fidelity:.1%} (NON-NEGOTIABLE)")
        print("="*80)
        
        test_cases = [
            # Quantificatori semplici
            "∀x: P(x)",
            "∃x: P(x)",
            
            # Logica base
            "P ⇒ Q",
            "P ∧ Q", 
            "P ∨ Q",
            "¬P",
            
            # Set theory
            "A ∪ B",
            "A ∩ B",
            "x ∈ A",
            "A ⊆ B",
            
            # Matematica base
            "x + y",
            "f(x)",
            "x = y",
            "x ≠ y",
            "x ≤ y",
            "x ≥ y",
            
            # Espressioni complesse
            "∀x ∈ ℝ: P(x)",
            "∃x ∈ ℕ: P(x)",
            "∀x ∃y: R(x,y)",
            "P ∧ Q ⇒ R",
            "¬(P ∨ Q)",
            
            # Matematica avanzata
            "∫ f(x) dx",
            "∑ xᵢ",
            "∏ xᵢ",
            "∂f/∂x"
        ]
        
        working_patterns = []
        broken_patterns = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n[{i:2d}/25] Testing: {test_case}")
            
            result = self._test_single_pattern(test_case)
            
            if result['meets_standard']:
                working_patterns.append(result)
                print(f"    ✅ PASS - Fidelity: {result['fidelity']:.1%}")
            else:
                broken_patterns.append(result)
                print(f"    ❌ FAIL - Fidelity: {result['fidelity']:.1%}")
                if result['error']:
                    print(f"    💥 Error: {result['error']}")
                if result['output']:
                    print(f"    📤 Output: {result['output']}")
        
        self._print_summary(working_patterns, broken_patterns)
        
        return working_patterns, broken_patterns
    
    def _test_single_pattern(self, input_text: str) -> dict:
        """Test rigoroso di singolo pattern."""
        result = {
            'input': input_text,
            'output': None,
            'fidelity': 0.0,
            'meets_standard': False,
            'parsing_success': False,
            'serialization_success': False,
            'error': None,
            'ast_type': None
        }
        
        try:
            # Step 1: Parsing
            ast = self.parser.parse(input_text)
            
            if ast and ast.root_concept:
                result['parsing_success'] = True
                result['ast_type'] = type(ast.root_concept).__name__
                
                # Step 2: Serialization
                try:
                    output = ast.to_neuroglyph()
                    result['output'] = output
                    result['serialization_success'] = True
                    
                    # Step 3: Fidelity calculation
                    fidelity = self._calculate_fidelity(input_text, output)
                    result['fidelity'] = fidelity
                    
                    # Step 4: Standard check (IMMUTABLE)
                    result['meets_standard'] = fidelity >= self.min_fidelity
                    
                except Exception as e:
                    result['error'] = f"Serialization failed: {e}"
            else:
                result['error'] = "Parsing failed - no AST"
        
        except SemanticParseError as e:
            result['error'] = f"Parse error: {e}"
        except Exception as e:
            result['error'] = f"Unexpected error: {e}"
        
        return result
    
    def _calculate_fidelity(self, original: str, reconstructed: str) -> float:
        """Calcolo rigoroso della fidelity."""
        # Exact match = 100% fidelity
        if original.strip() == reconstructed.strip():
            return 1.0
        
        # Normalized comparison
        orig_norm = self._normalize(original)
        recon_norm = self._normalize(reconstructed)
        
        if orig_norm == recon_norm:
            return 1.0
        
        # Sequence similarity
        similarity = SequenceMatcher(None, orig_norm, recon_norm).ratio()
        return similarity
    
    def _normalize(self, text: str) -> str:
        """Normalizzazione minimale per confronto."""
        import re
        # Solo normalizzazioni essenziali
        normalized = re.sub(r'\s+', ' ', text.strip())
        return normalized
    
    def _print_summary(self, working: list, broken: list):
        """Stampa summary rigoroso."""
        total = len(working) + len(broken)
        working_count = len(working)
        success_rate = working_count / total if total > 0 else 0
        
        print("\n" + "="*80)
        print("🔍 RIGOROUS ROUNDTRIP ANALYSIS SUMMARY")
        print("="*80)
        
        print(f"📊 RESULTS:")
        print(f"  Total Patterns: {total}")
        print(f"  Working Patterns: {working_count}")
        print(f"  Broken Patterns: {len(broken)}")
        print(f"  Success Rate: {success_rate:.1%}")
        
        print(f"\n✅ WORKING PATTERNS (≥{self.min_fidelity:.1%} fidelity):")
        if working:
            for pattern in working:
                print(f"  • {pattern['input']} → {pattern['output']} ({pattern['fidelity']:.1%})")
        else:
            print("  NONE - CRITICAL SYSTEM FAILURE")
        
        print(f"\n❌ BROKEN PATTERNS (<{self.min_fidelity:.1%} fidelity):")
        for pattern in broken[:10]:  # Show first 10
            print(f"  • {pattern['input']} → {pattern['output']} ({pattern['fidelity']:.1%})")
            if pattern['error']:
                print(f"    Error: {pattern['error']}")
        
        if len(broken) > 10:
            print(f"  ... and {len(broken) - 10} more")
        
        # IMMUTABLE STANDARD ASSESSMENT
        print(f"\n🎯 IMMUTABLE STANDARD ASSESSMENT:")
        print(f"  Required Success Rate: ≥95%")
        print(f"  Achieved Success Rate: {success_rate:.1%}")
        
        if success_rate >= 0.95:
            print(f"  Status: ✅ MEETS IMMUTABLE STANDARDS")
        else:
            print(f"  Status: ❌ FAILS IMMUTABLE STANDARDS")
            print(f"  Gap: {0.95 - success_rate:.1%} improvement needed")
        
        print(f"\n🚨 CRITICAL DECISION:")
        if success_rate >= 0.95:
            print("  ✅ PROCEED with fine-tuning")
        else:
            print("  ❌ MUST FIX serialization before proceeding")
            print("  🔧 Focus on broken patterns")
            print("  📊 No compromise on standards allowed")
        
        print("\n" + "="*80)


def main():
    """Main entry point."""
    debugger = RoundtripDebugger()
    
    print("🚨 STARTING RIGOROUS ROUNDTRIP DEBUG")
    print("⚠️  NO COMPROMISE ON IMMUTABLE STANDARDS")
    print("🎯 TARGET: ≥95% fidelity on ALL patterns")
    
    working, broken = debugger.debug_all_patterns()
    
    # Final assessment
    total = len(working) + len(broken)
    success_rate = len(working) / total if total > 0 else 0
    
    if success_rate >= 0.95:
        print("\n🎉 SUCCESS: System meets immutable standards!")
        sys.exit(0)
    else:
        print(f"\n💥 FAILURE: System fails immutable standards ({success_rate:.1%} < 95%)")
        print("🔧 REQUIRED ACTION: Fix serialization before proceeding")
        sys.exit(1)


if __name__ == "__main__":
    main()
