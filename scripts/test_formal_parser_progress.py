#!/usr/bin/env python3
"""
NEUROGLYPH - Test Formal Parser Progress

Test intermedio per verificare i progressi del parser formale
sui pattern precedentemente falliti.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.formal_parser import create_formal_parser, ParseError


def test_previously_failed_patterns():
    """Test pattern che fallivano prima."""
    print("🔧 TESTING PREVIOUSLY FAILED PATTERNS")
    print("="*60)
    
    tokenizer = create_conceptual_tokenizer()
    
    # Pattern che fallivano nella certificazione precedente
    test_patterns = [
        # Set operations
        ("A ∪ B", "Set Union"),
        ("A ∩ B", "Set Intersection"),
        ("x ∈ A", "Set Membership"),
        ("A ⊆ B", "Subset Relation"),
        
        # Mathematical operations
        ("x + y", "Addition"),
        ("x = y", "Equality"),
        ("x ≠ y", "Inequality"),
        
        # Calculus operations
        ("∫ f(x) dx", "Integral"),
        ("∑ xᵢ", "Summation"),
        ("∏ xᵢ", "Product"),
        ("∂f/∂x", "Partial Derivative"),
        
        # Multi-argument function
        ("f(x,y)", "Multi-Argument Function"),
    ]
    
    passed = 0
    for pattern, description in test_patterns:
        try:
            tokens = tokenizer.tokenize(pattern)
            parser = create_formal_parser(tokens)
            ast = parser.parse()
            
            if ast and ast.root_concept:
                # Test serialization
                try:
                    output = ast.to_neuroglyph()
                    print(f"  ✅ {description}: {pattern} → {output}")
                    passed += 1
                except Exception as e:
                    print(f"  🟡 {description}: {pattern} (parse OK, serialize failed: {e})")
            else:
                print(f"  ❌ {description}: {pattern} (no AST)")
        except ParseError as e:
            print(f"  ❌ {description}: {pattern} (parse error: {str(e)[:50]}...)")
        except Exception as e:
            print(f"  💥 {description}: {pattern} (error: {type(e).__name__})")
    
    print(f"\nProgress: {passed}/{len(test_patterns)} patterns now working")
    return passed, len(test_patterns)


def main():
    """Main test runner."""
    print("🚀 NEUROGLYPH FORMAL PARSER PROGRESS TEST")
    print("Testing improvements to previously failed patterns")
    
    passed, total = test_previously_failed_patterns()
    
    print(f"\n📊 SUMMARY")
    print(f"Working patterns: {passed}/{total} ({passed/total:.1%})")
    
    if passed >= total * 0.8:
        print("✅ GOOD PROGRESS - Most patterns now working")
        return True
    else:
        print("🔧 MORE WORK NEEDED - Continue implementation")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
