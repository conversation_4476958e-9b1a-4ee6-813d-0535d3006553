#!/usr/bin/env python3
"""
NEUROGLYPH Registry Extension from Dataset

Estende il registry concettuale con tutti i simboli presenti nel dataset 20k
per garantire 100% registry compliance.

Usage:
    python3 scripts/extend_registry_from_dataset.py
"""

import json
import sys
from pathlib import Path
from collections import Counter
from typing import Set, Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import (
    ConceptDefinition, ConceptType, ConceptRegistry
)


def extract_symbols_from_dataset(dataset_path: str) -> Set[str]:
    """Estrae tutti i simboli unici dal dataset."""
    all_symbols = set()
    
    with open(dataset_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                example = json.loads(line.strip())
                
                # Estrai simboli da symbols_used
                symbols_used = example.get('symbols_used', [])
                all_symbols.update(symbols_used)
                
                # Estrai simboli da prompt e response
                prompt = example.get('prompt_symbolic', '')
                response = example.get('response_symbolic', '')
                
                # Analizza carattere per carattere per trovare simboli Unicode
                for text in [prompt, response]:
                    for char in text:
                        if ord(char) > 127:  # Non-ASCII
                            all_symbols.add(char)
                        elif char in '+-=<>≠≤≥∴∘|/?':  # Simboli matematici ASCII
                            all_symbols.add(char)
                
            except json.JSONDecodeError:
                print(f"Warning: Invalid JSON at line {line_num}")
    
    return all_symbols


def create_missing_concept_definitions(missing_symbols: Set[str]) -> Dict[str, ConceptDefinition]:
    """Crea definizioni concettuali per simboli mancanti."""
    concepts = {}
    concept_id = 2000  # Inizia da ID 2000 per nuovi simboli
    
    # Mappatura simboli → tipi semantici
    symbol_mappings = {
        # Matematica base
        '+': (ConceptType.MATHEMATICS, "addition", "mathematical addition operation"),
        '-': (ConceptType.MATHEMATICS, "subtraction", "mathematical subtraction operation"),
        '*': (ConceptType.MATHEMATICS, "multiplication", "mathematical multiplication operation"),
        '/': (ConceptType.MATHEMATICS, "division", "mathematical division operation"),
        '=': (ConceptType.MATHEMATICS, "equality", "mathematical equality relation"),
        '≠': (ConceptType.MATHEMATICS, "inequality", "mathematical inequality relation"),
        '≤': (ConceptType.MATHEMATICS, "less_equal", "less than or equal relation"),
        '≥': (ConceptType.MATHEMATICS, "greater_equal", "greater than or equal relation"),
        '<': (ConceptType.MATHEMATICS, "less_than", "less than relation"),
        '>': (ConceptType.MATHEMATICS, "greater_than", "greater than relation"),
        
        # Logica
        '∴': (ConceptType.LOGICAL_CONNECTIVE, "therefore", "logical therefore conclusion"),
        '?': (ConceptType.META_CONCEPT, "question", "question or unknown placeholder"),
        
        # Programmazione/Codice
        '→': (ConceptType.META_CONCEPT, "arrow", "functional arrow or mapping"),
        '⤴': (ConceptType.META_CONCEPT, "return", "function return or output"),
        '⟦': (ConceptType.META_CONCEPT, "bracket_open", "semantic bracket opening"),
        '⟧': (ConceptType.META_CONCEPT, "bracket_close", "semantic bracket closing"),
        '🔢': (ConceptType.META_CONCEPT, "math_context", "mathematical context indicator"),
        '|': (ConceptType.LOGICAL_CONNECTIVE, "pipe", "logical pipe or separator"),
        '∘': (ConceptType.MATHEMATICS, "composition", "function composition operator"),
        
        # Simboli strutturali
        '(': (ConceptType.LITERAL, "paren_open", "opening parenthesis"),
        ')': (ConceptType.LITERAL, "paren_close", "closing parenthesis"),
        '[': (ConceptType.LITERAL, "bracket_open", "opening bracket"),
        ']': (ConceptType.LITERAL, "bracket_close", "closing bracket"),
        '{': (ConceptType.LITERAL, "brace_open", "opening brace"),
        '}': (ConceptType.LITERAL, "brace_close", "closing brace"),
        ',': (ConceptType.LITERAL, "comma", "comma separator"),
        ':': (ConceptType.LITERAL, "colon", "colon separator"),
        ';': (ConceptType.LITERAL, "semicolon", "semicolon separator"),
        
        # Simboli speciali Unicode
        '⇆': (ConceptType.META_CONCEPT, "bidirectional", "bidirectional arrow"),
        'λ': (ConceptType.MATHEMATICS, "lambda", "lambda function"),
        '∲': (ConceptType.MATHEMATICS, "clockwise_integral", "clockwise contour integral"),
        '∳': (ConceptType.MATHEMATICS, "counterclockwise_integral", "counterclockwise contour integral"),
        '⊥': (ConceptType.LOGICAL_CONNECTIVE, "perpendicular", "perpendicular or bottom"),
        '⊤': (ConceptType.LOGICAL_CONNECTIVE, "top", "logical top or true"),
        '⊭': (ConceptType.INFERENCE, "not_entails", "does not entail"),
        '⊈': (ConceptType.SET_THEORY, "not_subset", "not subset relation"),
        '∪': (ConceptType.SET_THEORY, "union", "set union operation"),
        '⊆': (ConceptType.SET_THEORY, "subset_equal", "subset or equal relation"),
    }
    
    for symbol in missing_symbols:
        if symbol in symbol_mappings:
            concept_type, name, meaning = symbol_mappings[symbol]
        else:
            # Default per simboli non mappati
            if symbol.isalpha():
                concept_type = ConceptType.VARIABLE
                name = f"variable_{symbol}"
                meaning = f"variable or identifier '{symbol}'"
            elif symbol.isdigit():
                concept_type = ConceptType.LITERAL
                name = f"digit_{symbol}"
                meaning = f"numeric digit '{symbol}'"
            elif len(symbol) == 1 and ord(symbol) > 127:  # Unicode single char
                concept_type = ConceptType.META_CONCEPT
                name = f"unicode_{ord(symbol):04x}"
                meaning = f"unicode symbol '{symbol}' (U+{ord(symbol):04X})"
            elif len(symbol) > 1:  # Multi-character symbol
                concept_type = ConceptType.META_CONCEPT
                name = f"multichar_{hash(symbol) % 10000:04d}"
                meaning = f"multi-character symbol '{symbol}'"
            else:
                concept_type = ConceptType.LITERAL
                name = f"symbol_{symbol}"
                meaning = f"literal symbol '{symbol}'"
        
        # Crea definizione concetto
        unicode_point = f"U+{ord(symbol):04X}" if len(symbol) == 1 else "multi-char"
        
        concept = ConceptDefinition(
            concept_id=concept_id,
            symbol=symbol,
            unicode_codepoint=unicode_point,
            concept_name=name,
            semantic_type=concept_type,
            arity=1 if concept_type in [ConceptType.VARIABLE, ConceptType.LITERAL] else 2,
            meaning=meaning,
            logical_strength=1.0 if concept_type == ConceptType.MATHEMATICS else 0.8,
            aliases=[]
        )
        
        concepts[symbol] = concept
        concept_id += 1
    
    return concepts


def extend_registry(registry: ConceptRegistry, new_concepts: Dict[str, ConceptDefinition]):
    """Estende registry con nuovi concetti."""
    for symbol, concept in new_concepts.items():
        if not registry.has_symbol(symbol):
            registry.register_concept(concept)
            print(f"✅ Added: {symbol} → {concept.concept_name} ({concept.semantic_type.value})")
        else:
            print(f"⏭️ Skipped: {symbol} (already exists)")


def main():
    """Main entry point."""
    dataset_path = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl"
    
    print("🔍 Extracting symbols from dataset...")
    dataset_symbols = extract_symbols_from_dataset(dataset_path)
    print(f"Found {len(dataset_symbols)} unique symbols in dataset")
    
    print("\n🔧 Loading current registry...")
    registry = ConceptRegistry()
    current_symbols = registry.get_all_symbols()
    print(f"Current registry has {len(current_symbols)} symbols")
    
    print("\n📊 Analyzing symbol gaps...")
    missing_symbols = dataset_symbols - current_symbols
    print(f"Missing symbols: {len(missing_symbols)}")
    
    if missing_symbols:
        print("\n🔍 Missing symbols:")
        for symbol in sorted(missing_symbols):
            if len(symbol) == 1:
                print(f"  '{symbol}' (U+{ord(symbol):04X})")
            else:
                print(f"  '{symbol}' (multi-char)")
        
        print("\n🏗️ Creating concept definitions for missing symbols...")
        new_concepts = create_missing_concept_definitions(missing_symbols)
        
        print("\n📝 Extending registry...")
        extend_registry(registry, new_concepts)
        
        print("\n💾 Saving extended registry...")
        registry.save_to_file("data/neuroglyph_registry_extended.json")
        
        print(f"\n✅ Registry extended successfully!")
        print(f"Total symbols: {len(registry.get_all_symbols())}")
        print(f"Added: {len(new_concepts)} new concepts")
        
        # Verifica finale
        print("\n🔬 Final verification...")
        final_missing = dataset_symbols - registry.get_all_symbols()
        if final_missing:
            print(f"❌ Still missing: {len(final_missing)} symbols")
            for symbol in final_missing:
                print(f"  '{symbol}'")
        else:
            print("✅ 100% registry compliance achieved!")
    
    else:
        print("\n✅ Registry already complete - no missing symbols!")


if __name__ == "__main__":
    main()
