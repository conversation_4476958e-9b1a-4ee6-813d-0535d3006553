#!/usr/bin/env python3
"""
NEUROGLYPH - Verify Audit Lock

Verifica semplice e diretta del sistema di audit lock.
"""

import sys
import hashlib
from pathlib import Path

def main():
    """Verifica diretta del sistema di audit lock."""
    print("🔒 NEUROGLYPH AUDIT LOCK VERIFICATION")
    print("="*50)
    
    project_root = Path(__file__).parent.parent
    constants_file = project_root / "neuroglyph" / "core" / "constants.py"
    hash_lock_file = project_root / "audit_hash.lock"
    
    # Test 1: File esistono
    print("\n📁 File Existence Check:")
    if constants_file.exists():
        print(f"  ✅ constants.py: {constants_file}")
    else:
        print(f"  ❌ constants.py: NOT FOUND")
        return False
    
    if hash_lock_file.exists():
        print(f"  ✅ audit_hash.lock: {hash_lock_file}")
    else:
        print(f"  ❌ audit_hash.lock: NOT FOUND")
        return False
    
    # Test 2: Hash Verification
    print("\n🔐 Hash Verification:")
    try:
        # Calcola hash corrente
        with open(constants_file, 'rb') as f:
            current_data = f.read()
            current_hash = hashlib.sha256(current_data).hexdigest()
        
        # Leggi hash salvato
        with open(hash_lock_file, 'r') as f:
            hash_line = f.read().strip()
            saved_hash = hash_line.split()[0]
        
        print(f"  📋 Current:  {current_hash}")
        print(f"  📋 Saved:    {saved_hash}")
        
        if current_hash == saved_hash:
            print("  ✅ Hash integrity: VERIFIED")
            hash_ok = True
        else:
            print("  ❌ Hash integrity: VIOLATED")
            hash_ok = False
    except Exception as e:
        print(f"  ❌ Hash check error: {e}")
        hash_ok = False
    
    # Test 3: Constants Values
    print("\n🎯 Constants Values Check:")
    try:
        # Leggi file e cerca valori critici
        with open(constants_file, 'r') as f:
            content = f.read()
        
        # Verifica valori critici
        checks = [
            ("AUDIT_FIDELITY_THRESHOLD = 0.95", "0.95"),
            ("AUDIT_SUCCESS_RATE_REQUIRED = 0.95", "0.95"),
            ("AUDIT_REJECTION_RATE_MIN = 0.95", "0.95"),
            ("AUDIT_ROUNDTRIP_REQUIRED = True", "True"),
            ("AUDIT_SEMANTIC_ZERO_TOLERANCE = True", "True"),
        ]
        
        constants_ok = True
        for check_text, expected in checks:
            if check_text in content:
                print(f"  ✅ {check_text}")
            else:
                print(f"  ❌ Missing or modified: {check_text}")
                constants_ok = False
    
    except Exception as e:
        print(f"  ❌ Constants check error: {e}")
        constants_ok = False
    
    # Test 4: Lock System Status
    print("\n🔒 Lock System Status:")
    try:
        with open(constants_file, 'r') as f:
            content = f.read()
        
        if "AUDIT_LOCK_ENABLED = True" in content:
            print("  ✅ Audit lock: ENABLED")
            lock_enabled = True
        else:
            print("  ❌ Audit lock: DISABLED")
            lock_enabled = False
            
        if "AUDIT_HASH_VERIFICATION_REQUIRED = True" in content:
            print("  ✅ Hash verification: REQUIRED")
            hash_required = True
        else:
            print("  ❌ Hash verification: NOT REQUIRED")
            hash_required = False
    
    except Exception as e:
        print(f"  ❌ Lock status error: {e}")
        lock_enabled = False
        hash_required = False
    
    # Assessment finale
    print("\n" + "="*50)
    print("🎯 FINAL ASSESSMENT")
    print("="*50)
    
    all_checks = [
        ("File Existence", True),  # Sempre true se arriviamo qui
        ("Hash Integrity", hash_ok),
        ("Constants Values", constants_ok),
        ("Lock Enabled", lock_enabled),
        ("Hash Required", hash_required),
    ]
    
    passed = sum(1 for _, status in all_checks if status)
    total = len(all_checks)
    
    for check_name, status in all_checks:
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {check_name}")
    
    print(f"\n📊 Overall: {passed}/{total} checks passed")
    
    if passed == total:
        print("✅ AUDIT LOCK SYSTEM: FULLY OPERATIONAL")
        print("🔒 All protections are active")
        print("🛡️ Immutable Principles are protected")
        return True
    else:
        print("❌ AUDIT LOCK SYSTEM: COMPROMISED")
        print("🚨 Some protections are not working")
        print("🔧 Manual intervention required")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
