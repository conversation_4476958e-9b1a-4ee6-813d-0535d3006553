#!/usr/bin/env python3
"""
NEUROGLYPH - Verify Notebook Integrity

Script per verificare l'integrità del notebook NG GODMODE v1
e la conformità ai Principi Immutabili.
"""

import json
import hashlib
from pathlib import Path
from typing import Dict, List, Any


def verify_notebook_structure(notebook_path: Path) -> Dict[str, Any]:
    """Verifica struttura del notebook."""
    print(f"🔍 Verificando struttura notebook: {notebook_path}")
    
    if not notebook_path.exists():
        return {'valid': False, 'error': 'Notebook file not found'}
    
    try:
        with open(notebook_path, 'r', encoding='utf-8') as f:
            notebook = json.load(f)
    except Exception as e:
        return {'valid': False, 'error': f'Failed to load notebook: {e}'}
    
    # Verifica struttura base
    required_keys = ['nbformat', 'nbformat_minor', 'metadata', 'cells']
    for key in required_keys:
        if key not in notebook:
            return {'valid': False, 'error': f'Missing required key: {key}'}
    
    # Verifica celle
    cells = notebook.get('cells', [])
    if len(cells) < 10:
        return {'valid': False, 'error': f'Too few cells: {len(cells)} < 10'}
    
    return {
        'valid': True,
        'nbformat': notebook['nbformat'],
        'total_cells': len(cells),
        'code_cells': len([c for c in cells if c.get('cell_type') == 'code']),
        'markdown_cells': len([c for c in cells if c.get('cell_type') == 'markdown'])
    }


def verify_immutable_principles(notebook_path: Path) -> Dict[str, Any]:
    """Verifica presenza dei Principi Immutabili nel notebook."""
    print(f"🔒 Verificando Principi Immutabili...")
    
    with open(notebook_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Principi da verificare
    principles = {
        'Atomicità': ['1 simbolo = 1 token = 1 concetto', 'atomicity'],
        'Unicità Unicode': ['nessun duplicato', 'unicode', 'codepoint'],
        'Reversibilità': ['AST', 'NEUROGLYPH', 'fidelity', 'reversibility'],
        'Semantica': ['mapping', 'semantica', 'significato'],
        'Scientificità': ['riproducibilità', 'SHA256', 'seed', 'audit'],
        'Zero Overfitting': ['early stopping', 'regolarizzazione', 'LoRA'],
        'Audit Trail': ['checkpointing', 'metadata', 'trail'],
        'Metriche Qualità': ['parsing rate', 'fidelity', 'validation'],
        'Ragionamento Simbolico': ['simbolico', 'logica', 'formale'],
        'Automazione CI/CD': ['test automatici', 'CI/CD', 'automation']
    }
    
    results = {}
    for principle, keywords in principles.items():
        found_keywords = []
        for keyword in keywords:
            if keyword.lower() in content.lower():
                found_keywords.append(keyword)
        
        results[principle] = {
            'found_keywords': found_keywords,
            'coverage': len(found_keywords) / len(keywords),
            'present': len(found_keywords) > 0
        }
    
    total_coverage = sum(r['coverage'] for r in results.values()) / len(results)
    principles_present = sum(1 for r in results.values() if r['present'])
    
    return {
        'total_coverage': total_coverage,
        'principles_present': principles_present,
        'total_principles': len(principles),
        'coverage_rate': principles_present / len(principles),
        'details': results
    }


def verify_technical_components(notebook_path: Path) -> Dict[str, Any]:
    """Verifica componenti tecnici essenziali."""
    print(f"🔧 Verificando componenti tecnici...")
    
    with open(notebook_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Componenti tecnici da verificare
    components = {
        'Unsloth Integration': ['unsloth', 'FastLanguageModel'],
        'QLoRA Configuration': ['lora_config', 'LoRA', 'quantization'],
        'Model Setup': ['Qwen2.5-Coder', 'model_name', 'tokenizer'],
        'Dataset Handling': ['certified_data', 'patterns', 'validation'],
        'Training Configuration': ['training_config', 'hyperparameters'],
        'Symbolic Validation': ['SymbolicValidator', 'fidelity', 'symbolic'],
        'Audit System': ['audit_trail', 'SHA256', 'reproducibility'],
        'Export Functions': ['save_pretrained', 'GGUF', 'export'],
        'Error Handling': ['try:', 'except:', 'Exception'],
        'Progress Monitoring': ['print', 'logging', 'progress']
    }
    
    results = {}
    for component, keywords in components.items():
        found = sum(1 for keyword in keywords if keyword in content)
        results[component] = {
            'keywords_found': found,
            'total_keywords': len(keywords),
            'coverage': found / len(keywords),
            'present': found > 0
        }
    
    total_coverage = sum(r['coverage'] for r in results.values()) / len(results)
    components_present = sum(1 for r in results.values() if r['present'])
    
    return {
        'total_coverage': total_coverage,
        'components_present': components_present,
        'total_components': len(components),
        'coverage_rate': components_present / len(components),
        'details': results
    }


def calculate_notebook_hash(notebook_path: Path) -> str:
    """Calcola hash SHA256 del notebook."""
    with open(notebook_path, 'rb') as f:
        content = f.read()
    return hashlib.sha256(content).hexdigest()


def main():
    """Main verification function."""
    print("🏆 NEUROGLYPH NG GODMODE v1 - NOTEBOOK INTEGRITY VERIFICATION")
    print("="*80)
    
    # Path del notebook
    notebook_path = Path(__file__).parent.parent / "notebooks" / "NG_GODMODE_v1_Fine_Tuning.ipynb"
    
    if not notebook_path.exists():
        print(f"❌ Notebook non trovato: {notebook_path}")
        return False
    
    # Calcola hash
    notebook_hash = calculate_notebook_hash(notebook_path)
    print(f"🔐 Notebook SHA256: {notebook_hash[:16]}...")
    
    # Verifica struttura
    structure_result = verify_notebook_structure(notebook_path)
    print(f"\n📋 STRUTTURA NOTEBOOK:")
    if structure_result['valid']:
        print(f"  ✅ Struttura valida")
        print(f"  📊 Celle totali: {structure_result['total_cells']}")
        print(f"  💻 Celle codice: {structure_result['code_cells']}")
        print(f"  📝 Celle markdown: {structure_result['markdown_cells']}")
    else:
        print(f"  ❌ Struttura invalida: {structure_result['error']}")
        return False
    
    # Verifica Principi Immutabili
    principles_result = verify_immutable_principles(notebook_path)
    print(f"\n🔒 PRINCIPI IMMUTABILI:")
    print(f"  📊 Coverage totale: {principles_result['total_coverage']:.1%}")
    print(f"  ✅ Principi presenti: {principles_result['principles_present']}/{principles_result['total_principles']}")
    print(f"  📈 Coverage rate: {principles_result['coverage_rate']:.1%}")
    
    # Dettagli principi
    for principle, details in principles_result['details'].items():
        status = "✅" if details['present'] else "❌"
        print(f"    {status} {principle}: {details['coverage']:.1%}")
    
    # Verifica componenti tecnici
    technical_result = verify_technical_components(notebook_path)
    print(f"\n🔧 COMPONENTI TECNICI:")
    print(f"  📊 Coverage totale: {technical_result['total_coverage']:.1%}")
    print(f"  ✅ Componenti presenti: {technical_result['components_present']}/{technical_result['total_components']}")
    print(f"  📈 Coverage rate: {technical_result['coverage_rate']:.1%}")
    
    # Assessment finale
    overall_score = (
        (1.0 if structure_result['valid'] else 0.0) * 0.3 +
        principles_result['coverage_rate'] * 0.4 +
        technical_result['coverage_rate'] * 0.3
    )
    
    print(f"\n" + "="*80)
    print(f"🎯 ASSESSMENT FINALE")
    print(f"="*80)
    print(f"📊 Overall Score: {overall_score:.1%}")
    print(f"🔐 Notebook Hash: {notebook_hash}")
    
    if overall_score >= 0.95:
        print(f"\n✅ NOTEBOOK VERIFICATION: PASSED")
        print(f"🏆 QUALITÀ: ECCELLENTE")
        print(f"🚀 PRONTO PER FINE-TUNING")
        return True
    elif overall_score >= 0.80:
        print(f"\n🟡 NOTEBOOK VERIFICATION: CONDITIONAL PASS")
        print(f"🎯 QUALITÀ: BUONA")
        print(f"🔧 MIGLIORAMENTI RACCOMANDATI")
        return True
    else:
        print(f"\n❌ NOTEBOOK VERIFICATION: FAILED")
        print(f"🔧 QUALITÀ: NEEDS WORK")
        print(f"📊 Score: {overall_score:.1%} < 80%")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
