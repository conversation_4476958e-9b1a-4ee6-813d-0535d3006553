#!/usr/bin/env python3
"""
NEUROGLYPH - Test Formal Parser Simple

Test semplice del parser formale per verificare funzionalità base.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.formal_parser import create_formal_parser, ParseError


def test_valid_patterns():
    """Test pattern validi che dovrebbero essere accettati."""
    print("✅ TESTING VALID PATTERNS")
    print("-" * 40)
    
    tokenizer = create_conceptual_tokenizer()
    
    valid_patterns = [
        ("P", "Simple Variable"),
        ("P ⇒ Q", "Simple Implication"),
        ("P ∧ Q", "Simple Conjunction"),
        ("P ∨ Q", "Simple Disjunction"),
        ("¬P", "Simple Negation"),
        ("∀x: P(x)", "Universal Quantification"),
        ("∃x: P(x)", "Existential Quantification"),
        ("f(x)", "Function Call"),
        ("(P ∨ Q)", "Grouped Expression"),
    ]
    
    passed = 0
    for pattern, description in valid_patterns:
        try:
            tokens = tokenizer.tokenize(pattern)
            parser = create_formal_parser(tokens)
            ast = parser.parse()
            
            if ast and ast.root_concept:
                print(f"  ✅ {description}: {pattern}")
                passed += 1
            else:
                print(f"  ❌ {description}: {pattern} (no AST)")
        except Exception as e:
            print(f"  ❌ {description}: {pattern} (error: {e})")
    
    print(f"\nValid patterns: {passed}/{len(valid_patterns)} passed")
    return passed, len(valid_patterns)


def test_invalid_patterns():
    """Test pattern invalidi che dovrebbero essere rifiutati."""
    print("\n❌ TESTING INVALID PATTERNS")
    print("-" * 40)
    
    tokenizer = create_conceptual_tokenizer()
    
    invalid_patterns = [
        ("", "Empty String"),
        ("∧", "Isolated Conjunction"),
        ("P ⇒", "Incomplete Implication"),
        ("¬∨P", "Malformed Negation"),
        ("∀∃x", "Malformed Quantifier"),
        ("f()", "Function Without Args"),
        ("(P ∨", "Unclosed Parenthesis"),
        ("P ∨ Q)", "Unopened Parenthesis"),
        ("BROKEN_SYNTAX", "Broken Syntax"),
        ("∀x:", "Quantifier Without Body"),
    ]
    
    rejected = 0
    for pattern, description in invalid_patterns:
        try:
            tokens = tokenizer.tokenize(pattern)
            parser = create_formal_parser(tokens)
            ast = parser.parse()
            
            # Se arriva qui, il pattern è stato accettato (male)
            print(f"  ❌ {description}: {pattern} (incorrectly accepted)")
        except ParseError:
            print(f"  ✅ {description}: {pattern} (correctly rejected)")
            rejected += 1
        except Exception as e:
            print(f"  ✅ {description}: {pattern} (rejected with error: {type(e).__name__})")
            rejected += 1
    
    print(f"\nInvalid patterns: {rejected}/{len(invalid_patterns)} rejected")
    return rejected, len(invalid_patterns)


def main():
    """Main test runner."""
    print("🔥 NEUROGLYPH FORMAL PARSER SIMPLE TEST")
    print("=" * 60)
    
    # Test valid patterns
    valid_passed, valid_total = test_valid_patterns()
    
    # Test invalid patterns
    invalid_rejected, invalid_total = test_invalid_patterns()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    valid_rate = valid_passed / valid_total if valid_total > 0 else 0
    rejection_rate = invalid_rejected / invalid_total if invalid_total > 0 else 0
    
    print(f"Valid patterns accepted: {valid_passed}/{valid_total} ({valid_rate:.1%})")
    print(f"Invalid patterns rejected: {invalid_rejected}/{invalid_total} ({rejection_rate:.1%})")
    
    # Assessment
    if valid_rate >= 0.8 and rejection_rate >= 0.8:
        print("\n✅ FORMAL PARSER: BASIC FUNCTIONALITY WORKING")
        print("🎯 Ready for more comprehensive testing")
        return True
    else:
        print("\n❌ FORMAL PARSER: NEEDS IMPROVEMENT")
        print("🔧 Fix required before proceeding")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
