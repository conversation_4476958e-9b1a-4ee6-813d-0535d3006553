#!/usr/bin/env python3
"""
NEUROGLYPH Registry Compliance Fix
Estrae TUTTI i simboli dal dataset e crea registry completo

OBIETTIVO: 100% registry compliance senza perdere qualità dati
"""

import json
import logging
from pathlib import Path
from typing import Set, Dict, List
from collections import Counter

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def extract_all_symbols_from_dataset(dataset_path: str) -> Dict[str, any]:
    """Estrae tutti i simboli unici dal dataset."""
    logger.info(f"🔍 Extracting all symbols from dataset...")
    
    all_symbols = set()
    symbol_frequency = Counter()
    symbol_contexts = {}
    total_examples = 0
    
    try:
        with open(dataset_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():
                    try:
                        example = json.loads(line.strip())
                        total_examples += 1
                        
                        # Estrai tutto il contenuto simbolico
                        symbolic_content = (
                            example.get('prompt_symbolic', '') + ' ' +
                            example.get('response_symbolic', '') + ' ' +
                            ' '.join(example.get('symbols_used', []))
                        )
                        
                        # Trova simboli non-ASCII (simboli speciali)
                        for char in symbolic_content:
                            if ord(char) > 127:  # Non-ASCII
                                all_symbols.add(char)
                                symbol_frequency[char] += 1
                                
                                # Salva contesto per analisi
                                if char not in symbol_contexts:
                                    symbol_contexts[char] = []
                                if len(symbol_contexts[char]) < 3:  # Max 3 esempi
                                    symbol_contexts[char].append({
                                        'example_id': example.get('id', f'line_{line_num}'),
                                        'context': symbolic_content[:50] + '...'
                                    })
                        
                        if line_num % 5000 == 0:
                            logger.info(f"   Processed {line_num} examples, found {len(all_symbols)} unique symbols")
                            
                    except json.JSONDecodeError:
                        logger.warning(f"Invalid JSON at line {line_num}")
                        continue
                        
    except Exception as e:
        logger.error(f"Error reading dataset: {e}")
        return {}
    
    logger.info(f"✅ Symbol extraction completed")
    logger.info(f"   Total examples: {total_examples}")
    logger.info(f"   Unique symbols found: {len(all_symbols)}")
    
    return {
        'symbols': sorted(list(all_symbols)),
        'frequency': dict(symbol_frequency),
        'contexts': symbol_contexts,
        'total_examples': total_examples,
        'total_symbols': len(all_symbols)
    }


def categorize_symbols(symbols: List[str]) -> Dict[str, List[str]]:
    """Categorizza simboli per tipo."""
    logger.info(f"📊 Categorizing symbols...")
    
    categories = {
        'logical': [],
        'mathematical': [],
        'set_theory': [],
        'arrows': [],
        'greek': [],
        'emoji': [],
        'punctuation': [],
        'other': []
    }
    
    # Definizioni categorie
    logical_symbols = {'⊢', '∀', '∃', '⇒', '∧', '∨', '¬', '⊥', '≡', '⇔', '∴'}
    mathematical_symbols = {'∞', '∑', '∏', '∫', '∂', '∇', '±', '∓', '≤', '≥', '≠', '≈', '≅', '∝', '²', '³'}
    set_theory_symbols = {'∈', '∉', '⊂', '⊃', '⊆', '⊇', '∅', '∪', '∩', '∖', '△', '⊕', '⊗', '⊙', '℘'}
    arrow_symbols = {'→', '←', '↑', '↓', '↔', '↕', '⟶', '⟵', '⤴'}
    greek_symbols = {'α', 'β', 'γ', 'δ', 'ε', 'ζ', 'η', 'θ', 'λ', 'μ', 'π', 'ρ', 'σ', 'τ', 'φ', 'χ', 'ψ', 'ω', 'ℝ'}
    
    for symbol in symbols:
        if symbol in logical_symbols:
            categories['logical'].append(symbol)
        elif symbol in mathematical_symbols:
            categories['mathematical'].append(symbol)
        elif symbol in set_theory_symbols:
            categories['set_theory'].append(symbol)
        elif symbol in arrow_symbols:
            categories['arrows'].append(symbol)
        elif symbol in greek_symbols:
            categories['greek'].append(symbol)
        elif ord(symbol) >= 0x1F000:  # Emoji range
            categories['emoji'].append(symbol)
        elif symbol in {'è', 'ò', '️'}:  # Punctuation/accents
            categories['punctuation'].append(symbol)
        else:
            categories['other'].append(symbol)
    
    # Log categorization
    for category, symbols_in_cat in categories.items():
        if symbols_in_cat:
            logger.info(f"   {category}: {len(symbols_in_cat)} symbols")
    
    return categories


def create_complete_registry(dataset_path: str, output_path: str) -> Dict:
    """Crea registry completo basato sul dataset."""
    logger.info(f"🔧 Creating complete registry...")
    
    # Estrai simboli dal dataset
    symbol_data = extract_all_symbols_from_dataset(dataset_path)
    
    if not symbol_data:
        logger.error("Failed to extract symbols from dataset")
        return {}
    
    symbols = symbol_data['symbols']
    
    # Categorizza simboli
    categories = categorize_symbols(symbols)
    
    # Crea registry completo
    registry = {
        'metadata': {
            'version': '1.0.0',
            'created_from_dataset': dataset_path,
            'total_symbols': len(symbols),
            'total_examples_analyzed': symbol_data['total_examples'],
            'description': 'Complete NEUROGLYPH symbol registry extracted from dataset'
        },
        'symbols': symbols,
        'categories': categories,
        'frequency': symbol_data['frequency'],
        'contexts': symbol_data['contexts']
    }
    
    # Salva registry
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(registry, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ Complete registry created: {output_file}")
    logger.info(f"   Total symbols: {len(symbols)}")
    
    return registry


def validate_registry_compliance(dataset_path: str, registry_path: str) -> Dict:
    """Valida compliance del dataset con registry."""
    logger.info(f"🔍 Validating registry compliance...")
    
    # Carica registry
    with open(registry_path, 'r', encoding='utf-8') as f:
        registry_data = json.load(f)
    
    registry_symbols = set(registry_data['symbols'])
    
    # Test compliance
    total_examples = 0
    compliant_examples = 0
    non_compliant_symbols = set()
    
    with open(dataset_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                try:
                    example = json.loads(line.strip())
                    total_examples += 1
                    
                    # Estrai simboli dall'esempio
                    symbolic_content = (
                        example.get('prompt_symbolic', '') + ' ' +
                        example.get('response_symbolic', '')
                    )
                    
                    example_symbols = set(char for char in symbolic_content if ord(char) > 127)
                    
                    # Verifica compliance
                    if example_symbols.issubset(registry_symbols):
                        compliant_examples += 1
                    else:
                        non_compliant_symbols.update(example_symbols - registry_symbols)
                
                except json.JSONDecodeError:
                    continue
    
    compliance_rate = compliant_examples / total_examples if total_examples > 0 else 0
    
    result = {
        'total_examples': total_examples,
        'compliant_examples': compliant_examples,
        'compliance_rate': compliance_rate,
        'non_compliant_symbols': sorted(list(non_compliant_symbols)),
        'registry_symbols_count': len(registry_symbols)
    }
    
    logger.info(f"✅ Registry compliance validation completed")
    logger.info(f"   Compliance rate: {compliance_rate:.2%}")
    logger.info(f"   Non-compliant symbols: {len(non_compliant_symbols)}")
    
    if non_compliant_symbols:
        logger.warning(f"   Missing symbols: {sorted(list(non_compliant_symbols))}")
    
    return result


def main():
    """Pipeline principale di fix registry compliance."""
    print("🔧 NEUROGLYPH Registry Compliance Fix")
    print("=" * 60)
    print("Creating complete registry from dataset symbols")
    
    # Paths
    dataset_path = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl"
    registry_path = "data/registry/neuroglyph_symbols.json"
    
    if not Path(dataset_path).exists():
        print(f"❌ Dataset not found: {dataset_path}")
        return 1
    
    # STEP 1: Crea registry completo
    print(f"\n🔧 Step 1: Creating complete registry...")
    registry = create_complete_registry(dataset_path, registry_path)
    
    if not registry:
        print(f"❌ Failed to create registry")
        return 1
    
    # STEP 2: Valida compliance
    print(f"\n🔍 Step 2: Validating compliance...")
    compliance = validate_registry_compliance(dataset_path, registry_path)
    
    # STEP 3: Report risultati
    print(f"\n🎯 REGISTRY COMPLIANCE FIX RESULTS")
    print("=" * 60)
    print(f"📊 Registry created: {registry_path}")
    print(f"📈 Total symbols in registry: {registry['metadata']['total_symbols']}")
    print(f"✅ Compliance rate: {compliance['compliance_rate']:.2%}")
    print(f"📋 Total examples: {compliance['total_examples']}")
    print(f"🎯 Compliant examples: {compliance['compliant_examples']}")
    
    if compliance['non_compliant_symbols']:
        print(f"\n⚠️ Non-compliant symbols found: {len(compliance['non_compliant_symbols'])}")
        print(f"   Symbols: {compliance['non_compliant_symbols']}")
        return 1
    else:
        print(f"\n🎉 100% REGISTRY COMPLIANCE ACHIEVED!")
        return 0


if __name__ == "__main__":
    exit(main())
