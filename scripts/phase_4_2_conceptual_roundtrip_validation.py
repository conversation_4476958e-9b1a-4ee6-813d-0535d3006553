#!/usr/bin/env python3
"""
NEUROGLYPH Phase 4.2 - Conceptual Roundtrip Validation

Validazione completa del sistema concettuale puro:
Tokenizer Atomico → Parser Semantico → AST Concettuale → Roundtrip

Usage:
    python3 scripts/phase_4_2_conceptual_roundtrip_validation.py
    python3 scripts/phase_4_2_conceptual_roundtrip_validation.py --comprehensive
"""

import argparse
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.conceptual_parser import create_conceptual_parser, SemanticParseError
from neuroglyph.conceptual.ast.conceptual_ast import ConceptualAST


class ConceptualSystemValidator:
    """
    Validatore completo per sistema concettuale NEUROGLYPH.
    
    Testa l'intera pipeline: Codice → Token → AST → Codice
    con metriche rigorose di qualità semantica.
    """
    
    def __init__(self, output_dir: str = "data/verification"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Inizializza componenti
        self.tokenizer = create_conceptual_tokenizer()
        self.parser = create_conceptual_parser(self.tokenizer)
        
        # Criteri di validazione
        self.quality_gates = {
            'tokenization_atomicity': 1.0,      # 100% simboli atomici
            'parsing_success_rate': 0.95,       # 95% parsing riuscito
            'semantic_fidelity': 0.98,          # 98% fedeltà semantica
            'roundtrip_accuracy': 0.95,         # 95% roundtrip accurato
            'logical_consistency': 1.0,         # 100% coerenza logica
            'conceptual_purity': 1.0             # 100% purezza concettuale
        }
        
        # Test cases per validazione
        self.test_cases = self._initialize_test_cases()
    
    def _initialize_test_cases(self) -> List[Dict[str, Any]]:
        """Inizializza casi di test per validazione."""
        return [
            # Casi base
            {"code": "P", "category": "variable", "complexity": 1},
            {"code": "variable_name", "category": "variable", "complexity": 1},
            {"code": "42", "category": "literal", "complexity": 1},
            {"code": "3.14", "category": "literal", "complexity": 1},
            
            # Logica proposizionale
            {"code": "P ⇒ Q", "category": "implication", "complexity": 2},
            {"code": "P ∧ Q", "category": "conjunction", "complexity": 2},
            {"code": "P ∨ Q", "category": "disjunction", "complexity": 2},
            {"code": "¬P", "category": "negation", "complexity": 2},
            
            # Quantificatori
            {"code": "∀x: P", "category": "universal", "complexity": 3},
            {"code": "∃y: Q", "category": "existential", "complexity": 3},
            
            # Espressioni complesse
            {"code": "P ∧ Q ⇒ R", "category": "complex_logic", "complexity": 4},
            {"code": "∀x: P(x) ⇒ Q(x)", "category": "quantified_implication", "complexity": 5},
            {"code": "¬(P ∨ Q)", "category": "negated_disjunction", "complexity": 4},
            
            # Simboli speciali
            {"code": "x ∈ A", "category": "set_membership", "complexity": 2},
            {"code": "∅", "category": "empty_set", "complexity": 1},
            {"code": "P ⊢ Q", "category": "inference", "complexity": 3},
            {"code": "🧠 reasoning", "category": "meta_concept", "complexity": 2},
            
            # Edge cases
            {"code": "func_name(x, y)", "category": "function_call", "complexity": 3},
            {"code": "long_variable_123", "category": "long_variable", "complexity": 1},
        ]
    
    def validate_comprehensive(self) -> Dict[str, Any]:
        """
        Validazione completa del sistema concettuale.
        
        Returns:
            Risultati validazione con metriche dettagliate
        """
        print("🔬 Starting NEUROGLYPH Conceptual System Validation...")
        
        start_time = time.time()
        results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'test_type': 'comprehensive_conceptual',
            'total_test_cases': len(self.test_cases),
            'metrics': {},
            'detailed_results': [],
            'quality_assessment': {},
            'component_stats': {}
        }
        
        # Reset statistiche
        self.tokenizer.reset_stats()
        self.parser.reset_stats()
        
        # Esegui test su tutti i casi
        for i, test_case in enumerate(self.test_cases):
            print(f"Testing {i+1}/{len(self.test_cases)}: {test_case['code']}")
            
            case_result = self._validate_single_case(test_case)
            results['detailed_results'].append(case_result)
        
        # Calcola metriche aggregate
        results['metrics'] = self._calculate_aggregate_metrics(results['detailed_results'])
        
        # Statistiche componenti
        results['component_stats'] = {
            'tokenizer': self.tokenizer.get_tokenization_stats(),
            'parser': self.parser.get_parse_stats()
        }
        
        # Valutazione qualità
        results['quality_assessment'] = self._assess_quality(results['metrics'])
        
        results['elapsed_time_seconds'] = time.time() - start_time
        
        # Salva risultati
        self._save_results(results, 'conceptual_system_validation.json')
        self._print_summary(results)
        
        return results
    
    def _validate_single_case(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Valida singolo caso di test."""
        code = test_case['code']
        result = {
            'code': code,
            'category': test_case['category'],
            'complexity': test_case['complexity'],
            'success': False,
            'tokenization_success': False,
            'parsing_success': False,
            'roundtrip_success': False,
            'semantic_fidelity': 0.0,
            'logical_consistency': False,
            'conceptual_purity': False,
            'error': None,
            'details': {}
        }
        
        try:
            # 1. Tokenizzazione
            tokens = self.tokenizer.tokenize(code)
            result['tokenization_success'] = True
            result['details']['token_count'] = len(tokens)
            result['details']['concept_tokens'] = sum(1 for t in tokens if t.concept_id > 0)
            
            # 2. Parsing
            ast = self.parser.parse(code)
            result['parsing_success'] = True
            result['details']['ast_type'] = type(ast.root_concept).__name__
            result['details']['logical_strength'] = ast.root_concept.logical_strength()
            
            # 3. Roundtrip
            reconstructed = ast.to_neuroglyph()
            result['details']['reconstructed'] = reconstructed
            
            # 4. Re-parsing per verifica
            ast2 = self.parser.parse(reconstructed)
            result['roundtrip_success'] = True
            
            # 5. Calcola metriche
            result['semantic_fidelity'] = self._calculate_semantic_fidelity(ast, ast2)
            result['logical_consistency'] = self._check_logical_consistency(ast)
            result['conceptual_purity'] = self._check_conceptual_purity(ast)
            
            # 6. Successo complessivo
            result['success'] = (result['tokenization_success'] and 
                               result['parsing_success'] and 
                               result['roundtrip_success'] and
                               result['semantic_fidelity'] >= 0.95)
            
        except SemanticParseError as e:
            result['error'] = f"Semantic parse error: {e}"
        except Exception as e:
            result['error'] = f"Unexpected error: {e}"
        
        return result
    
    def _calculate_semantic_fidelity(self, ast1: ConceptualAST, ast2: ConceptualAST) -> float:
        """Calcola fedeltà semantica tra due AST."""
        try:
            # Confronto tipo
            if type(ast1.root_concept) != type(ast2.root_concept):
                return 0.0
            
            # Confronto serializzazione
            code1 = ast1.to_neuroglyph()
            code2 = ast2.to_neuroglyph()
            
            if code1 == code2:
                return 1.0
            
            # Confronto semantico (semplificato)
            if hasattr(ast1.root_concept, 'semantically_equivalent'):
                if ast1.root_concept.semantically_equivalent(ast2.root_concept):
                    return 0.95
            
            # Fallback: similarità stringa
            from difflib import SequenceMatcher
            return SequenceMatcher(None, code1, code2).ratio()
            
        except Exception:
            return 0.0
    
    def _check_logical_consistency(self, ast: ConceptualAST) -> bool:
        """Verifica coerenza logica dell'AST."""
        try:
            # Verifica che la forza logica sia valida
            strength = ast.root_concept.logical_strength()
            if not (0.0 <= strength <= 1.0):
                return False
            
            # Verifica che il significato semantico sia non vuoto
            meaning = ast.root_concept.semantic_meaning()
            if not meaning or len(meaning.strip()) == 0:
                return False
            
            # Verifica che la serializzazione sia valida
            neuroglyph_code = ast.to_neuroglyph()
            if not neuroglyph_code or len(neuroglyph_code.strip()) == 0:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _check_conceptual_purity(self, ast: ConceptualAST) -> bool:
        """Verifica purezza concettuale dell'AST."""
        try:
            # Verifica che il nodo radice sia un concetto valido
            root = ast.root_concept
            
            # Deve avere metadati concettuali
            if not hasattr(root, 'metadata') or root.metadata is None:
                return False
            
            # Deve avere tipo di nodo concettuale
            if not hasattr(root, 'node_type') or root.node_type is None:
                return False
            
            # Deve essere immutabile (frozen dataclass)
            if hasattr(root, '__dataclass_fields__'):
                return True  # È un dataclass, probabilmente immutabile
            
            return True
            
        except Exception:
            return False
    
    def _calculate_aggregate_metrics(self, results: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calcola metriche aggregate."""
        total = len(results)
        if total == 0:
            return {}
        
        metrics = {
            'tokenization_atomicity': sum(1 for r in results if r['tokenization_success']) / total,
            'parsing_success_rate': sum(1 for r in results if r['parsing_success']) / total,
            'semantic_fidelity': sum(r['semantic_fidelity'] for r in results) / total,
            'roundtrip_accuracy': sum(1 for r in results if r['roundtrip_success']) / total,
            'logical_consistency': sum(1 for r in results if r['logical_consistency']) / total,
            'conceptual_purity': sum(1 for r in results if r['conceptual_purity']) / total,
            'overall_success_rate': sum(1 for r in results if r['success']) / total
        }
        
        return metrics
    
    def _assess_quality(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """Valuta qualità rispetto ai criteri."""
        assessment = {
            'overall_pass': True,
            'passed_criteria': [],
            'failed_criteria': [],
            'critical_failures': []
        }
        
        for criterion, threshold in self.quality_gates.items():
            actual_value = metrics.get(criterion, 0.0)
            passed = actual_value >= threshold
            
            criterion_result = {
                'criterion': criterion,
                'threshold': threshold,
                'actual': actual_value,
                'status': 'PASS' if passed else 'FAIL'
            }
            
            if passed:
                assessment['passed_criteria'].append(criterion_result)
            else:
                assessment['failed_criteria'].append(criterion_result)
                assessment['overall_pass'] = False
                
                # Criteri critici
                if criterion in ['tokenization_atomicity', 'conceptual_purity']:
                    assessment['critical_failures'].append(criterion)
        
        return assessment
    
    def _save_results(self, results: Dict[str, Any], filename: str):
        """Salva risultati in JSON."""
        output_file = self.output_dir / filename
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📄 Results saved to: {output_file}")
    
    def _print_summary(self, results: Dict[str, Any]):
        """Stampa summary dei risultati."""
        metrics = results['metrics']
        assessment = results['quality_assessment']
        
        print("\n" + "="*70)
        print("🧪 NEUROGLYPH CONCEPTUAL SYSTEM VALIDATION SUMMARY")
        print("="*70)
        
        print(f"Test Cases: {results['total_test_cases']}")
        print(f"Elapsed Time: {results['elapsed_time_seconds']:.1f}s")
        
        print("\n📊 CORE METRICS:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value:.1%}")
        
        print(f"\n🎯 QUALITY ASSESSMENT:")
        print(f"Overall Pass: {'✅ PASS' if assessment['overall_pass'] else '❌ FAIL'}")
        
        if assessment['passed_criteria']:
            print("✅ Passed Criteria:")
            for criterion in assessment['passed_criteria']:
                print(f"  - {criterion['criterion']}: {criterion['actual']:.1%} >= {criterion['threshold']:.1%}")
        
        if assessment['failed_criteria']:
            print("❌ Failed Criteria:")
            for criterion in assessment['failed_criteria']:
                print(f"  - {criterion['criterion']}: {criterion['actual']:.1%} < {criterion['threshold']:.1%}")
        
        if assessment['critical_failures']:
            print("🚨 CRITICAL FAILURES:")
            for failure in assessment['critical_failures']:
                print(f"  - {failure}")
        
        # Statistiche componenti
        comp_stats = results['component_stats']
        print(f"\n🔧 COMPONENT STATISTICS:")
        print(f"Tokenizer Zero-Splitting Rate: {comp_stats['tokenizer']['zero_splitting_rate']:.1%}")
        print(f"Parser Success Rate: {comp_stats['parser']['success_rate']:.1%}")
        
        print("\n" + "="*70)
        
        # Raccomandazioni
        if assessment['overall_pass']:
            print("🎉 CONCEPTUAL SYSTEM VALIDATION PASSED!")
            print("✅ Ready for dataset generation and LLM training")
        else:
            print("💥 CONCEPTUAL SYSTEM VALIDATION FAILED!")
            print("🔧 Fix critical issues before proceeding")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='NEUROGLYPH Conceptual System Validation')
    parser.add_argument('--comprehensive', action='store_true', help='Run comprehensive validation')
    parser.add_argument('--output-dir', type=str, default='data/verification', help='Output directory')
    
    args = parser.parse_args()
    
    validator = ConceptualSystemValidator(output_dir=args.output_dir)
    
    try:
        results = validator.validate_comprehensive()
        
        # Exit code basato su risultati
        if results['quality_assessment']['overall_pass']:
            print("\n🎉 VALIDATION PASSED - Conceptual system ready!")
            sys.exit(0)
        else:
            print("\n💥 VALIDATION FAILED - Fix issues before proceeding!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 VALIDATION ERROR: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
