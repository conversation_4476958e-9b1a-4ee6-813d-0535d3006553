#!/usr/bin/env python3
"""
NEUROGLYPH - Formal Roundtrip Certification

Test di certificazione finale per roundtrip AST ↔ NEUROGLYPH
con il parser formale. Verifica reversibilità perfetta sui 25 pattern ufficiali.

OBIETTIVO: Fidelity = 1.0 su tutti i pattern certificati
STANDARD: ≥95% fidelity, ≥95% reject rate
"""

import sys
from pathlib import Path
from difflib import SequenceMatcher

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.core.constants import (
    AUDIT_FIDELITY_THRESHOLD,
    AUDIT_SUCCESS_RATE_REQUIRED,
    verify_audit_constants_integrity
)

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.formal_parser import create_formal_parser, ParseError


class FormalRoundtripCertifier:
    """Certificatore finale per roundtrip con parser formale."""
    
    def __init__(self):
        # Verifica integrità audit
        verify_audit_constants_integrity()
        
        self.tokenizer = create_conceptual_tokenizer()
        self.min_fidelity = AUDIT_FIDELITY_THRESHOLD
        self.target_success_rate = AUDIT_SUCCESS_RATE_REQUIRED
    
    def certify_formal_roundtrip(self):
        """Certificazione finale roundtrip con parser formale."""
        print("🎯 NEUROGLYPH FORMAL ROUNDTRIP CERTIFICATION")
        print("="*80)
        print(f"PARSER: Formal grammar-based (no fallbacks)")
        print(f"STANDARD: Fidelity ≥{self.min_fidelity:.1%}, Success ≥{self.target_success_rate:.1%}")
        print(f"OBJECTIVE: Perfect reversibility on certified patterns")
        print("="*80)
        
        # 25 PATTERN UFFICIALI NEUROGLYPH
        official_patterns = [
            # Quantificatori (5)
            ("∀x: P(x)", "Universal Simple"),
            ("∃x: P(x)", "Existential Simple"),
            ("∀x ∈ ℝ: P(x)", "Universal with Domain"),
            ("∃x ∈ ℕ: P(x)", "Existential with Domain"),
            ("∀x ∃y: R(x,y)", "Nested Quantifiers"),
            
            # Logica (6)
            ("P ⇒ Q", "Material Implication"),
            ("P ∧ Q", "Logical Conjunction"),
            ("P ∨ Q", "Logical Disjunction"),
            ("¬P", "Logical Negation"),
            ("¬(P ∨ Q)", "Negated Disjunction"),
            ("P ∧ Q ⇒ R", "Compound Implication"),
            
            # Set Theory (4)
            ("A ∪ B", "Set Union"),
            ("A ∩ B", "Set Intersection"),
            ("x ∈ A", "Set Membership"),
            ("A ⊆ B", "Subset Relation"),
            
            # Matematica Base (3)
            ("x + y", "Addition"),
            ("f(x)", "Function Call"),
            ("x = y", "Equality"),
            
            # Matematica Avanzata (4)
            ("∫ f(x) dx", "Integral"),
            ("∑ xᵢ", "Summation"),
            ("∏ xᵢ", "Product"),
            ("∂f/∂x", "Partial Derivative"),
            
            # Espressioni Complesse (3)
            ("(P ∨ Q)", "Grouped Expression"),
            ("f(x,y)", "Multi-Argument Function"),
            ("x ≠ y", "Inequality")
        ]
        
        print(f"\n🧪 TESTING {len(official_patterns)} OFFICIAL PATTERNS")
        print("Each pattern must achieve perfect roundtrip fidelity")
        
        results = []
        perfect_fidelity = []
        imperfect_fidelity = []
        failed_parsing = []
        
        for i, (pattern, name) in enumerate(official_patterns, 1):
            print(f"\n[{i:2d}/25] {name}: {pattern}")
            
            result = self._test_formal_roundtrip(pattern, name)
            results.append(result)
            
            if result['parsing_success']:
                if result['fidelity'] == 1.0:
                    perfect_fidelity.append(result)
                    print(f"    ✅ PERFECT - Fidelity: 100%")
                    if result['output'] != pattern:
                        print(f"    📤 Normalized: {result['output']}")
                elif result['fidelity'] >= self.min_fidelity:
                    imperfect_fidelity.append(result)
                    print(f"    🟡 GOOD - Fidelity: {result['fidelity']:.1%}")
                    print(f"    📤 Expected: {pattern}")
                    print(f"    📤 Got:      {result['output']}")
                else:
                    imperfect_fidelity.append(result)
                    print(f"    ❌ POOR - Fidelity: {result['fidelity']:.1%}")
                    print(f"    📤 Expected: {pattern}")
                    print(f"    📤 Got:      {result['output']}")
            else:
                failed_parsing.append(result)
                print(f"    💥 PARSE FAILED - {result['error']}")
        
        self._print_certification_results(perfect_fidelity, imperfect_fidelity, failed_parsing)
        
        return len(perfect_fidelity), len(imperfect_fidelity), len(failed_parsing)
    
    def _test_formal_roundtrip(self, pattern: str, name: str) -> dict:
        """Test roundtrip con parser formale."""
        result = {
            'name': name,
            'input': pattern,
            'output': None,
            'fidelity': 0.0,
            'parsing_success': False,
            'meets_standard': False,
            'error': None,
            'ast_type': None
        }
        
        try:
            # Step 1: Tokenize
            tokens = self.tokenizer.tokenize(pattern)
            
            # Step 2: Parse with formal parser
            parser = create_formal_parser(tokens)
            ast = parser.parse()
            
            if ast and ast.root_concept:
                result['parsing_success'] = True
                result['ast_type'] = type(ast.root_concept).__name__
                
                # Step 3: Serialize back to NEUROGLYPH
                try:
                    output = ast.to_neuroglyph()
                    result['output'] = output
                    
                    # Step 4: Calculate fidelity
                    fidelity = self._calculate_fidelity(pattern, output)
                    result['fidelity'] = fidelity
                    
                    # Step 5: Check standards
                    result['meets_standard'] = fidelity >= self.min_fidelity
                    
                except Exception as e:
                    result['error'] = f"Serialization failed: {e}"
            else:
                result['error'] = "Parsing failed - no AST"
        
        except ParseError as e:
            result['error'] = f"Parse error: {e}"
        except Exception as e:
            result['error'] = f"Unexpected error: {e}"
        
        return result
    
    def _calculate_fidelity(self, original: str, reconstructed: str) -> float:
        """Calcolo fidelity identico al sistema originale."""
        # Exact match = 100% fidelity
        if original.strip() == reconstructed.strip():
            return 1.0
        
        # Normalized comparison
        orig_norm = self._normalize(original)
        recon_norm = self._normalize(reconstructed)
        
        if orig_norm == recon_norm:
            return 1.0
        
        # Sequence similarity
        similarity = SequenceMatcher(None, orig_norm, recon_norm).ratio()
        return similarity
    
    def _normalize(self, text: str) -> str:
        """Normalizzazione per confronto."""
        import re
        normalized = re.sub(r'\s+', ' ', text.strip())
        return normalized
    
    def _print_certification_results(self, perfect: list, imperfect: list, failed: list):
        """Stampa risultati di certificazione."""
        total = len(perfect) + len(imperfect) + len(failed)
        success_rate = len(perfect) / total if total > 0 else 0
        parsing_rate = (len(perfect) + len(imperfect)) / total if total > 0 else 0
        
        print("\n" + "="*80)
        print("🎯 FORMAL ROUNDTRIP CERTIFICATION RESULTS")
        print("="*80)
        
        print(f"📊 PARSING RESULTS:")
        print(f"  Total Patterns: {total}")
        print(f"  Successful Parsing: {len(perfect) + len(imperfect)}")
        print(f"  Failed Parsing: {len(failed)}")
        print(f"  Parsing Rate: {parsing_rate:.1%}")
        
        print(f"\n📊 FIDELITY RESULTS:")
        print(f"  Perfect Fidelity (100%): {len(perfect)}")
        print(f"  Good Fidelity (≥95%): {len(imperfect)}")
        print(f"  Poor Fidelity (<95%): {len([r for r in imperfect if r['fidelity'] < self.min_fidelity])}")
        print(f"  Overall Success Rate: {success_rate:.1%}")
        
        # Categorizza per tipo
        categories = {}
        for pattern in perfect + imperfect:
            category = self._categorize_pattern(pattern['name'])
            if category not in categories:
                categories[category] = {'perfect': 0, 'good': 0, 'total': 0}
            categories[category]['total'] += 1
            if pattern in perfect:
                categories[category]['perfect'] += 1
            else:
                categories[category]['good'] += 1
        
        print(f"\n📋 BY CATEGORY:")
        for category, stats in categories.items():
            perfect_rate = stats['perfect'] / stats['total'] if stats['total'] > 0 else 0
            total_good = stats['perfect'] + stats['good']
            good_rate = total_good / stats['total'] if stats['total'] > 0 else 0
            status = "✅" if perfect_rate >= 0.8 else "🟡" if good_rate >= 0.8 else "❌"
            print(f"  {category}: {stats['perfect']}/{stats['total']} perfect ({perfect_rate:.1%}) {status}")
        
        print(f"\n🎯 CERTIFICATION ASSESSMENT:")
        print(f"  Required Success Rate: ≥{self.target_success_rate:.1%}")
        print(f"  Achieved Success Rate: {success_rate:.1%}")
        print(f"  Required Fidelity: ≥{self.min_fidelity:.1%}")
        print(f"  Perfect Fidelity Rate: {len(perfect)/total:.1%}")
        
        # Assessment finale
        if success_rate >= self.target_success_rate and parsing_rate >= 0.95:
            print(f"\n✅ CERTIFICATION: PASSED")
            print(f"🏆 QUALITY: EXCELLENT - Ready for fine-tuning")
            print(f"🎯 STATUS: CERTIFIED FOR PRODUCTION")
            print(f"🚀 DECISION: PROCEED with NG GODMODE v1")
        elif success_rate >= 0.80 and parsing_rate >= 0.90:
            print(f"\n🟡 CERTIFICATION: CONDITIONAL PASS")
            print(f"🎯 QUALITY: GOOD - Minor improvements recommended")
            print(f"📊 GAP: {self.target_success_rate - success_rate:.1%} improvement needed")
            print(f"🔧 DECISION: PROCEED with monitoring")
        else:
            print(f"\n❌ CERTIFICATION: FAILED")
            print(f"🔧 QUALITY: NEEDS WORK")
            print(f"📊 GAP: {self.target_success_rate - success_rate:.1%} improvement needed")
            print(f"❌ DECISION: FIX REQUIRED before fine-tuning")
        
        if failed:
            print(f"\n💥 PARSING FAILURES ({len(failed)}):")
            for pattern in failed:
                print(f"  • {pattern['name']}: {pattern['input']}")
                print(f"    Error: {pattern['error']}")
        
        if imperfect and any(r['fidelity'] < self.min_fidelity for r in imperfect):
            print(f"\n🔧 LOW FIDELITY PATTERNS:")
            for pattern in imperfect:
                if pattern['fidelity'] < self.min_fidelity:
                    print(f"  • {pattern['name']}: {pattern['fidelity']:.1%}")
        
        print("\n" + "="*80)
        
        return success_rate >= self.target_success_rate
    
    def _categorize_pattern(self, name: str) -> str:
        """Categorizza pattern per tipo."""
        if "Quantifier" in name or "Universal" in name or "Existential" in name:
            return "Quantifiers"
        elif "Implication" in name or "Conjunction" in name or "Disjunction" in name or "Negation" in name:
            return "Logic"
        elif "Set" in name or "Union" in name or "Intersection" in name or "Membership" in name or "Subset" in name:
            return "Set Theory"
        elif "Addition" in name or "Equality" in name or "Function" in name or "Inequality" in name:
            return "Mathematics Basic"
        elif "Integral" in name or "Summation" in name or "Product" in name or "Derivative" in name:
            return "Mathematics Advanced"
        elif "Grouped" in name or "Multi" in name:
            return "Complex Expressions"
        else:
            return "Other"


def main():
    """Main entry point."""
    certifier = FormalRoundtripCertifier()
    
    print("🚨 STARTING FORMAL ROUNDTRIP CERTIFICATION")
    print("🎯 OBJECTIVE: Perfect reversibility on official patterns")
    print("🔒 AUDIT LOCK: VERIFIED")
    
    perfect_count, imperfect_count, failed_count = certifier.certify_formal_roundtrip()
    total = perfect_count + imperfect_count + failed_count
    success_rate = perfect_count / total if total > 0 else 0
    
    if success_rate >= AUDIT_SUCCESS_RATE_REQUIRED:
        print(f"\n🎉 CERTIFICATION PASSED!")
        print(f"🏆 Perfect fidelity: {perfect_count}/{total} patterns")
        print(f"✅ Ready for NG GODMODE v1 fine-tuning")
        sys.exit(0)
    else:
        print(f"\n🔧 CERTIFICATION NEEDS IMPROVEMENT")
        print(f"📊 Perfect fidelity: {perfect_count}/{total} patterns ({success_rate:.1%})")
        print(f"🎯 Target: ≥{AUDIT_SUCCESS_RATE_REQUIRED:.1%}")
        sys.exit(1)


if __name__ == "__main__":
    main()
