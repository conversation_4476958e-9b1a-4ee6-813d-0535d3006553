#!/usr/bin/env python3
"""
NEUROGLYPH Tokenization Integrity Fix
Crea tokenizer che garantisce zero-splitting per tutti i simboli NEUROGLYPH

OBIETTIVO: 100% tokenization integrity senza perdere qualità dati
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Set
from tokenizers import Tokenizer, models, pre_tokenizers, decoders, trainers
from tokenizers.normalizers import NFD, StripAccents

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_neuroglyph_symbols(registry_path: str) -> Set[str]:
    """Carica simboli NEUROGLYPH dal registry."""
    logger.info(f"📖 Loading NEUROGLYPH symbols from registry...")
    
    try:
        with open(registry_path, 'r', encoding='utf-8') as f:
            registry = json.load(f)
        
        symbols = set(registry['symbols'])
        logger.info(f"   Loaded {len(symbols)} symbols from registry")
        return symbols
        
    except Exception as e:
        logger.error(f"Error loading registry: {e}")
        return set()


def create_neuroglyph_tokenizer(symbols: Set[str], output_path: str) -> bool:
    """Crea tokenizer NEUROGLYPH con zero-splitting garantito."""
    logger.info(f"🔧 Creating NEUROGLYPH tokenizer...")
    
    try:
        # STEP 1: Crea base tokenizer con BPE
        tokenizer = Tokenizer(models.BPE())
        
        # STEP 2: Configura normalizer (minimo per preservare simboli)
        tokenizer.normalizer = NFD()
        
        # STEP 3: Pre-tokenizer che preserva simboli NEUROGLYPH
        # Usa Whitespace + preserva simboli speciali
        tokenizer.pre_tokenizer = pre_tokenizers.Whitespace()
        
        # STEP 4: Decoder standard
        tokenizer.decoder = decoders.BPEDecoder()
        
        # STEP 5: Crea vocabolario base
        base_vocab = []
        
        # Aggiungi simboli speciali
        special_tokens = ["<pad>", "<unk>", "<s>", "</s>", "<mask>"]
        base_vocab.extend(special_tokens)
        
        # Aggiungi TUTTI i simboli NEUROGLYPH come token singoli
        for symbol in sorted(symbols):
            base_vocab.append(symbol)
        
        # Aggiungi caratteri ASCII comuni
        for i in range(32, 127):  # ASCII printable
            char = chr(i)
            if char not in base_vocab:
                base_vocab.append(char)
        
        # Aggiungi alcuni token comuni
        common_tokens = [
            "the", "and", "or", "not", "if", "then", "else",
            "true", "false", "null", "none", "zero", "one", "two"
        ]
        base_vocab.extend(common_tokens)
        
        logger.info(f"   Base vocabulary size: {len(base_vocab)}")
        
        # STEP 6: Crea trainer con vocabolario predefinito
        trainer = trainers.BpeTrainer(
            vocab_size=len(base_vocab) + 1000,  # Spazio per crescita
            min_frequency=1,
            special_tokens=special_tokens,
            show_progress=True
        )
        
        # STEP 7: Prepara testi di training (simboli + contesti)
        training_texts = []
        
        # Aggiungi ogni simbolo come testo separato (garantisce single-token)
        for symbol in symbols:
            training_texts.append(symbol)
            training_texts.append(f" {symbol} ")  # Con spazi
            training_texts.append(f"{symbol}{symbol}")  # Ripetuto
        
        # Aggiungi combinazioni comuni
        symbol_list = sorted(list(symbols))
        for i, sym1 in enumerate(symbol_list[:20]):  # Prime 20 per performance
            for sym2 in symbol_list[i+1:i+6]:  # Combinazioni limitate
                training_texts.append(f"{sym1} {sym2}")
                training_texts.append(f"{sym1}{sym2}")
        
        logger.info(f"   Training texts prepared: {len(training_texts)}")
        
        # STEP 8: Training del tokenizer
        logger.info(f"   Training tokenizer...")
        tokenizer.train_from_iterator(training_texts, trainer=trainer)
        
        # STEP 9: Aggiungi simboli NEUROGLYPH come token speciali
        # Questo garantisce che non vengano mai splittati
        neuroglyph_tokens = [(symbol, len(tokenizer.get_vocab()) + i) 
                           for i, symbol in enumerate(sorted(symbols))]
        
        tokenizer.add_tokens([symbol for symbol, _ in neuroglyph_tokens])
        
        # STEP 10: Salva tokenizer
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        tokenizer.save(str(output_file))
        
        logger.info(f"✅ NEUROGLYPH tokenizer created: {output_file}")
        logger.info(f"   Vocabulary size: {tokenizer.get_vocab_size()}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error creating tokenizer: {e}")
        return False


def test_tokenization_integrity(tokenizer_path: str, symbols: Set[str]) -> Dict:
    """Testa integrità tokenizzazione."""
    logger.info(f"🧪 Testing tokenization integrity...")
    
    try:
        # Carica tokenizer
        tokenizer = Tokenizer.from_file(tokenizer_path)
        
        results = {
            'total_symbols': len(symbols),
            'single_token_symbols': 0,
            'multi_token_symbols': 0,
            'zero_splitting_rate': 0.0,
            'failed_symbols': []
        }
        
        # Test ogni simbolo
        for symbol in symbols:
            try:
                # Encode simbolo
                encoding = tokenizer.encode(symbol)
                token_count = len(encoding.tokens)
                
                if token_count == 1:
                    results['single_token_symbols'] += 1
                else:
                    results['multi_token_symbols'] += 1
                    results['failed_symbols'].append({
                        'symbol': symbol,
                        'token_count': token_count,
                        'tokens': encoding.tokens
                    })
                
            except Exception as e:
                results['failed_symbols'].append({
                    'symbol': symbol,
                    'error': str(e)
                })
        
        # Calcola zero-splitting rate
        results['zero_splitting_rate'] = results['single_token_symbols'] / results['total_symbols']
        
        logger.info(f"✅ Tokenization integrity test completed")
        logger.info(f"   Single-token symbols: {results['single_token_symbols']}")
        logger.info(f"   Multi-token symbols: {results['multi_token_symbols']}")
        logger.info(f"   Zero-splitting rate: {results['zero_splitting_rate']:.2%}")
        
        return results
        
    except Exception as e:
        logger.error(f"Error testing tokenization: {e}")
        return {}


def test_roundtrip_fidelity(tokenizer_path: str, test_texts: List[str]) -> Dict:
    """Testa fidelity roundtrip encode/decode."""
    logger.info(f"🔄 Testing roundtrip fidelity...")
    
    try:
        tokenizer = Tokenizer.from_file(tokenizer_path)
        
        results = {
            'total_tests': len(test_texts),
            'perfect_roundtrips': 0,
            'fidelity_scores': [],
            'failed_roundtrips': []
        }
        
        for i, text in enumerate(test_texts):
            try:
                # Encode → Decode
                encoding = tokenizer.encode(text)
                decoded = tokenizer.decode(encoding.ids)
                
                # Calcola fidelity
                if text == decoded:
                    fidelity = 1.0
                    results['perfect_roundtrips'] += 1
                else:
                    # Calcola similarità
                    from difflib import SequenceMatcher
                    fidelity = SequenceMatcher(None, text, decoded).ratio()
                    
                    if fidelity < 0.95:  # Log solo problemi significativi
                        results['failed_roundtrips'].append({
                            'original': text,
                            'decoded': decoded,
                            'fidelity': fidelity
                        })
                
                results['fidelity_scores'].append(fidelity)
                
            except Exception as e:
                results['failed_roundtrips'].append({
                    'original': text,
                    'error': str(e)
                })
        
        # Calcola statistiche
        if results['fidelity_scores']:
            avg_fidelity = sum(results['fidelity_scores']) / len(results['fidelity_scores'])
        else:
            avg_fidelity = 0.0
        
        perfect_rate = results['perfect_roundtrips'] / results['total_tests']
        
        logger.info(f"✅ Roundtrip fidelity test completed")
        logger.info(f"   Perfect roundtrips: {results['perfect_roundtrips']}/{results['total_tests']}")
        logger.info(f"   Perfect rate: {perfect_rate:.2%}")
        logger.info(f"   Average fidelity: {avg_fidelity:.3f}")
        
        results['perfect_rate'] = perfect_rate
        results['average_fidelity'] = avg_fidelity
        
        return results
        
    except Exception as e:
        logger.error(f"Error testing roundtrip: {e}")
        return {}


def create_test_texts_from_dataset(dataset_path: str, sample_size: int = 100) -> List[str]:
    """Crea testi di test dal dataset."""
    logger.info(f"📝 Creating test texts from dataset...")
    
    test_texts = []
    
    try:
        with open(dataset_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip() and len(test_texts) < sample_size:
                    try:
                        example = json.loads(line.strip())
                        
                        # Aggiungi contenuto simbolico
                        prompt_symbolic = example.get('prompt_symbolic', '')
                        response_symbolic = example.get('response_symbolic', '')
                        
                        if prompt_symbolic:
                            test_texts.append(prompt_symbolic)
                        if response_symbolic:
                            test_texts.append(response_symbolic)
                        
                    except json.JSONDecodeError:
                        continue
        
        logger.info(f"   Created {len(test_texts)} test texts")
        return test_texts
        
    except Exception as e:
        logger.error(f"Error creating test texts: {e}")
        return []


def main():
    """Pipeline principale di fix tokenization integrity."""
    print("🔧 NEUROGLYPH Tokenization Integrity Fix")
    print("=" * 60)
    print("Creating zero-splitting tokenizer for NEUROGLYPH symbols")
    
    # Paths
    registry_path = "data/registry/neuroglyph_symbols.json"
    tokenizer_path = "models/tokenizer/neuroglyph_tokenizer.json"
    dataset_path = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl"
    
    if not Path(registry_path).exists():
        print(f"❌ Registry not found: {registry_path}")
        print("Please run fix_registry_compliance.py first")
        return 1
    
    # STEP 1: Carica simboli NEUROGLYPH
    print(f"\n📖 Step 1: Loading NEUROGLYPH symbols...")
    symbols = load_neuroglyph_symbols(registry_path)
    
    if not symbols:
        print(f"❌ Failed to load symbols")
        return 1
    
    print(f"   Loaded {len(symbols)} symbols")
    
    # STEP 2: Crea tokenizer
    print(f"\n🔧 Step 2: Creating NEUROGLYPH tokenizer...")
    success = create_neuroglyph_tokenizer(symbols, tokenizer_path)
    
    if not success:
        print(f"❌ Failed to create tokenizer")
        return 1
    
    # STEP 3: Test tokenization integrity
    print(f"\n🧪 Step 3: Testing tokenization integrity...")
    integrity_results = test_tokenization_integrity(tokenizer_path, symbols)
    
    if not integrity_results:
        print(f"❌ Failed to test tokenization")
        return 1
    
    # STEP 4: Test roundtrip fidelity
    print(f"\n🔄 Step 4: Testing roundtrip fidelity...")
    test_texts = create_test_texts_from_dataset(dataset_path, sample_size=100)
    fidelity_results = test_roundtrip_fidelity(tokenizer_path, test_texts)
    
    # STEP 5: Report risultati
    print(f"\n🎯 TOKENIZATION INTEGRITY FIX RESULTS")
    print("=" * 60)
    print(f"🔧 Tokenizer created: {tokenizer_path}")
    print(f"📊 Total symbols: {integrity_results['total_symbols']}")
    print(f"✅ Single-token symbols: {integrity_results['single_token_symbols']}")
    print(f"❌ Multi-token symbols: {integrity_results['multi_token_symbols']}")
    print(f"🎯 Zero-splitting rate: {integrity_results['zero_splitting_rate']:.2%}")
    
    if fidelity_results:
        print(f"🔄 Perfect roundtrips: {fidelity_results['perfect_rate']:.2%}")
        print(f"📈 Average fidelity: {fidelity_results['average_fidelity']:.3f}")
    
    # Verifica successo
    if integrity_results['zero_splitting_rate'] >= 1.0:
        print(f"\n🎉 100% TOKENIZATION INTEGRITY ACHIEVED!")
        return 0
    else:
        print(f"\n⚠️ TOKENIZATION INTEGRITY INCOMPLETE")
        print(f"Failed symbols: {len(integrity_results['failed_symbols'])}")
        for failure in integrity_results['failed_symbols'][:5]:  # Show first 5
            print(f"   {failure}")
        return 1


if __name__ == "__main__":
    exit(main())
