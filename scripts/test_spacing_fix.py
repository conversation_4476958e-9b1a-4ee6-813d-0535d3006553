#!/usr/bin/env python3
"""
NEUROGLYPH - Test Spacing Fix

Test rapido per verificare che i fix di spacing funzionino.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.formal_parser import create_formal_parser, ParseError


def test_spacing_fixes():
    """Test dei fix di spacing."""
    print("🔧 TESTING SPACING FIXES")
    print("="*50)
    
    tokenizer = create_conceptual_tokenizer()
    
    # Pattern che avevano problemi di spacing
    test_patterns = [
        ("f(x,y)", "Multi-Argument Function"),
        ("∀x ∃y: R(x,y)", "Nested Quantifiers"),
    ]
    
    all_perfect = True
    
    for pattern, description in test_patterns:
        try:
            tokens = tokenizer.tokenize(pattern)
            parser = create_formal_parser(tokens)
            ast = parser.parse()
            
            if ast and ast.root_concept:
                output = ast.to_neuroglyph()
                
                if output == pattern:
                    print(f"  ✅ {description}: PERFECT MATCH")
                    print(f"      Input:  '{pattern}'")
                    print(f"      Output: '{output}'")
                else:
                    print(f"  ❌ {description}: MISMATCH")
                    print(f"      Input:  '{pattern}'")
                    print(f"      Output: '{output}'")
                    all_perfect = False
            else:
                print(f"  💥 {description}: NO AST")
                all_perfect = False
        except Exception as e:
            print(f"  💥 {description}: ERROR - {e}")
            all_perfect = False
    
    return all_perfect


def main():
    """Main test runner."""
    print("🚀 NEUROGLYPH SPACING FIX TEST")
    
    success = test_spacing_fixes()
    
    if success:
        print(f"\n✅ ALL SPACING FIXES WORKING")
        print(f"🎯 Ready for final certification")
        return True
    else:
        print(f"\n❌ SPACING FIXES NEED MORE WORK")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
