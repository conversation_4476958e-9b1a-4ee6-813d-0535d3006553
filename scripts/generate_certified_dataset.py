#!/usr/bin/env python3
"""
NEUROGLYPH - Generate Certified Dataset

Genera dataset certificato per fine-tuning usando solo pattern
che passano il parser formale con fidelity ≥95%.

OBIETTIVO: Dataset epistemologicamente puro per NG GODMODE v1
"""

import sys
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.core.constants import verify_audit_constants_integrity
from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
from neuroglyph.conceptual.parser.formal_parser import create_formal_parser, ParseError


class CertifiedDatasetGenerator:
    """Generatore di dataset certificato."""
    
    def __init__(self):
        # Verifica integrità audit
        verify_audit_constants_integrity()
        
        self.tokenizer = create_conceptual_tokenizer()
        self.min_fidelity = 0.95
    
    def generate_certified_dataset(self):
        """Genera dataset certificato."""
        print("📦 NEUROGLYPH CERTIFIED DATASET GENERATION")
        print("="*80)
        print(f"PARSER: Formal grammar-based (100% certified)")
        print(f"STANDARD: Fidelity ≥{self.min_fidelity:.1%}, Perfect roundtrip")
        print(f"OBJECTIVE: Epistemologically pure dataset for NG GODMODE v1")
        print("="*80)
        
        # 25 PATTERN UFFICIALI CERTIFICATI
        certified_patterns = [
            # Quantificatori (5)
            "∀x: P(x)",
            "∃x: P(x)", 
            "∀x ∈ ℝ: P(x)",
            "∃x ∈ ℕ: P(x)",
            "∀x ∃y: R(x,y)",
            
            # Logica (6)
            "P ⇒ Q",
            "P ∧ Q",
            "P ∨ Q", 
            "¬P",
            "¬(P ∨ Q)",
            "P ∧ Q ⇒ R",
            
            # Set Theory (4)
            "A ∪ B",
            "A ∩ B",
            "x ∈ A",
            "A ⊆ B",
            
            # Matematica Base (3)
            "x + y",
            "f(x)",
            "x = y",
            
            # Matematica Avanzata (4)
            "∫ f(x) dx",
            "∑ xᵢ",
            "∏ xᵢ", 
            "∂f/∂x",
            
            # Espressioni Complesse (3)
            "(P ∨ Q)",
            "f(x,y)",
            "x ≠ y"
        ]
        
        print(f"\n📋 VALIDATING {len(certified_patterns)} CERTIFIED PATTERNS")
        
        validated_patterns = []
        failed_patterns = []
        
        for i, pattern in enumerate(certified_patterns, 1):
            print(f"[{i:2d}/25] Validating: {pattern}")
            
            result = self._validate_pattern(pattern)
            
            if result['valid'] and result['fidelity'] >= self.min_fidelity:
                validated_patterns.append({
                    'input': pattern,
                    'output': result['output'],
                    'fidelity': result['fidelity'],
                    'ast_type': result['ast_type']
                })
                print(f"    ✅ CERTIFIED - Fidelity: {result['fidelity']:.1%}")
            else:
                failed_patterns.append({
                    'pattern': pattern,
                    'error': result.get('error', 'Low fidelity'),
                    'fidelity': result.get('fidelity', 0.0)
                })
                print(f"    ❌ REJECTED - {result.get('error', 'Low fidelity')}")
        
        # Genera dataset JSON
        dataset = {
            'metadata': {
                'name': 'NEUROGLYPH Certified Dataset v1.0',
                'description': 'Epistemologically pure dataset for NG GODMODE v1 fine-tuning',
                'parser': 'Formal grammar-based',
                'certification_date': '2024-12-19',
                'total_patterns': len(certified_patterns),
                'validated_patterns': len(validated_patterns),
                'failed_patterns': len(failed_patterns),
                'validation_rate': len(validated_patterns) / len(certified_patterns),
                'min_fidelity_required': self.min_fidelity,
                'audit_lock_verified': True
            },
            'patterns': validated_patterns,
            'failed_patterns': failed_patterns
        }
        
        # Salva dataset
        output_file = project_root / "data" / "neuroglyph_certified_v1.json"
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)
        
        print(f"\n" + "="*80)
        print("📊 CERTIFIED DATASET GENERATION RESULTS")
        print("="*80)
        
        validation_rate = len(validated_patterns) / len(certified_patterns)
        
        print(f"📋 PATTERNS PROCESSED: {len(certified_patterns)}")
        print(f"✅ VALIDATED: {len(validated_patterns)}")
        print(f"❌ REJECTED: {len(failed_patterns)}")
        print(f"📊 VALIDATION RATE: {validation_rate:.1%}")
        
        print(f"\n💾 DATASET SAVED: {output_file}")
        print(f"📦 SIZE: {len(validated_patterns)} certified patterns")
        print(f"🎯 QUALITY: Epistemologically pure")
        print(f"🔒 AUDIT: Verified and locked")
        
        if validation_rate >= 0.95:
            print(f"\n✅ DATASET CERTIFICATION: PASSED")
            print(f"🏆 QUALITY: EXCELLENT")
            print(f"🚀 STATUS: READY FOR NG GODMODE v1 FINE-TUNING")
            return True
        else:
            print(f"\n❌ DATASET CERTIFICATION: FAILED")
            print(f"🔧 QUALITY: NEEDS IMPROVEMENT")
            print(f"📊 GAP: {0.95 - validation_rate:.1%} improvement needed")
            return False
    
    def _validate_pattern(self, pattern: str) -> dict:
        """Valida singolo pattern."""
        result = {
            'valid': False,
            'fidelity': 0.0,
            'output': None,
            'ast_type': None,
            'error': None
        }
        
        try:
            # Tokenize
            tokens = self.tokenizer.tokenize(pattern)
            
            # Parse with formal parser
            parser = create_formal_parser(tokens)
            ast = parser.parse()
            
            if ast and ast.root_concept:
                result['ast_type'] = type(ast.root_concept).__name__
                
                # Serialize back
                output = ast.to_neuroglyph()
                result['output'] = output
                
                # Calculate fidelity
                if output.strip() == pattern.strip():
                    result['fidelity'] = 1.0
                else:
                    # Use sequence similarity for near-matches
                    from difflib import SequenceMatcher
                    similarity = SequenceMatcher(None, pattern, output).ratio()
                    result['fidelity'] = similarity
                
                result['valid'] = True
            else:
                result['error'] = "No AST generated"
        
        except ParseError as e:
            result['error'] = f"Parse error: {str(e)[:50]}..."
        except Exception as e:
            result['error'] = f"Unexpected error: {type(e).__name__}"
        
        return result


def main():
    """Main entry point."""
    generator = CertifiedDatasetGenerator()
    
    print("🚨 STARTING CERTIFIED DATASET GENERATION")
    print("🎯 OBJECTIVE: Epistemologically pure dataset for NG GODMODE v1")
    print("🔒 AUDIT LOCK: VERIFIED")
    
    success = generator.generate_certified_dataset()
    
    if success:
        print(f"\n🎉 SUCCESS: Certified dataset generated!")
        print(f"📦 Ready for NG GODMODE v1 fine-tuning")
        sys.exit(0)
    else:
        print(f"\n💥 FAILURE: Dataset certification failed!")
        print(f"🔧 Manual intervention required")
        sys.exit(1)


if __name__ == "__main__":
    main()
