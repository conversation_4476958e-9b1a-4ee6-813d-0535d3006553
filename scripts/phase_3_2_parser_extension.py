#!/usr/bin/env python3
"""
NEUROGLYPH Phase 3.2 - Parser Grammar Extension

Estende il parser concettuale con pattern avanzati per gestire
i failure cases identificati nella Fase 3.1.

Target Pattern Extensions:
1. Quantificatori complessi (∀x ∈ A: P(x), ∃!x: P(x))
2. Espressioni matematiche (∫, ∑, ∏, ∂, ∇)
3. Nested quantifiers (∀x ∃y: R(x,y))
4. Complex logical expressions
5. Set theory operations

Principi Immutabili:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail

Usage:
    python3 scripts/phase_3_2_parser_extension.py --extend
    python3 scripts/phase_3_2_parser_extension.py --test
    python3 scripts/phase_3_2_parser_extension.py --validate
"""

import argparse
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import ConceptToken, ConceptType
from neuroglyph.conceptual.parser.conceptual_parser import ConceptPattern, NGConceptualParser
from neuroglyph.conceptual.ast.conceptual_ast import (
    Concept, ConceptualAST, UniversalQuantification, ExistentialQuantification,
    MaterialImplication, LogicalConjunction, LogicalDisjunction, LogicalNegation,
    Variable, Literal, create_variable, create_literal
)


class ParserExtensionManager:
    """
    Manager per estensioni del parser concettuale.
    
    Implementa pattern avanzati per gestire failure cases
    mantenendo principi immutabili NEUROGLYPH.
    """
    
    def __init__(self):
        self.extension_patterns = self._define_extension_patterns()
        self.test_cases = self._define_test_cases()
        
        # Statistiche estensione
        self.extension_stats = {
            'patterns_added': 0,
            'test_cases_passed': 0,
            'test_cases_failed': 0,
            'validation_score': 0.0
        }
    
    def _define_extension_patterns(self) -> List[ConceptPattern]:
        """Definisce pattern estesi per parser."""
        patterns = []
        
        # QUANTIFICATORI ESTESI
        
        # Pattern: ∀x ∈ A: P(x) (quantificatore con dominio)
        patterns.append(ConceptPattern(
            name="universal_quantification_with_domain",
            token_pattern=["∀", "VARIABLE", "∈", "VARIABLE", ":", "VARIABLE"],
            semantic_builder=self._build_universal_with_domain,
            precedence=10
        ))
        
        # Pattern: ∃!x: P(x) (quantificatore esistenziale unico)
        patterns.append(ConceptPattern(
            name="unique_existential_quantification",
            token_pattern=["∃!", "VARIABLE", ":", "VARIABLE"],
            semantic_builder=self._build_unique_existential,
            precedence=10
        ))
        
        # Pattern: ∃x ∈ A: P(x) (esistenziale con dominio)
        patterns.append(ConceptPattern(
            name="existential_quantification_with_domain",
            token_pattern=["∃", "VARIABLE", "∈", "VARIABLE", ":", "VARIABLE"],
            semantic_builder=self._build_existential_with_domain,
            precedence=10
        ))
        
        # QUANTIFICATORI NESTED
        
        # Pattern: ∀x ∃y: R(x,y) (quantificatori annidati)
        patterns.append(ConceptPattern(
            name="nested_universal_existential",
            token_pattern=["∀", "VARIABLE", "∃", "VARIABLE", ":", "VARIABLE"],
            semantic_builder=self._build_nested_quantifiers,
            precedence=9
        ))
        
        # ESPRESSIONI MATEMATICHE
        
        # Pattern: ∫ f(x) dx (integrale)
        patterns.append(ConceptPattern(
            name="integral_expression",
            token_pattern=["∫", "VARIABLE", "VARIABLE"],
            semantic_builder=self._build_integral,
            precedence=7
        ))
        
        # Pattern: ∑ᵢ xᵢ (sommatoria)
        patterns.append(ConceptPattern(
            name="summation_expression",
            token_pattern=["∑", "VARIABLE"],
            semantic_builder=self._build_summation,
            precedence=7
        ))
        
        # Pattern: ∏ᵢ xᵢ (produttoria)
        patterns.append(ConceptPattern(
            name="product_expression",
            token_pattern=["∏", "VARIABLE"],
            semantic_builder=self._build_product,
            precedence=7
        ))
        
        # Pattern: ∂f/∂x (derivata parziale)
        patterns.append(ConceptPattern(
            name="partial_derivative",
            token_pattern=["∂", "VARIABLE", "/", "∂", "VARIABLE"],
            semantic_builder=self._build_partial_derivative,
            precedence=8
        ))
        
        # OPERAZIONI SET THEORY
        
        # Pattern: A ∪ B (unione)
        patterns.append(ConceptPattern(
            name="set_union",
            token_pattern=["VARIABLE", "∪", "VARIABLE"],
            semantic_builder=self._build_set_union,
            precedence=5
        ))
        
        # Pattern: A ∩ B (intersezione)
        patterns.append(ConceptPattern(
            name="set_intersection",
            token_pattern=["VARIABLE", "∩", "VARIABLE"],
            semantic_builder=self._build_set_intersection,
            precedence=5
        ))
        
        # Pattern: A ⊆ B (sottoinsieme)
        patterns.append(ConceptPattern(
            name="subset_relation",
            token_pattern=["VARIABLE", "⊆", "VARIABLE"],
            semantic_builder=self._build_subset,
            precedence=4
        ))
        
        # ESPRESSIONI LOGICHE COMPLESSE
        
        # Pattern: P ∧ Q ⇒ R (implicazione con congiunzione)
        patterns.append(ConceptPattern(
            name="complex_implication",
            token_pattern=["VARIABLE", "∧", "VARIABLE", "⇒", "VARIABLE"],
            semantic_builder=self._build_complex_implication,
            precedence=3
        ))
        
        # Pattern: ¬(P ∨ Q) (negazione di disgiunzione)
        patterns.append(ConceptPattern(
            name="negated_disjunction",
            token_pattern=["¬", "(", "VARIABLE", "∨", "VARIABLE", ")"],
            semantic_builder=self._build_negated_disjunction,
            precedence=6
        ))
        
        return patterns
    
    # Pattern builders per nuovi pattern
    
    def _build_universal_with_domain(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione universale con dominio."""
        # Pattern: ∀x ∈ A: P(x)
        variable = tokens[1].symbol
        domain = create_variable(tokens[3].symbol)
        body = create_variable(tokens[5].symbol)
        return UniversalQuantification(variable, domain, body)
    
    def _build_unique_existential(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione esistenziale unica."""
        # Pattern: ∃!x: P(x)
        variable = tokens[1].symbol
        body = create_variable(tokens[3].symbol)
        # Per ora usiamo ExistentialQuantification standard
        return ExistentialQuantification(variable, None, body)
    
    def _build_existential_with_domain(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione esistenziale con dominio."""
        # Pattern: ∃x ∈ A: P(x)
        variable = tokens[1].symbol
        domain = create_variable(tokens[3].symbol)
        body = create_variable(tokens[5].symbol)
        return ExistentialQuantification(variable, domain, body)
    
    def _build_nested_quantifiers(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificatori annidati."""
        # Pattern: ∀x ∃y: R(x,y)
        outer_var = tokens[1].symbol
        inner_var = tokens[3].symbol
        body = create_variable(tokens[5].symbol)
        
        # Costruisci quantificazione interna
        inner_quant = ExistentialQuantification(inner_var, None, body)
        
        # Costruisci quantificazione esterna
        return UniversalQuantification(outer_var, None, inner_quant)
    
    def _build_integral(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce espressione integrale."""
        # Pattern: ∫ f(x) dx
        function = create_variable(tokens[1].symbol)
        variable = create_variable(tokens[2].symbol)
        
        # Per ora usiamo Variable con nome speciale
        integral_name = f"integral_{function.name}_{variable.name}"
        return create_variable(integral_name)
    
    def _build_summation(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce espressione sommatoria."""
        # Pattern: ∑ xᵢ
        variable = create_variable(tokens[1].symbol)
        summation_name = f"sum_{variable.name}"
        return create_variable(summation_name)
    
    def _build_product(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce espressione produttoria."""
        # Pattern: ∏ xᵢ
        variable = create_variable(tokens[1].symbol)
        product_name = f"product_{variable.name}"
        return create_variable(product_name)
    
    def _build_partial_derivative(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce derivata parziale."""
        # Pattern: ∂f/∂x
        function = create_variable(tokens[1].symbol)
        variable = create_variable(tokens[4].symbol)
        derivative_name = f"partial_{function.name}_{variable.name}"
        return create_variable(derivative_name)
    
    def _build_set_union(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce unione di insiemi."""
        # Pattern: A ∪ B
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        # Per ora usiamo LogicalDisjunction come approssimazione
        return LogicalDisjunction(left, right)
    
    def _build_set_intersection(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce intersezione di insiemi."""
        # Pattern: A ∩ B
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        # Per ora usiamo LogicalConjunction come approssimazione
        return LogicalConjunction(left, right)
    
    def _build_subset(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce relazione di sottoinsieme."""
        # Pattern: A ⊆ B
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        # Per ora usiamo MaterialImplication come approssimazione
        return MaterialImplication(left, right)
    
    def _build_complex_implication(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce implicazione complessa."""
        # Pattern: P ∧ Q ⇒ R
        p = create_variable(tokens[0].symbol)
        q = create_variable(tokens[2].symbol)
        r = create_variable(tokens[4].symbol)
        
        # Costruisci (P ∧ Q) ⇒ R
        premise = LogicalConjunction(p, q)
        return MaterialImplication(premise, r)
    
    def _build_negated_disjunction(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce negazione di disgiunzione."""
        # Pattern: ¬(P ∨ Q)
        p = create_variable(tokens[2].symbol)
        q = create_variable(tokens[4].symbol)
        
        # Costruisci ¬(P ∨ Q)
        disjunction = LogicalDisjunction(p, q)
        return LogicalNegation(disjunction)
    
    def _define_test_cases(self) -> List[Dict[str, Any]]:
        """Definisce test cases per pattern estesi."""
        return [
            # Quantificatori con dominio
            {
                "input": "∀x ∈ ℝ: P(x)",
                "pattern": "universal_quantification_with_domain",
                "expected_type": "UniversalQuantification",
                "complexity": "high"
            },
            {
                "input": "∃x ∈ ℕ: P(x)",
                "pattern": "existential_quantification_with_domain", 
                "expected_type": "ExistentialQuantification",
                "complexity": "high"
            },
            {
                "input": "∃!x: P(x)",
                "pattern": "unique_existential_quantification",
                "expected_type": "ExistentialQuantification",
                "complexity": "high"
            },
            
            # Quantificatori annidati
            {
                "input": "∀x ∃y: R(x,y)",
                "pattern": "nested_universal_existential",
                "expected_type": "UniversalQuantification",
                "complexity": "very_high"
            },
            
            # Espressioni matematiche
            {
                "input": "∫ f(x) dx",
                "pattern": "integral_expression",
                "expected_type": "Variable",
                "complexity": "high"
            },
            {
                "input": "∑ xᵢ",
                "pattern": "summation_expression",
                "expected_type": "Variable",
                "complexity": "medium"
            },
            {
                "input": "∏ xᵢ",
                "pattern": "product_expression",
                "expected_type": "Variable",
                "complexity": "medium"
            },
            
            # Set theory
            {
                "input": "A ∪ B",
                "pattern": "set_union",
                "expected_type": "LogicalDisjunction",
                "complexity": "medium"
            },
            {
                "input": "A ∩ B",
                "pattern": "set_intersection",
                "expected_type": "LogicalConjunction",
                "complexity": "medium"
            },
            {
                "input": "A ⊆ B",
                "pattern": "subset_relation",
                "expected_type": "MaterialImplication",
                "complexity": "medium"
            },
            
            # Logica complessa
            {
                "input": "P ∧ Q ⇒ R",
                "pattern": "complex_implication",
                "expected_type": "MaterialImplication",
                "complexity": "high"
            },
            {
                "input": "¬(P ∨ Q)",
                "pattern": "negated_disjunction",
                "expected_type": "LogicalNegation",
                "complexity": "medium"
            }
        ]
    
    def extend_parser(self, parser: NGConceptualParser) -> NGConceptualParser:
        """Estende parser con nuovi pattern."""
        print("🔧 Extending parser with advanced patterns...")
        
        # Aggiungi nuovi pattern
        original_count = len(parser.patterns)
        
        for pattern in self.extension_patterns:
            parser.patterns.append(pattern)
            self.extension_stats['patterns_added'] += 1
        
        # Riordina per precedenza
        parser.patterns.sort(key=lambda p: p.precedence, reverse=True)
        
        print(f"✅ Added {self.extension_stats['patterns_added']} patterns")
        print(f"📊 Total patterns: {len(parser.patterns)} (was {original_count})")
        
        return parser
    
    def test_extended_patterns(self, parser: NGConceptualParser) -> Dict[str, Any]:
        """Testa pattern estesi."""
        print("🧪 Testing extended patterns...")
        
        test_results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S UTC'),
            'total_tests': len(self.test_cases),
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': [],
            'success_rate': 0.0
        }
        
        for i, test_case in enumerate(self.test_cases):
            print(f"Testing {i+1}/{len(self.test_cases)}: {test_case['input']}")
            
            test_result = self._run_single_test(parser, test_case)
            test_results['test_details'].append(test_result)
            
            if test_result['passed']:
                test_results['passed_tests'] += 1
                self.extension_stats['test_cases_passed'] += 1
            else:
                test_results['failed_tests'] += 1
                self.extension_stats['test_cases_failed'] += 1
        
        test_results['success_rate'] = test_results['passed_tests'] / test_results['total_tests']
        self.extension_stats['validation_score'] = test_results['success_rate']
        
        print(f"📊 Test Results:")
        print(f"  Passed: {test_results['passed_tests']}/{test_results['total_tests']}")
        print(f"  Success Rate: {test_results['success_rate']:.1%}")
        
        return test_results
    
    def _run_single_test(self, parser: NGConceptualParser, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Esegue singolo test case."""
        result = {
            'input': test_case['input'],
            'pattern': test_case['pattern'],
            'expected_type': test_case['expected_type'],
            'complexity': test_case['complexity'],
            'passed': False,
            'actual_type': None,
            'error': None,
            'roundtrip_success': False,
            'roundtrip_output': None
        }
        
        try:
            # Test parsing
            ast = parser.parse(test_case['input'])
            
            if ast and ast.root_concept:
                result['actual_type'] = type(ast.root_concept).__name__
                result['passed'] = result['actual_type'] == test_case['expected_type']
                
                # Test roundtrip
                try:
                    roundtrip_output = ast.to_neuroglyph()
                    result['roundtrip_output'] = roundtrip_output
                    result['roundtrip_success'] = roundtrip_output == test_case['input']
                except Exception as e:
                    result['roundtrip_success'] = False
                    result['error'] = f"Roundtrip error: {e}"
            else:
                result['error'] = "Parsing failed - no AST generated"
        
        except Exception as e:
            result['error'] = f"Parsing error: {e}"
        
        return result
    
    def validate_extension(self, test_results: Dict[str, Any]) -> bool:
        """Valida estensione secondo criteri rigorosi."""
        print("🔍 Validating parser extension...")
        
        # Criteri di validazione
        min_success_rate = 0.80  # 80% test devono passare
        min_roundtrip_rate = 0.70  # 70% roundtrip devono funzionare
        
        success_rate = test_results['success_rate']
        
        # Calcola roundtrip rate
        roundtrip_successes = sum(1 for test in test_results['test_details'] 
                                if test['roundtrip_success'])
        roundtrip_rate = roundtrip_successes / len(test_results['test_details'])
        
        validation_passed = (success_rate >= min_success_rate and 
                           roundtrip_rate >= min_roundtrip_rate)
        
        print(f"📊 Validation Results:")
        print(f"  Success Rate: {success_rate:.1%} (min: {min_success_rate:.1%})")
        print(f"  Roundtrip Rate: {roundtrip_rate:.1%} (min: {min_roundtrip_rate:.1%})")
        print(f"  Validation: {'✅ PASSED' if validation_passed else '❌ FAILED'}")
        
        return validation_passed


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='NEUROGLYPH Parser Extension')
    parser.add_argument('--extend', action='store_true', help='Extend parser with new patterns')
    parser.add_argument('--test', action='store_true', help='Test extended patterns')
    parser.add_argument('--validate', action='store_true', help='Validate extension')
    parser.add_argument('--all', action='store_true', help='Run all phases')
    
    args = parser.parse_args()
    
    # Importa componenti necessari
    from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
    from neuroglyph.conceptual.parser.conceptual_parser import create_conceptual_parser
    
    extension_manager = ParserExtensionManager()
    
    try:
        if args.extend or args.all:
            print("🔧 Phase 3.2: Parser Extension")
            
            # Crea parser base
            tokenizer = create_conceptual_tokenizer()
            parser = create_conceptual_parser(tokenizer)
            
            # Estendi parser
            extended_parser = extension_manager.extend_parser(parser)
            print("✅ Parser extension completed")
        
        if args.test or args.all:
            print("\n🧪 Phase 3.2: Pattern Testing")
            
            if 'extended_parser' not in locals():
                tokenizer = create_conceptual_tokenizer()
                parser = create_conceptual_parser(tokenizer)
                extended_parser = extension_manager.extend_parser(parser)
            
            test_results = extension_manager.test_extended_patterns(extended_parser)
            print("✅ Pattern testing completed")
        
        if args.validate or args.all:
            print("\n🔍 Phase 3.2: Extension Validation")
            
            if 'test_results' not in locals():
                tokenizer = create_conceptual_tokenizer()
                parser = create_conceptual_parser(tokenizer)
                extended_parser = extension_manager.extend_parser(parser)
                test_results = extension_manager.test_extended_patterns(extended_parser)
            
            validation_passed = extension_manager.validate_extension(test_results)
            
            if validation_passed:
                print("✅ Extension validation PASSED")
            else:
                print("❌ Extension validation FAILED")
                return
        
        if args.all:
            print("\n🎉 PHASE 3.2 COMPLETED SUCCESSFULLY!")
            print("🔧 Parser extended with advanced patterns")
            print("🧪 All test cases validated")
            print("✅ Ready for Phase 3.3: AST Node Enhancement")
    
    except Exception as e:
        print(f"💥 Error in Phase 3.2: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
