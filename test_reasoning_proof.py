#!/usr/bin/env python3
"""
Test REALE per dimostrare che il reasoning funziona davvero.
Mostra tutti i logs, steps, proof tree, timing reali.
"""

import sys
import os
import time
import logging

# Aggiungi path per import
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

# Abilita logging dettagliato
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

try:
    from neuroglyph.logic.logic_engine import FormalLogicEngine
    from neuroglyph.logic.formula import *
    LOGIC_AVAILABLE = True
except ImportError as e:
    print(f"❌ Logic engine non disponibile: {e}")
    LOGIC_AVAILABLE = False


def test_real_reasoning_with_full_logs():
    """Test REALE con logs completi."""
    if not LOGIC_AVAILABLE:
        print("❌ Logic engine non disponibile")
        return False
    
    print("🔍 NEUROGLYPH REASONING - TEST REALE CON LOGS COMPLETI")
    print("=" * 80)
    
    # Crea engine con debug completo
    engine = FormalLogicEngine(max_depth=20, max_steps=100)
    
    print("\n📋 SCENARIO: Mathematical Property Chain")
    print("Dimostriamo che 4 è un numero reale attraverso una catena di proprietà matematiche")
    
    # Definisci variabili e costanti
    x = Variable("x")
    four = Constant("4")
    
    # Premises: catena di proprietà matematiche
    premises = [
        Implication(P("Even", x), P("DivisibleBy2", x)),      # Se x è pari → x è divisibile per 2
        Implication(P("DivisibleBy2", x), P("Integer", x)),   # Se x è divisibile per 2 → x è intero
        Implication(P("Integer", x), P("Rational", x)),       # Se x è intero → x è razionale
        Implication(P("Rational", x), P("Real", x)),          # Se x è razionale → x è reale
        P("Even", four)                                       # 4 è pari
    ]
    
    goal = P("Real", four)  # Dimostra che 4 è reale
    
    print(f"\n📝 PREMISES ({len(premises)} formule):")
    for i, premise in enumerate(premises, 1):
        print(f"   P{i}: {premise}")
    
    print(f"\n🎯 GOAL: {goal}")
    
    print(f"\n⏱️  STARTING REASONING...")
    start_time = time.perf_counter()
    
    # Esegui deduzione con logs completi
    result = engine.deduce(premises, goal)
    
    end_time = time.perf_counter()
    duration = end_time - start_time
    
    print(f"\n📊 RISULTATI REASONING:")
    print(f"   ✅ Success: {result.success}")
    print(f"   📈 Steps: {result.steps_count}")
    print(f"   🔢 Depth: {result.depth}")
    print(f"   ⏱️  Duration: {duration:.6f} seconds")
    print(f"   💾 Error: {result.error_message or 'None'}")
    
    if result.success and result.proof_tree:
        print(f"\n🌳 PROOF TREE COMPLETO ({len(result.proof_tree.steps)} steps):")
        print("-" * 80)
        
        for i, step in enumerate(result.proof_tree.steps, 1):
            print(f"Step {i:2d}: {step.formula}")
            print(f"         Rule: {step.rule.value}")
            print(f"         Justification: {step.justification}")
            if step.substitution:
                print(f"         Substitution: {step.substitution}")
            if step.premises:
                premise_ids = [p.step_id[:8] for p in step.premises]
                print(f"         Premises: {premise_ids}")
            print(f"         Timestamp: {step.timestamp:.6f}")
            print()
        
        print("🔍 REASONING CHAIN ANALYSIS:")
        print("-" * 40)
        
        # Analizza la catena di reasoning
        reasoning_chain = []
        for step in result.proof_tree.steps:
            if step.rule.value == "modus_ponens":
                reasoning_chain.append(step.formula)
        
        print(f"📈 Reasoning Chain ({len(reasoning_chain)} deductions):")
        for i, formula in enumerate(reasoning_chain, 1):
            print(f"   {i}. {formula}")
        
        print(f"\n✅ PROOF VALIDATION:")
        print(f"   - Proof tree valid: {result.proof_tree.is_valid}")
        print(f"   - Goal reached: {result.proof_tree.steps[-1].formula == goal}")
        print(f"   - All steps justified: {all(step.justification for step in result.proof_tree.steps)}")
        
        return True
    else:
        print(f"\n❌ REASONING FAILED:")
        print(f"   Error: {result.error_message}")
        return False


def test_simple_3_step_reasoning():
    """Test semplice a 3 step per confronto."""
    if not LOGIC_AVAILABLE:
        return False
    
    print("\n" + "=" * 80)
    print("🔍 TEST SEMPLICE: 3-Step Reasoning")
    print("=" * 80)
    
    engine = FormalLogicEngine(max_depth=10, max_steps=50)
    
    # Scenario semplice: A → B → C
    premises = [
        Implication(P("A"), P("B")),  # A → B
        Implication(P("B"), P("C")),  # B → C
        P("A")                        # A è vero
    ]
    goal = P("C")  # Dimostra C
    
    print(f"📝 PREMISES: {premises}")
    print(f"🎯 GOAL: {goal}")
    
    start_time = time.perf_counter()
    result = engine.deduce(premises, goal)
    duration = time.perf_counter() - start_time
    
    print(f"\n📊 RISULTATI:")
    print(f"   Success: {result.success}")
    print(f"   Steps: {result.steps_count}")
    print(f"   Depth: {result.depth}")
    print(f"   Duration: {duration:.6f}s")
    
    if result.success:
        print(f"\n🌳 PROOF STEPS:")
        for i, step in enumerate(result.proof_tree.steps, 1):
            print(f"   {i}. {step.formula} ({step.rule.value})")
    
    return result.success


def test_performance_scaling():
    """Test performance su catene di lunghezza crescente."""
    if not LOGIC_AVAILABLE:
        return False
    
    print("\n" + "=" * 80)
    print("🔍 PERFORMANCE SCALING TEST")
    print("=" * 80)
    
    engine = FormalLogicEngine(max_depth=30, max_steps=200)
    
    chain_lengths = [3, 5, 8, 12]
    results = []
    
    for length in chain_lengths:
        print(f"\n📏 Testing {length}-step chain...")
        
        # Crea catena P0 → P1 → P2 → ... → P{length}
        premises = []
        for i in range(length):
            premises.append(Implication(P(f"P{i}"), P(f"P{i+1}")))
        premises.append(P("P0"))  # Premessa iniziale
        
        goal = P(f"P{length}")
        
        start_time = time.perf_counter()
        result = engine.deduce(premises, goal)
        duration = time.perf_counter() - start_time
        
        results.append({
            'length': length,
            'success': result.success,
            'steps': result.steps_count,
            'depth': result.depth,
            'duration': duration
        })
        
        print(f"   ✅ Success: {result.success}")
        print(f"   📈 Steps: {result.steps_count}")
        print(f"   🔢 Depth: {result.depth}")
        print(f"   ⏱️  Duration: {duration:.6f}s")
    
    print(f"\n📊 PERFORMANCE SUMMARY:")
    print("Length | Success | Steps | Depth | Duration")
    print("-" * 45)
    for r in results:
        print(f"{r['length']:6d} | {str(r['success']):7s} | {r['steps']:5d} | {r['depth']:5d} | {r['duration']:8.6f}s")
    
    return all(r['success'] for r in results)


if __name__ == "__main__":
    print("🚀 NEUROGLYPH REASONING - PROVE REALI")
    print("Questo test mostra il reasoning reale con logs completi, timing, proof trees")
    print()
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Mathematical reasoning complesso
    if test_real_reasoning_with_full_logs():
        success_count += 1
        print("✅ Test 1 PASSED: Mathematical reasoning")
    else:
        print("❌ Test 1 FAILED: Mathematical reasoning")
    
    # Test 2: Reasoning semplice
    if test_simple_3_step_reasoning():
        success_count += 1
        print("✅ Test 2 PASSED: Simple 3-step reasoning")
    else:
        print("❌ Test 2 FAILED: Simple 3-step reasoning")
    
    # Test 3: Performance scaling
    if test_performance_scaling():
        success_count += 1
        print("✅ Test 3 PASSED: Performance scaling")
    else:
        print("❌ Test 3 FAILED: Performance scaling")
    
    print(f"\n🎯 RISULTATI FINALI: {success_count}/{total_tests} test passati")
    
    if success_count == total_tests:
        print("🎉 TUTTI I TEST PASSATI - REASONING FUNZIONA PERFETTAMENTE!")
    else:
        print("⚠️  Alcuni test falliti - reasoning parzialmente funzionante")
