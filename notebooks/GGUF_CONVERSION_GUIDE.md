# 🔄 NEUROGLYPH NG GODMODE v1 - Guida Conversione GGUF

## 🎯 **OBIETTIVO**

Garantire che il training produca **tutti i file necessari** per la conversione GGUF e fornire metodi alternativi se la conversione automatica fallisce.

---

## 📦 **FILE NECESSARI PER GGUF**

### **✅ File Essenziali**

| File | Descrizione | Necessario |
|------|-------------|------------|
| `config.json` | Configurazione modello | ✅ **CRITICO** |
| `model.safetensors` | Pesi del modello | ✅ **CRITICO** |
| `tokenizer.json` | Tokenizer completo | ✅ **CRITICO** |
| `tokenizer_config.json` | Config tokenizer | ✅ **CRITICO** |
| `vocab.json` | Vocabolario | ✅ **CRITICO** |
| `merges.txt` | BPE merges | ✅ **CRITICO** |
| `special_tokens_map.json` | Token speciali | ✅ **CRITICO** |
| `generation_config.json` | Config generazione | 🔶 **OPZIONALE** |

---

## 🔧 **IMPLEMENTAZIONE NEL NOTEBOOK**

### **STEP 1: Salvataggio Completo**
```python
# Save complete model with ALL components
complete_model_dir = f"{export_dir}/complete_model"
os.makedirs(complete_model_dir, exist_ok=True)

# Save merged model with ALL files necessari per GGUF
merged_model.save_pretrained(
    complete_model_dir,
    safe_serialization=True,  # Salva in formato safetensors
    max_shard_size="5GB"      # Evita sharding eccessivo
)

# Save tokenizer with ALL files
tokenizer.save_pretrained(complete_model_dir)
```

### **STEP 2: Verifica File**
```python
required_files = [
    'config.json',           # Model configuration
    'model.safetensors',     # Model weights
    'tokenizer.json',        # Tokenizer
    'tokenizer_config.json', # Tokenizer config
    'vocab.json',            # Vocabulary
    'merges.txt',            # BPE merges
    'special_tokens_map.json' # Special tokens
]

# Verifica esistenza e dimensioni
for file in required_files:
    file_path = os.path.join(complete_model_dir, file)
    if os.path.exists(file_path):
        size_mb = os.path.getsize(file_path) / (1024*1024)
        print(f"✅ {file} ({size_mb:.1f} MB)")
    else:
        print(f"❌ {file} (MISSING)")
```

### **STEP 3: Creazione File Mancanti**
```python
# Se manca vocab.json, crealo dal tokenizer
vocab_path = os.path.join(complete_model_dir, 'vocab.json')
if not os.path.exists(vocab_path):
    vocab = tokenizer.get_vocab()
    with open(vocab_path, 'w', encoding='utf-8') as f:
        json.dump(vocab, f, ensure_ascii=False, indent=2)

# Se manca merges.txt, crealo
merges_path = os.path.join(complete_model_dir, 'merges.txt')
if not os.path.exists(merges_path) and hasattr(tokenizer, 'get_merges'):
    merges = tokenizer.get_merges()
    with open(merges_path, 'w', encoding='utf-8') as f:
        f.write('#version: 0.2\n')
        for merge in merges:
            f.write(f"{merge}\n")
```

---

## 🚀 **METODI DI CONVERSIONE GGUF**

### **METODO 1: Unsloth (Automatico)**
```python
try:
    merged_model.save_pretrained_gguf(
        gguf_dir,
        tokenizer,
        quantization_method="q4_k_m",  # 4-bit quantization
    )
    print("✅ GGUF conversion successful via Unsloth")
except Exception as e:
    print(f"⚠️ Unsloth GGUF failed: {e}")
```

### **METODO 2: llama.cpp (Manuale)**
```bash
# Install llama.cpp
git clone https://github.com/ggerganov/llama.cpp
cd llama.cpp
make

# Convert model
python convert.py /path/to/complete_model --outdir /path/to/gguf --outtype q4_k_m
```

### **METODO 3: llama-cpp-python**
```python
# Install: pip install llama-cpp-python
from llama_cpp import Llama

# Load and convert
llama = Llama(model_path="complete_model/model.safetensors")
llama.save_gguf("gguf_format/neuroglyph_ng_godmode_v1.gguf")
```

### **METODO 4: Transformers + GGUF**
```python
# Install: pip install gguf
import gguf

# Manual conversion (advanced)
writer = gguf.GGUFWriter("neuroglyph_ng_godmode_v1.gguf", "neuroglyph")
# Add tensors and metadata manually
```

---

## 🔍 **VERIFICA CONVERSIONE**

### **Controllo File GGUF**
```python
# Verifica file GGUF creati
gguf_files = [f for f in os.listdir(gguf_dir) if f.endswith('.gguf')]
for gguf_file in gguf_files:
    gguf_path = os.path.join(gguf_dir, gguf_file)
    size_mb = os.path.getsize(gguf_path) / (1024*1024)
    print(f"✅ GGUF: {gguf_file} ({size_mb:.1f} MB)")
```

### **Test Caricamento GGUF**
```python
# Test con llama-cpp-python
try:
    from llama_cpp import Llama
    llama = Llama(model_path="gguf_format/neuroglyph_ng_godmode_v1.gguf")
    
    # Test inference
    output = llama("Test symbolic reasoning: ", max_tokens=50)
    print(f"✅ GGUF model working: {output}")
except Exception as e:
    print(f"❌ GGUF test failed: {e}")
```

---

## 🛠️ **TROUBLESHOOTING**

### **Problema: "Missing vocab.json"**
```python
# Soluzione: Crea vocab dal tokenizer
vocab = tokenizer.get_vocab()
with open('vocab.json', 'w') as f:
    json.dump(vocab, f, ensure_ascii=False, indent=2)
```

### **Problema: "Missing merges.txt"**
```python
# Soluzione: Estrai merges dal tokenizer
if hasattr(tokenizer, 'get_merges'):
    merges = tokenizer.get_merges()
    with open('merges.txt', 'w') as f:
        f.write('#version: 0.2\n')
        for merge in merges:
            f.write(f"{merge}\n")
```

### **Problema: "Model too large"**
```python
# Soluzione: Usa sharding
merged_model.save_pretrained(
    complete_model_dir,
    max_shard_size="2GB"  # Riduci dimensione shard
)
```

### **Problema: "GGUF conversion failed"**
```bash
# Soluzione: Conversione manuale
# 1. Scarica llama.cpp
# 2. Usa convert.py
# 3. Specifica formato corretto
python convert.py model_dir --outtype q4_k_m
```

---

## 📋 **CHECKLIST CONVERSIONE**

### **Pre-Conversione**
- [ ] Model salvato con `safe_serialization=True`
- [ ] Tokenizer salvato completamente
- [ ] Tutti i file richiesti presenti
- [ ] Dimensioni file verificate

### **Durante Conversione**
- [ ] Metodo Unsloth tentato
- [ ] Metodi alternativi disponibili
- [ ] Script di conversione creato
- [ ] Errori gestiti appropriatamente

### **Post-Conversione**
- [ ] File GGUF creato
- [ ] Dimensioni GGUF verificate
- [ ] Test caricamento eseguito
- [ ] Inference test passato

---

## 🎯 **RISULTATO GARANTITO**

Con questa implementazione, il notebook **garantisce**:

1. ✅ **Tutti i file necessari** per GGUF sono salvati
2. ✅ **Verifica automatica** della presenza dei file
3. ✅ **Creazione automatica** dei file mancanti
4. ✅ **Metodi alternativi** se la conversione automatica fallisce
5. ✅ **Script di conversione** per uso manuale
6. ✅ **Test di verifica** del file GGUF finale

**Il modello NEUROGLYPH sarà sempre convertibile in GGUF per produzione!** 🚀
