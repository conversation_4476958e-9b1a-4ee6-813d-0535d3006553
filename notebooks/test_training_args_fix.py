#!/usr/bin/env python3
"""
Test script per verificare il fix dei TrainingArguments
"""

def test_training_args_fix():
    """Test del fix per TrainingArguments."""
    
    print("🔧 Testing TrainingArguments fix...")
    
    try:
        from transformers import TrainingArguments
        
        # Test configurazione corretta (quella del fix)
        print("✅ Testing fixed configuration...")
        
        training_args = TrainingArguments(
            per_device_train_batch_size = 1,
            gradient_accumulation_steps = 4,
            warmup_steps = 5,
            num_train_epochs = 1,
            learning_rate = 2e-5,
            logging_steps = 1,
            optim = "adamw_8bit",
            weight_decay = 0.01,
            lr_scheduler_type = "cosine",
            seed = 42,
            output_dir = "./test_checkpoints",
            save_steps = 10,
            save_total_limit = 3,
            evaluation_strategy = "steps",  # FIX: Align with save strategy
            eval_steps = 10,
            load_best_model_at_end = True,
            metric_for_best_model = "eval_loss",  # FIX: Use eval_loss
            greater_is_better = False,
            report_to = None,
        )
        
        print(f"✅ TrainingArguments created successfully!")
        print(f"  Evaluation strategy: {training_args.evaluation_strategy}")
        print(f"  Save strategy: {training_args.save_strategy}")
        print(f"  Load best model: {training_args.load_best_model_at_end}")
        print(f"  Metric for best: {training_args.metric_for_best_model}")
        
        return True
        
    except ValueError as e:
        print(f"❌ TrainingArguments failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_dataset_split():
    """Test del dataset split per evaluation."""
    
    print("\n🔧 Testing dataset split...")
    
    try:
        from datasets import Dataset
        
        # Create mock training examples
        training_examples = [
            {'text': f'Example {i}: Mock training data'} 
            for i in range(20)
        ]
        
        # Create dataset
        dataset = Dataset.from_list(training_examples)
        
        # Split dataset (80/20)
        dataset = dataset.train_test_split(test_size=0.2, seed=42)
        train_dataset = dataset['train']
        eval_dataset = dataset['test']
        
        print(f"✅ Dataset split successfully!")
        print(f"  Total examples: {len(training_examples)}")
        print(f"  Training samples: {len(train_dataset)}")
        print(f"  Validation samples: {len(eval_dataset)}")
        print(f"  Split ratio: {len(eval_dataset)/len(training_examples):.1%} validation")
        
        return True
        
    except Exception as e:
        print(f"❌ Dataset split failed: {e}")
        return False

def test_problematic_config():
    """Test della configurazione problematica (per confronto)."""
    
    print("\n🔧 Testing problematic configuration...")
    
    try:
        from transformers import TrainingArguments
        
        # Configurazione problematica (quella che causava l'errore)
        training_args = TrainingArguments(
            per_device_train_batch_size = 1,
            output_dir = "./test_checkpoints",
            save_steps = 10,
            load_best_model_at_end = True,  # Problematico senza eval strategy
            metric_for_best_model = "train_loss",
            greater_is_better = False,
        )
        
        print(f"❌ Problematic config should have failed but didn't!")
        return False
        
    except ValueError as e:
        print(f"✅ Problematic config correctly failed: {str(e)[:100]}...")
        return True
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 NEUROGLYPH TrainingArguments Fix Test")
    print("=" * 50)
    
    # Test fixed configuration
    fix_ok = test_training_args_fix()
    
    # Test dataset split
    split_ok = test_dataset_split()
    
    # Test problematic configuration
    problem_ok = test_problematic_config()
    
    print("\n" + "=" * 50)
    if fix_ok and split_ok and problem_ok:
        print("🎉 ALL TESTS PASSED!")
        print("✅ TrainingArguments fix is working correctly")
        print("✅ Dataset split is working correctly")
        print("✅ Problematic config correctly fails")
    else:
        print("❌ Some tests failed")
        print("🔧 Manual intervention may be needed")
