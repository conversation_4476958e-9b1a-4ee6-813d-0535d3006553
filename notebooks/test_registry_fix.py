#!/usr/bin/env python3
"""
Test script per verificare il fix della struttura registry simboli
"""

import json
import os

def test_registry_structure():
    """Test della struttura del registry simboli."""
    
    # Path al registry
    registry_path = "../data/registry/neuroglyph_symbols.json"
    
    if not os.path.exists(registry_path):
        print(f"❌ Registry not found: {registry_path}")
        return False
    
    # Load registry
    with open(registry_path, 'r', encoding='utf-8') as f:
        symbols_registry = json.load(f)
    
    print("🔍 Testing registry structure...")
    
    # Test new structure handling
    if 'symbols' in symbols_registry:
        total_symbols = len(symbols_registry['symbols'])
        categories = len(symbols_registry.get('categories', {}))
        print(f"✅ New structure detected")
        print(f"  Total symbols: {total_symbols}")
        print(f"  Categories: {categories}")
        print(f"  Registry version: {symbols_registry.get('metadata', {}).get('version', 'unknown')}")
        
        # Test symbols list
        symbols_list = symbols_registry['symbols']
        print(f"  First 5 symbols: {symbols_list[:5]}")
        
        # Test categories
        categories_dict = symbols_registry.get('categories', {})
        print(f"  Category names: {list(categories_dict.keys())}")
        
        # Test frequency
        frequency_dict = symbols_registry.get('frequency', {})
        print(f"  Symbols with frequency: {len(frequency_dict)}")
        
        return True
        
    else:
        # Fallback for old structure
        total_symbols = len(symbols_registry)
        categories = len(set(s.get('category', 'unknown') for s in symbols_registry.values() if isinstance(s, dict)))
        print(f"⚠️ Old structure detected")
        print(f"  Total symbols: {total_symbols}")
        print(f"  Categories: {categories}")
        
        return True

def test_fixed_code():
    """Test del codice corretto."""
    
    # Simula il codice del notebook
    registry_path = "../data/registry/neuroglyph_symbols.json"
    
    with open(registry_path, 'r', encoding='utf-8') as f:
        symbols_registry = json.load(f)
    
    print("\n🧪 Testing fixed code...")
    
    try:
        # Codice corretto dal notebook
        if 'symbols' in symbols_registry:
            total_symbols = len(symbols_registry['symbols'])
            categories = len(symbols_registry.get('categories', {}))
            print(f"✅ Total symbols: {total_symbols}")
            print(f"✅ Categories: {categories}")
            print(f"✅ Registry version: {symbols_registry.get('metadata', {}).get('version', 'unknown')}")
        else:
            # Fallback for old structure
            total_symbols = len(symbols_registry)
            categories = len(set(s.get('category', 'unknown') for s in symbols_registry.values() if isinstance(s, dict)))
            print(f"✅ Total symbols: {total_symbols}")
            print(f"✅ Categories: {categories}")
        
        print("✅ Fixed code works correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Fixed code failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 NEUROGLYPH Registry Structure Fix Test")
    print("=" * 50)
    
    # Test registry structure
    structure_ok = test_registry_structure()
    
    # Test fixed code
    code_ok = test_fixed_code()
    
    print("\n" + "=" * 50)
    if structure_ok and code_ok:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Registry structure fix is working correctly")
    else:
        print("❌ Some tests failed")
        print("🔧 Manual intervention may be needed")
