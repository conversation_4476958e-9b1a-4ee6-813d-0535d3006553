#!/usr/bin/env python3
"""
Test script per verificare il fix della struttura Drive
"""

import sys
import os
import shutil

def create_mock_drive_structure():
    """Crea una struttura mock simile a quella su Drive."""
    
    print("🔧 Creating mock Drive structure...")
    
    # Create mock directory
    mock_drive = "./mock_drive_neuroglyph"
    if os.path.exists(mock_drive):
        shutil.rmtree(mock_drive)
    os.makedirs(mock_drive)
    
    # Copy files to root (simulating Drive structure)
    source_files = [
        "../neuroglyph/conceptual/tokenizer/conceptual_tokenizer.py",
        "../neuroglyph/conceptual/parser/formal_parser.py",
        "../neuroglyph/conceptual/ast/conceptual_ast.py",
        "../neuroglyph/core/constants.py"
    ]
    
    for source_file in source_files:
        if os.path.exists(source_file):
            filename = os.path.basename(source_file)
            dest_file = os.path.join(mock_drive, filename)
            shutil.copy2(source_file, dest_file)
            print(f"✅ Copied: {filename}")
        else:
            print(f"❌ Source not found: {source_file}")
    
    return mock_drive

def test_drive_structure_import(neuroglyph_root):
    """Test dell'import con struttura Drive."""
    
    print(f"\n🔧 Testing Drive structure import...")
    print(f"📁 NEUROGLYPH root: {neuroglyph_root}")
    
    # Check file structure first
    tokenizer_file = os.path.join(neuroglyph_root, 'conceptual_tokenizer.py')
    parser_file = os.path.join(neuroglyph_root, 'formal_parser.py')
    
    print(f"🔍 Checking NEUROGLYPH files:")
    print(f"  Tokenizer: {os.path.exists(tokenizer_file)} - {tokenizer_file}")
    print(f"  Parser: {os.path.exists(parser_file)} - {parser_file}")
    
    if os.path.exists(tokenizer_file) and os.path.exists(parser_file):
        # Files exist in root - use direct import from Drive structure
        try:
            # Add neuroglyph root to path
            if neuroglyph_root not in sys.path:
                sys.path.insert(0, neuroglyph_root)
            
            # Import directly from root files
            import conceptual_tokenizer
            import formal_parser
            
            print("✅ Modules imported successfully!")
            
            # Test factory functions exist
            if hasattr(conceptual_tokenizer, 'create_conceptual_tokenizer'):
                print("✅ create_conceptual_tokenizer found")
                
                # Test tokenizer creation
                try:
                    tokenizer = conceptual_tokenizer.create_conceptual_tokenizer()
                    print(f"✅ Tokenizer created: {type(tokenizer)}")
                    
                    # Test basic tokenization
                    test_text = "∀ x ∈ ℝ"
                    tokens = tokenizer.tokenize(test_text)
                    print(f"✅ Test tokenization: '{test_text}' → {len(tokens)} tokens")
                    
                except Exception as e:
                    print(f"❌ Tokenizer creation failed: {e}")
                    return False
            else:
                print("❌ create_conceptual_tokenizer not found")
                return False
            
            if hasattr(formal_parser, 'create_formal_parser'):
                print("✅ create_formal_parser found")
            else:
                print("❌ create_formal_parser not found")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Drive structure import failed: {e}")
            return False
    else:
        print("❌ Required files not found in Drive structure")
        return False

def test_symbolic_validator_mock():
    """Test del SymbolicValidator con struttura mock."""
    
    print(f"\n🔧 Testing SymbolicValidator with mock structure...")
    
    # Create mock Drive structure
    mock_drive = create_mock_drive_structure()
    
    try:
        # Test import with mock structure
        success = test_drive_structure_import(mock_drive)
        
        if success:
            print("✅ Mock Drive structure test passed!")
        else:
            print("❌ Mock Drive structure test failed!")
        
        return success
        
    finally:
        # Cleanup
        if os.path.exists(mock_drive):
            shutil.rmtree(mock_drive)
            print(f"🧹 Cleaned up: {mock_drive}")

def test_file_detection():
    """Test della detection dei file."""
    
    print(f"\n🔧 Testing file detection logic...")
    
    # Test paths
    test_paths = [
        "./mock_drive_neuroglyph",  # Mock Drive structure
        "..",                       # Local structure
        "/content/drive/MyDrive/NEUROGLYPH"  # Colab structure
    ]
    
    for path in test_paths:
        print(f"\n📁 Testing path: {path}")
        
        if not os.path.exists(path):
            print(f"  ❌ Path does not exist")
            continue
        
        # Check for Drive structure (files in root)
        tokenizer_file = os.path.join(path, 'conceptual_tokenizer.py')
        parser_file = os.path.join(path, 'formal_parser.py')
        
        drive_structure = os.path.exists(tokenizer_file) and os.path.exists(parser_file)
        
        # Check for standard structure
        standard_tokenizer = os.path.join(path, 'neuroglyph', 'conceptual', 'tokenizer', 'conceptual_tokenizer.py')
        standard_parser = os.path.join(path, 'neuroglyph', 'conceptual', 'parser', 'formal_parser.py')
        
        standard_structure = os.path.exists(standard_tokenizer) and os.path.exists(standard_parser)
        
        print(f"  Drive structure: {drive_structure}")
        print(f"  Standard structure: {standard_structure}")
        
        if drive_structure:
            print(f"  → Use Drive import method")
        elif standard_structure:
            print(f"  → Use standard import method")
        else:
            print(f"  → No compatible structure found")

if __name__ == "__main__":
    print("🔧 NEUROGLYPH Drive Structure Fix Test")
    print("=" * 50)
    
    # Test file detection
    test_file_detection()
    
    # Test with mock Drive structure
    mock_success = test_symbolic_validator_mock()
    
    print("\n" + "=" * 50)
    if mock_success:
        print("🎉 DRIVE STRUCTURE FIX WORKING!")
        print("✅ Can import from files in root directory")
        print("✅ Tokenizer and parser work correctly")
        print("🚀 Ready for Google Drive deployment")
    else:
        print("❌ Drive structure fix needs adjustment")
        print("🔧 Check file paths and import logic")
