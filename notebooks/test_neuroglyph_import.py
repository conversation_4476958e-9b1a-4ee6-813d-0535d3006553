#!/usr/bin/env python3
"""
Test script per verificare l'import del modulo NEUROGLYPH
"""

import sys
import os

def test_neuroglyph_import():
    """Test dell'import del modulo NEUROGLYPH."""
    
    print("🔧 Testing NEUROGLYPH module import...")
    
    # Setup paths (simula il notebook)
    neuroglyph_root = '/content/drive/MyDrive/NEUROGLYPH'  # Path Colab
    if not os.path.exists(neuroglyph_root):
        neuroglyph_root = '..'  # Path locale
    
    print(f"📁 NEUROGLYPH root: {neuroglyph_root}")
    
    # Add paths
    sys.path.append(neuroglyph_root)
    sys.path.append(os.path.join(neuroglyph_root, 'neuroglyph'))
    sys.path.append('/content')
    
    # Verify files exist
    tokenizer_file = os.path.join(neuroglyph_root, 'neuroglyph', 'conceptual', 'tokenizer', 'conceptual_tokenizer.py')
    parser_file = os.path.join(neuroglyph_root, 'neuroglyph', 'conceptual', 'parser', 'formal_parser.py')
    
    print(f"🔍 Checking files:")
    print(f"  Tokenizer: {os.path.exists(tokenizer_file)} - {tokenizer_file}")
    print(f"  Parser: {os.path.exists(parser_file)} - {parser_file}")
    
    # Test imports (multiple attempts like in notebook)
    success = False
    
    # Attempt 1: Direct import
    try:
        print("\n🔄 Attempt 1: Direct import...")
        from neuroglyph.conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
        from neuroglyph.conceptual.parser.formal_parser import create_formal_parser
        print("✅ Direct import successful!")
        success = True
    except Exception as e1:
        print(f"❌ Direct import failed: {e1}")
        
        # Attempt 2: Alternative path
        try:
            print("\n🔄 Attempt 2: Alternative path...")
            sys.path.insert(0, neuroglyph_root)
            from conceptual.tokenizer.conceptual_tokenizer import create_conceptual_tokenizer
            from conceptual.parser.formal_parser import create_formal_parser
            print("✅ Alternative path successful!")
            success = True
        except Exception as e2:
            print(f"❌ Alternative path failed: {e2}")
            
            # Attempt 3: Manual path construction
            try:
                print("\n🔄 Attempt 3: Manual path...")
                tokenizer_path = os.path.join(neuroglyph_root, 'neuroglyph', 'conceptual', 'tokenizer')
                parser_path = os.path.join(neuroglyph_root, 'neuroglyph', 'conceptual', 'parser')
                sys.path.extend([tokenizer_path, parser_path])
                
                from conceptual_tokenizer import create_conceptual_tokenizer
                from formal_parser import create_formal_parser
                print("✅ Manual path successful!")
                success = True
            except Exception as e3:
                print(f"❌ Manual path failed: {e3}")
                print(f"\n❌ All import attempts failed:")
                print(f"  Direct: {str(e1)[:50]}...")
                print(f"  Alternative: {str(e2)[:50]}...")
                print(f"  Manual: {str(e3)[:50]}...")
    
    if success:
        # Test tokenizer creation
        try:
            print("\n🧪 Testing tokenizer creation...")
            tokenizer = create_conceptual_tokenizer()
            print(f"✅ Tokenizer created successfully!")
            print(f"  Type: {type(tokenizer)}")
            
            # Test basic tokenization
            test_text = "∀ x ∈ ℝ"
            tokens = tokenizer.tokenize(test_text)
            print(f"  Test tokenization: '{test_text}' → {tokens}")
            
        except Exception as e:
            print(f"❌ Tokenizer test failed: {e}")
            success = False
    
    return success

def test_file_structure():
    """Test della struttura file NEUROGLYPH."""
    
    print("\n🔧 Testing NEUROGLYPH file structure...")
    
    neuroglyph_root = '..'  # Path locale
    
    required_files = [
        'neuroglyph/__init__.py',
        'neuroglyph/conceptual/__init__.py',
        'neuroglyph/conceptual/tokenizer/__init__.py',
        'neuroglyph/conceptual/tokenizer/conceptual_tokenizer.py',
        'neuroglyph/conceptual/parser/__init__.py',
        'neuroglyph/conceptual/parser/formal_parser.py',
        'neuroglyph/conceptual/ast/__init__.py',
        'neuroglyph/conceptual/ast/conceptual_ast.py',
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(neuroglyph_root, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (MISSING)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ Missing files: {len(missing_files)}")
        return False
    else:
        print(f"\n✅ All required files present!")
        return True

if __name__ == "__main__":
    print("🔧 NEUROGLYPH Import Test")
    print("=" * 50)
    
    # Test file structure
    structure_ok = test_file_structure()
    
    # Test imports
    import_ok = test_neuroglyph_import()
    
    print("\n" + "=" * 50)
    if structure_ok and import_ok:
        print("🎉 ALL TESTS PASSED!")
        print("✅ NEUROGLYPH module is importable")
        print("✅ Tokenizer and parser work correctly")
    else:
        print("❌ Some tests failed")
        if not structure_ok:
            print("🔧 Check file structure")
        if not import_ok:
            print("🔧 Check import paths")
