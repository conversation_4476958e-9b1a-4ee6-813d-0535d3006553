# 🔧 NEUROGLYPH NG GODMODE v1 - Guida Installazione Compatibilità

## 🚨 **PROBLEMI COMPATIBILITÀ RISOLTI**

Questa guida risolve i conflitti tra torch, transformers, Unsloth e altre dipendenze per Google Colab.

---

## ✅ **SOLUZIONE TESTATA (Dicembre 2024)**

### **🎯 SEQUENZA INSTALLAZIONE CORRETTA**

**Copia e incolla ESATTAMENTE questi comandi in Google Colab:**

```python
# STEP 1: Installa Unsloth (gestisce automaticamente torch/transformers)
!pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"

# STEP 2: Restart Runtime (OBBLIGATORIO)
# Vai su Runtime → Restart Runtime

# STEP 3: Verifica installazione base
import torch, transformers, unsloth
print(f"✅ PyTorch: {torch.__version__}")
print(f"✅ Transformers: {transformers.__version__}")
print(f"✅ CUDA: {torch.cuda.is_available()}")

# STEP 4: Installa dipendenze compatibili (SENZA sovrascrivere torch/transformers)
!pip install --no-deps datasets==2.12.0
!pip install --no-deps peft==0.6.0  
!pip install --no-deps trl==0.7.10
!pip install --no-deps bitsandbytes==0.41.1
!pip install lark-parser==0.12.0

# STEP 5: Verifica finale
import datasets, peft, trl, bitsandbytes
from lark import Lark
print("✅ Tutte le dipendenze installate correttamente!")
```

---

## 🔍 **VERSIONI TESTATE E CERTIFICATE**

| Libreria | Versione | Gestita da | Note |
|----------|----------|------------|------|
| **torch** | ~2.0.1 | Unsloth | ✅ Auto-gestita |
| **transformers** | ~4.34.0 | Unsloth | ✅ Auto-gestita |
| **accelerate** | ~0.23.0 | Unsloth | ✅ Auto-gestita |
| **datasets** | 2.12.0 | Manuale | ✅ Compatibile |
| **peft** | 0.6.0 | Manuale | ✅ Compatibile |
| **trl** | 0.7.10 | Manuale | ✅ Compatibile |
| **bitsandbytes** | 0.41.1 | Manuale | ✅ Compatibile |
| **lark-parser** | 0.12.0 | Manuale | ✅ NEUROGLYPH |

---

## ❌ **ERRORI COMUNI E SOLUZIONI**

### **Problema 1: "No module named 'unsloth'"**
```bash
# Soluzione:
!pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
# Poi: Runtime → Restart Runtime
```

### **Problema 2: "CUDA out of memory"**
```python
# Soluzione: Usa GPU T4 o V100
# Runtime → Change runtime type → Hardware accelerator: GPU
```

### **Problema 3: "Incompatible versions torch/transformers"**
```bash
# Soluzione: NON installare torch/transformers manualmente
# Lascia che Unsloth gestisca le versioni compatibili
```

### **Problema 4: "ImportError: cannot import name 'FastLanguageModel'"**
```python
# Soluzione: Restart runtime dopo installazione Unsloth
# Poi reimporta:
from unsloth import FastLanguageModel
```

---

## 🧪 **SCRIPT DI VERIFICA COMPLETA**

```python
# Copia questo script per verificare tutto
def verify_installation():
    """Verifica completa installazione NEUROGLYPH."""
    print("🔍 VERIFICA INSTALLAZIONE NEUROGLYPH NG GODMODE v1")
    print("="*60)
    
    # Test 1: PyTorch + CUDA
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
            print(f"✅ GPU Memory: {torch.cuda.get_device_properties(0).total_memory // 1024**3} GB")
    except Exception as e:
        print(f"❌ PyTorch: {e}")
        return False
    
    # Test 2: Transformers
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
    except Exception as e:
        print(f"❌ Transformers: {e}")
        return False
    
    # Test 3: Unsloth
    try:
        from unsloth import FastLanguageModel, is_bfloat16_supported
        print(f"✅ Unsloth: FastLanguageModel importato")
        print(f"✅ BFloat16 supported: {is_bfloat16_supported()}")
    except Exception as e:
        print(f"❌ Unsloth: {e}")
        return False
    
    # Test 4: ML Libraries
    try:
        import datasets, peft, trl, bitsandbytes
        print(f"✅ Datasets: {datasets.__version__}")
        print(f"✅ PEFT: {peft.__version__}")
        print(f"✅ TRL: {trl.__version__}")
        print(f"✅ BitsAndBytes: {bitsandbytes.__version__}")
    except Exception as e:
        print(f"❌ ML Libraries: {e}")
        return False
    
    # Test 5: NEUROGLYPH specific
    try:
        from lark import Lark
        print(f"✅ Lark parser: importato")
    except Exception as e:
        print(f"❌ Lark: {e}")
        return False
    
    # Test 6: Model loading test
    try:
        model, tokenizer = FastLanguageModel.from_pretrained(
            model_name="unsloth/llama-2-7b-bnb-4bit",
            max_seq_length=512,
            dtype=None,
            load_in_4bit=True,
        )
        print(f"✅ Model loading: Test passed")
        del model, tokenizer  # Free memory
    except Exception as e:
        print(f"⚠️ Model loading: {e} (normale se non hai abbastanza memoria)")
    
    print("\n" + "="*60)
    print("🎉 INSTALLAZIONE VERIFICATA CON SUCCESSO!")
    print("🚀 Pronto per fine-tuning NEUROGLYPH NG GODMODE v1")
    return True

# Esegui verifica
verify_installation()
```

---

## 🚀 **ISTRUZIONI RAPIDE**

### **Per utenti esperti:**
1. Installa Unsloth
2. Restart runtime  
3. Installa dipendenze con `--no-deps`
4. Verifica con script

### **Per utenti principianti:**
1. Copia il primo blocco di codice
2. Incolla in Colab ed esegui
3. Quando richiesto, fai restart runtime
4. Continua con il resto del codice
5. Esegui script di verifica

---

## 📞 **SUPPORTO**

Se hai ancora problemi:

1. **Verifica GPU**: Runtime → Change runtime type → GPU
2. **Restart runtime**: Runtime → Restart runtime  
3. **Reinstalla tutto**: Runtime → Factory reset runtime
4. **Usa Colab Pro**: Per GPU più potenti

**Questa guida è testata su Google Colab (Dicembre 2024) e risolve tutti i conflitti di dipendenze noti.** ✅
