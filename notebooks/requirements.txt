# NEUROGLYPH NG GODMODE v1 - COMPATIB<PERSON>ITÀ COLAB + UNSLOTH
# Versioni TESTATE e CERTIFICATE per Google Colab (Dicembre 2024)

# ⚠️ IMPORTANTE: Installare in questo ORDINE ESATTO

# 1. PRIMA: Unsloth (installa automaticamente torch/transformers compatibili)
# !pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"

# 2. DOPO: Dipendenze compatibili (NON sovrascrivere torch/transformers!)
# Core ML frameworks - LASCIATE GESTIRE DA UNSLOTH
# torch - gestito da Unsloth
# transformers - gestito da Unsloth
# accelerate - gestito da Unsloth

# Dipendenze sicure (non conflittuali)
datasets>=2.10.0,<2.15.0
peft>=0.5.0,<0.8.0
trl>=0.7.0,<0.9.0

# Quantization (compatibili)
bitsandbytes>=0.41.0,<0.42.0
xformers<0.0.27

# NEUROGLYPH specific
lark-parser==0.12.0
numpy>=1.21.0,<1.25.0

# Utilities (sicure)
psutil>=5.8.0
packaging>=20.0

# Jupyter/Colab (pre-installate)
# ipywidgets - già in Colab
# jupyter - già in Colab

# Optional (solo se necessario)
# tensorboard>=2.10.0
# wandb>=0.15.0

# ISTRUZIONI INSTALLAZIONE:
# 1. Esegui SOLO: !pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
# 2. Poi: !pip install --no-deps datasets peft trl lark-parser
# 3. Restart runtime
# 4. Verifica: import unsloth, torch, transformers
